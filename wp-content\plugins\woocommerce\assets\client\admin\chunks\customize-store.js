"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[4040],{79745:(e,t,o)=>{o.d(t,{$k:()=>s,Q_:()=>a,eN:()=>r,x6:()=>n});const r=["image"],s=60,n=20,a=200},30847:(e,t,o)=>{o.r(t),o.d(t,{CustomizeStoreController:()=>ss,customizeStoreStateMachineActions:()=>ts,customizeStoreStateMachineDefinition:()=>rs,customizeStoreStateMachineServices:()=>os,default:()=>ns,machineActions:()=>es});var r={};o.r(r),o.d(r,{assignActiveTheme:()=>F,assignActiveThemeHasMods:()=>Z,assignCustomizeStoreCompleted:()=>Y,assignFetchIntroDataError:()=>R,assignFlags:()=>V,assignIsFontLibraryAvailable:()=>G,assignIsPTKPatternsAPIAvailable:()=>H,assignNoAIFlowError:()=>W,assignTaskReferral:()=>B,assignThemeData:()=>O,recordTracksBrowseAllThemesClicked:()=>Q,recordTracksDesignWithoutAIClicked:()=>P,recordTracksThemeSelected:()=>U});var s={};o.r(s),o.d(s,{fetchAiStatus:()=>q,fetchCustomizeStoreCompleted:()=>$,fetchIntroData:()=>ee,fetchThemeCards:()=>X,setFlags:()=>te});var n={};o.r(n),o.d(n,{fetchSurveyCompletedOption:()=>Er});var a=o(3582),i=o(11673),c=o(86087),l=o(3472),m=o(55739),u=o(96476),d=o(40314),g=o(47143),p=o(98846),y=o(15703),h=o(92279),w=o(85816),M=o(24060),k=o(27723),b=o(46445),f=o(36849),j=o(56427),N=o(14908),I=o(64155),_=o(29491),x=o(39793);const D=({colorPalettes:e,totalPalettes:t})=>{const o=t<=4,r=(0,_.useInstanceId)(D,"color-palettes-description");return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)("ul",{className:"theme-card__color-palettes","aria-label":(0,k.__)("Color palettes","woocommerce"),"aria-describedby":r,children:[e.map((e=>(0,x.jsx)("li",{"aria-label":e.title,style:{background:"linear-gradient(to right, "+e.primary+" 0px, "+e.primary+" 50%, "+e.secondary+" 50%, "+e.secondary+" 100%)"}},e.title))),o?null:(0,x.jsxs)("li",{"aria-hidden":"true",className:"more_palettes",children:["+",t-4]})]}),o?null:(0,x.jsx)("p",{id:r,className:"theme-card__color-palettes-description",children:(0,k.sprintf)((0,k.__)("There are a total of %d color palettes","woocommerce"),t)})]})},T=({slug:e,description:t,thumbnail_url:o,name:r,color_palettes:s=[],total_palettes:n=0,link_url:a="",is_active:i=!1,is_free:c,price:l,onClick:m})=>(0,x.jsxs)("div",{className:"theme-card",children:[(0,x.jsx)("div",{children:a?(0,x.jsx)(p.Link,{href:a,onClick:m,children:(0,x.jsx)("img",{src:o,alt:t})}):(0,x.jsx)("img",{src:o,alt:t})}),(0,x.jsxs)("div",{className:"theme-card__info",children:[(0,x.jsx)("h2",{className:"theme-card__title",children:r}),s&&(0,x.jsx)(D,{colorPalettes:s,totalPalettes:n})]}),(0,x.jsxs)("div",{children:[i&&(0,x.jsx)("span",{className:"theme-card__active",children:(0,k.__)("Active theme","woocommerce")}),!c&&(0,x.jsx)("span",{className:"theme-card__paid",children:(0,k.__)("Paid","woocommerce")}),(0,x.jsx)("span",{className:"theme-card__free",children:l})]})]},e);var v=o(81392);const z=()=>{const[e,t]=(0,c.useState)(!1);return(0,c.useEffect)((()=>{const e=()=>{t(!0)},o=()=>{t(!1)};return window.addEventListener("offline",e),window.addEventListener("online",o),()=>{window.removeEventListener("offline",e),window.removeEventListener("online",o)}}),[]),e};var E=o(30737);const S=o.p+"f7b873a3a464ccdb5585.svg";var A=o(42859),L=o(47884),C=o(85945);const O=(0,C.assign)({intro:(e,t)=>{const o=t.data.themeData;return{...e.intro,themeData:o}}}),F=(0,C.assign)({intro:(e,t)=>{const o=t.data.activeTheme;return{...e.intro,activeTheme:o}}}),B=(0,C.assign)({intro:(e,t)=>{const o=t.data.taskReferral;return{...e.intro,taskReferral:o}}}),P=()=>{(0,L.s)("customize_your_store_intro_design_without_ai_click")},U=(e,t)=>{(0,L.s)("customize_your_store_intro_theme_select",{theme:t.payload.theme,is_active:"SELECTED_ACTIVE_THEME"===t.type?"yes":"no"})},Q=()=>{(0,L.s)("customize_your_store_intro_browse_all_themes_click")},Y=(0,C.assign)({intro:(e,t)=>{const o=t.data.customizeStoreTaskCompleted;return{...e.intro,customizeStoreTaskCompleted:o}}}),R=(0,C.assign)({intro:e=>({...e.intro,hasErrors:!0})}),W=(0,C.assign)({intro:(e,t)=>{const{errorStatus:o}=t;return{...e.intro,hasErrors:!0,errorStatus:o}}}),G=(0,C.assign)({isFontLibraryAvailable:(e,t)=>t.payload}),H=(0,C.assign)({isPTKPatternsAPIAvailable:(e,t)=>t.payload}),Z=(0,C.assign)({activeThemeHasMods:(e,t)=>t.data.activeThemeHasMods}),V=(0,C.assign)({activeThemeHasMods:()=>(0,A.d4)(window)?window.parent.__wcCustomizeStore.activeThemeHasMods:window.__wcCustomizeStore.activeThemeHasMods,isFontLibraryAvailable:()=>(0,A.d4)(window)?window.parent.__wcCustomizeStore.isFontLibraryAvailable||!1:window.__wcCustomizeStore.isFontLibraryAvailable,isPTKPatternsAPIAvailable:()=>(0,A.d4)(window)?window.parent.__wcCustomizeStore.isPTKPatternsAPIAvailable||!1:window.__wcCustomizeStore.isPTKPatternsAPIAvailable});var J=o(1455),K=o.n(J);const q=async()=>{const e=await fetch("https://status.openai.com/api/v2/status.json");return await e.json()},X=async()=>await K()({path:"/wc-admin/onboarding/themes/recommended",method:"GET"}),$=async()=>{const e=await(0,g.resolveSelect)(d.onboardingStore).getTask("customize-store");return{customizeStoreTaskCompleted:e?.isComplete}},ee=async()=>{const e=(0,g.resolveSelect)(d.onboardingStore).getTask("customize-store"),t=X(),[o,r]=await Promise.all([e,t]),s=o?.isComplete;return{customizeStoreTaskCompleted:s,themeData:r,activeTheme:(await(0,g.resolveSelect)("core").getCurrentTheme()).stylesheet||""}},te=async()=>{if(!(0,A.d4)(window)){const e={FONT_LIBRARY_AVAILABLE:(async()=>{const e=await(async()=>{try{return await K()({path:"/wp/v2/font-collections?_fields=slug",method:"GET"}),!0}catch(e){return!1}})();window.__wcCustomizeStore={...window.__wcCustomizeStore,isFontLibraryAvailable:e}})(),PTK_PATTERNS_API_AVAILABLE:(async()=>{const e=await(async()=>{try{return await K()({path:"/wc/private/patterns",method:"GET"}),!0}catch(e){return!1}})();window.__wcCustomizeStore={...window.__wcCustomizeStore,isPTKPatternsAPIAvailable:e}})()};await Promise.all(Object.values(e))}},oe={"network-offline":E.wY,"jetpack-offline":E.ow,"no-ai":E.k8,"existing-no-ai-theme":E.QL,"classic-theme":E.G5,"non-default-block-theme":E.bp},re=({sendEvent:e,themeData:t})=>(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("p",{className:"select-theme-text",children:(0,k.__)("Or select a professionally designed theme to customize and make your own.","woocommerce")}),(0,x.jsx)("div",{className:"woocommerce-customize-store-theme-cards",children:t.themes?.map((t=>(0,x.jsx)(T,{slug:t.slug,description:t.description,thumbnail_url:t.thumbnail_url,name:t.name,color_palettes:t.color_palettes,total_palettes:t.total_palettes,link_url:t?.link_url,is_active:t.is_active,is_free:t.is_free,price:t.price,onClick:()=>{t.is_active?e({type:"SELECTED_ACTIVE_THEME",payload:{theme:t.slug}}):e({type:"SELECTED_NEW_THEME",payload:{theme:t.slug}})}},t.slug)))}),(0,x.jsxs)(j.Card,{className:"woocommerce-customize-store-browse-themes",children:[(0,x.jsx)(j.CardHeader,{children:(0,x.jsx)(N.Text,{variant:"title.small",as:"h2",className:"woocommerce-browse-themes-card__title",children:(0,k.__)("Visit the WooCommerce Theme Marketplace","woocommerce")})}),(0,x.jsxs)(j.CardFooter,{children:[(0,x.jsx)(N.Text,{variant:"body.small",as:"p",children:(0,k.__)("Browse more than 100 free and paid themes tailored to different industries—30-day money-back guarantee. If you change your mind within 30 days of your purchase, we’ll give you a full refund — hassle-free.","woocommerce")}),(0,x.jsx)("button",{onClick:()=>e({type:"SELECTED_BROWSE_ALL_THEMES"}),children:(0,k.__)("Browse all themes","woocommerce")})]})]})]}),se=({isBlockTheme:e,isDefaultTheme:t,sendEvent:o})=>{const[r,s]=(0,c.useState)(!1);return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("p",{className:"select-theme-text",children:(0,k.__)("Design or choose a new theme","woocommerce")}),(0,x.jsxs)("div",{className:"woocommerce-customize-store-cards",children:[(0,x.jsxs)("div",{className:"intro-card",children:[(0,x.jsx)("img",{src:"data:image/svg+xml;base64,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",alt:(0,k.__)("Design your own theme","woocommerce")}),(0,x.jsxs)("div",{children:[(0,x.jsx)("h2",{className:"intro-card__title",children:(0,k.__)("Design your own theme","woocommerce")}),(0,x.jsx)("button",{className:"intro-card__link",onClick:()=>{(0,L.s)("customize_your_store_intro_design_theme",{theme_type:e?"block":"classic"}),t?(0,A.$g)(window,(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub",{})):s(!0)},children:(0,k.__)("Use the store designer","woocommerce")})]})]}),(0,x.jsxs)("div",{className:"intro-card",children:[(0,x.jsx)("img",{src:S,alt:(0,k.__)("Choose a professionally designed theme","woocommerce")}),(0,x.jsxs)("div",{children:[(0,x.jsx)("h2",{className:"intro-card__title",children:(0,k.__)("Choose a professionally designed theme","woocommerce")}),(0,x.jsx)("button",{className:"intro-card__link",onClick:()=>{(0,L.s)("customize_your_store_intro_browse_themes"),o({type:"SELECTED_BROWSE_ALL_THEMES"})},children:(0,k.__)("Browse themes","woocommerce")})]})]})]}),r&&(0,x.jsx)(v.z,{setIsModalOpen:s,redirectToCYSFlow:()=>o({type:"DESIGN_WITHOUT_AI"})})]})},ne=o.p+"73ae3f4886531d9c3fee.svg",ae="data:image/svg+xml;base64,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",ie=o.p+"ef66842cc5fb22e3c7ca.svg",ce=[{title:(0,k.__)("Setting up the foundations","woocommerce"),image:(0,x.jsx)("img",{src:ne,alt:(0,k.__)("Setting up the foundations","woocommerce")}),progress:25},{title:(0,k.__)("Turning on the lights","woocommerce"),image:(0,x.jsx)("img",{src:ae,alt:(0,k.__)("Turning on the lights","woocommerce")}),progress:50},{title:(0,k.__)("Opening the doors","woocommerce"),image:(0,x.jsx)("img",{src:ie,alt:(0,k.__)("Opening the doors","woocommerce")}),progress:100}],le=()=>{const[e,t]=(0,c.useState)(5);(0,c.useEffect)((()=>{const e=e=>{const t=new Image;t.src=e,t.onload=()=>{}};e(ne),e(ae),e(ie)}),[]);const o=(0,A.am)(ce.slice(0,-1),10);return(0,x.jsxs)(w.Loader,{children:[(0,x.jsx)(w.Loader.Sequence,{interval:5e3/(o.length-1),shouldLoop:!1,onChange:e=>{setTimeout((()=>{t(o[e].progress)}),0)},children:o.map((e=>(0,x.jsxs)(w.Loader.Layout,{children:[(0,x.jsx)(w.Loader.Illustration,{children:e.image}),(0,x.jsx)(w.Loader.Title,{children:e.title})]},e.title)))}),(0,x.jsx)(w.Loader.ProgressBar,{className:"smooth-transition",progress:e||0})]})},me=({sendEvent:e})=>{const t=(0,u.getNewPath)({},"/customize-store/assembler-hub",{}),o=(0,c.useRef)(null),[r,s]=(0,c.useState)(!1);return(0,c.useEffect)((()=>{window.addEventListener("message",(t=>{"INSTALL_FONTS"===t.data?.type&&e({type:"INSTALL_FONTS"})}))}),[e]),(0,x.jsx)("iframe",{ref:o,onLoad:e=>{const o=()=>s(!0);(0,A.Hr)(e.currentTarget),(0,A.AV)(o),setTimeout(o,6e4),window.parent.history?.pushState({},"",t)},style:{opacity:r?1:0},src:t,title:"assembler-hub",className:"cys-fullscreen-iframe"})};var ue=o(24443);const de=[{title:"New - Neutral",version:2,settings:{color:{palette:{theme:[{color:"#000000",name:"Primary",slug:"primary"},{color:"#636363",name:"Secondary",slug:"secondary"},{color:"#000000",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#ffffff",name:"Tertiary",slug:"tertiary"},{color:"#FFFFFF",name:"Color 1",slug:"theme-1"},{color:"#1a0c00",name:"Color 5",slug:"theme-5"}]}}},styles:{blocks:{"core/button":{}},color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--primary)",text:"var(--wp--preset--color--background)"},":hover":{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}},link:{color:{text:"var(--wp--preset--color--foreground)"}}}}},{title:"Ancient Bronze",version:2,settings:{color:{palette:{theme:[{color:"#323856",name:"Primary",slug:"primary"},{color:"#8C8369",name:"Secondary",slug:"secondary"},{color:"#323856",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#F7F2EE",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Neutral"},{title:"Arctic Dawn",version:2,settings:{color:{palette:{theme:[{color:"#1E226F",name:"Primary",slug:"primary"},{color:"#DD301D",name:"Secondary",slug:"secondary"},{color:"#0D1263",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#F0F1F5",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Neutral"},{title:"Bronze Serenity",version:2,settings:{color:{palette:{theme:[{color:"#1e4b4b",name:"Primary",slug:"primary"},{color:"#9e7047",name:"Secondary",slug:"secondary"},{color:"#1e4b4b",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#e9eded",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Neutral"},{title:"Purple Twilight",version:2,settings:{color:{palette:{theme:[{color:"#301834",name:"Primary",slug:"primary"},{color:"#6a5eb7",name:"Secondary",slug:"secondary"},{color:"#090909",name:"Foreground",slug:"foreground"},{color:"#fefbff",name:"Background",slug:"background"},{color:"#f3eaf5",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Neutral"},{title:"Candy Store",version:2,settings:{color:{palette:{theme:[{color:"#293852",name:"Primary",slug:"primary"},{color:"#f1bea7",name:"Secondary",slug:"secondary"},{color:"#293852",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#fffddb",name:"Tertiary",slug:"tertiary"}]}}},styles:{blocks:{"core/button":{color:{background:"var(--wp--preset--color--secondary)"},variations:{outline:{border:{color:"var(--wp--preset--color--secondary)"},color:{text:"var(--wp--preset--color--primary)"}}}}},color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--primary)"}}}},wpcom_category:"Neutral"},{title:"Midnight Citrus",version:2,settings:{color:{palette:{theme:[{color:"#222222",name:"Primary",slug:"primary"},{color:"#c0f500",name:"Secondary",slug:"secondary"},{color:"#222222",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#f7faed",name:"Tertiary",slug:"tertiary"}]}}},styles:{blocks:{"core/button":{color:{background:"var(--wp--preset--color--secondary)"},variations:{outline:{border:{color:"var(--wp--preset--color--secondary)"},color:{text:"var(--wp--preset--color--primary)"}}}}},color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{":hover":{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--primary)"}},color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--primary)"}}}},wpcom_category:"Neutral"},{title:"Crimson Tide",version:2,settings:{color:{palette:{theme:[{color:"#101317",name:"Primary",slug:"primary"},{color:"#EC5E3F",name:"Secondary",slug:"secondary"},{color:"#101317",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#EEEEEE",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Neutral"},{title:"Raspberry Chocolate",version:2,settings:{color:{palette:{theme:[{color:"#42332e",name:"Primary",slug:"primary"},{color:"#d64d68",name:"Secondary",slug:"secondary"},{color:"#241d1a",name:"Foreground",slug:"foreground"},{color:"#eeeae6",name:"Background",slug:"background"},{color:"#D6CCC2",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Bright"},{title:"Gumtree Sunset",version:2,settings:{color:{palette:{theme:[{color:"#8699A1",name:"Primary",slug:"primary"},{color:"#BB6154",name:"Secondary",slug:"secondary"},{color:"#476C77",name:"Foreground",slug:"foreground"},{color:"#F4F7F7",name:"Background",slug:"background"},{color:"#ffffff",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Bright"},{title:"Fuchsia",version:2,settings:{color:{palette:{theme:[{color:"#b7127f",name:"Primary",slug:"primary"},{color:"#18020C",name:"Secondary",slug:"secondary"},{color:"#b7127f",name:"Foreground",slug:"foreground"},{color:"#f7edf6",name:"Background",slug:"background"},{color:"#ffffff",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Bright"},{title:"Cinder",version:2,settings:{color:{palette:{theme:[{color:"#c14420",name:"Primary",slug:"primary"},{color:"#2F2D2D",name:"Secondary",slug:"secondary"},{color:"#c14420",name:"Foreground",slug:"foreground"},{color:"#f1f2f2",name:"Background",slug:"background"},{color:"#DCDCDC",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Bright"},{title:"Canary",version:2,settings:{color:{palette:{theme:[{color:"#0F0F05",name:"Primary",slug:"primary"},{color:"#666666",name:"Secondary",slug:"secondary"},{color:"#0F0F05",name:"Foreground",slug:"foreground"},{color:"#FCFF9B",name:"Background",slug:"background"},{color:"#E8EB8C",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Bright"},{title:"Blue Lagoon",version:2,settings:{color:{palette:{theme:[{color:"#004DE5",name:"Primary",slug:"primary"},{color:"#0496FF",name:"Secondary",slug:"secondary"},{color:"#0036A3",name:"Foreground",slug:"foreground"},{color:"#FEFDF8",name:"Background",slug:"background"},{color:"#DEF2F7",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Bright"},{title:"Vibrant Berry",version:2,settings:{color:{palette:{theme:[{slug:"primary",color:"#7C1D6F",name:"Primary"},{slug:"secondary",color:"#C62FB2",name:"Secondary"},{slug:"foreground",color:"#7C1D6F",name:"Foreground"},{slug:"background",color:"#FFEED6",name:"Background"},{slug:"tertiary",color:"#FDD8DE",name:"Tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Bright"},{title:"Aquamarine Night",version:2,settings:{color:{palette:{theme:[{color:"#deffef",name:"Primary",slug:"primary"},{color:"#56fbb9",name:"Secondary",slug:"secondary"},{color:"#ffffff",name:"Foreground",slug:"foreground"},{color:"#091C48",name:"Background",slug:"background"},{color:"#10317F",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Dark"},{title:"Evergreen Twilight",version:2,settings:{color:{palette:{theme:[{color:"#ffffff",name:"Primary",slug:"primary"},{color:"#8EE978",name:"Secondary",slug:"secondary"},{color:"#ffffff",name:"Foreground",slug:"foreground"},{color:"#181818",name:"Background",slug:"background"},{color:"#636363",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Dark"},{title:"Cinnamon Latte",version:2,settings:{color:{palette:{theme:[{slug:"primary",color:"#D9CAB3",name:"Primary"},{slug:"secondary",color:"#BC8034",name:"Secondary"},{slug:"foreground",color:"#FFFFFF",name:"Foreground"},{slug:"background",color:"#3C3F4D",name:"Background"},{slug:"tertiary",color:"#2B2D36",name:"Tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Dark"},{title:"Lightning",version:2,settings:{color:{palette:{theme:[{color:"#ebffd2",name:"Primary",slug:"primary"},{color:"#fefefe",name:"Secondary",slug:"secondary"},{color:"#ebffd2",name:"Foreground",slug:"foreground"},{color:"#0e1fb5",name:"Background",slug:"background"},{color:"#0A1680",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Dark"},{title:"Lilac Nightshade",version:2,settings:{color:{palette:{theme:[{color:"#f5d6ff",name:"Primary",slug:"primary"},{color:"#C48DDA",name:"Secondary",slug:"secondary"},{color:"#ffffff",name:"Foreground",slug:"foreground"},{color:"#000000",name:"Background",slug:"background"},{color:"#462749",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Dark"},{title:"Charcoal",version:2,settings:{color:{palette:{theme:[{color:"#dbdbdb",name:"Primary",slug:"primary"},{color:"#efefef",name:"Secondary",slug:"secondary"},{color:"#dbdbdb",name:"Foreground",slug:"foreground"},{color:"#1e1e1e",name:"Background",slug:"background"},{color:"#000000",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Dark"},{title:"Rustic Rosewood",version:2,settings:{color:{palette:{theme:[{color:"#F4F4F2",name:"Primary",slug:"primary"},{color:"#EE797C",name:"Secondary",slug:"secondary"},{color:"#ffffff",name:"Foreground",slug:"foreground"},{color:"#1A1A1A",name:"Background",slug:"background"},{color:"#3B3939",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Dark"},{title:"Sandalwood Oasis",version:2,settings:{color:{palette:{theme:[{color:"#F0EBE3",name:"Primary",slug:"primary"},{color:"#DF9785",name:"Secondary",slug:"secondary"},{color:"#ffffff",name:"Foreground",slug:"foreground"},{color:"#2a2a16",name:"Background",slug:"background"},{color:"#434323",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"}},wpcom_category:"Dark"},{title:"Slate",version:2,settings:{color:{palette:{theme:[{slug:"primary",color:"#FFFFFF",name:"Primary"},{slug:"secondary",color:"#FFDF6D",name:"Secondary"},{slug:"foreground",color:"#EFF2F9",name:"Foreground"},{slug:"background",color:"#13161E",name:"Background"},{slug:"tertiary",color:"#303036",name:"Tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}},wpcom_category:"Dark"},{title:"Blueberry Sorbet",version:2,settings:{color:{palette:{theme:[{color:"#2038B6",name:"Primary",slug:"primary"},{color:"#BD4089",name:"Secondary",slug:"secondary"},{color:"#2038B6",name:"Foreground",slug:"foreground"},{color:"#FDFBEF",name:"Background",slug:"background"},{color:"#F8F2E2",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}}}}},{title:"Green Thumb",version:2,settings:{color:{palette:{theme:[{color:"#164A41",name:"Primary",slug:"primary"},{color:"#4B7B4D",name:"Secondary",slug:"secondary"},{color:"#164A41",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#CEEAC4",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}},link:{color:{text:"var(--wp--preset--color--secondary)"},":hover":{color:{text:"var(--wp--preset--color--foreground)"}}}}}},{title:"Golden Haze",version:2,settings:{color:{palette:{theme:[{color:"#232224",name:"Primary",slug:"primary"},{color:"#EBB54F",name:"Secondary",slug:"secondary"},{color:"#515151",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#FFF0AE",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--foreground)"}},link:{color:{text:"var(--wp--preset--color--secondary)"},":hover":{color:{text:"var(--wp--preset--color--foreground)"}}}}}},{title:"Golden Indigo",version:2,settings:{color:{palette:{theme:[{color:"#4866C0",name:"Primary",slug:"primary"},{color:"#C09F50",name:"Secondary",slug:"secondary"},{color:"#405AA7",name:"Foreground",slug:"foreground"},{color:"#ffffff",name:"Background",slug:"background"},{color:"#FBF5EE",name:"Tertiary",slug:"tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}},link:{color:{text:"var(--wp--preset--color--secondary)"},":hover":{color:{text:"var(--wp--preset--color--foreground)"}}}}}},{title:"Ice",version:2,settings:{color:{palette:{theme:[{slug:"primary",color:"#3473FE",name:"Primary"},{slug:"secondary",color:"#12123F",name:"Secondary"},{slug:"foreground",color:"#12123F",name:"Foreground"},{slug:"background",color:"#F1F4FA",name:"Background"},{slug:"tertiary",color:"#DBE6EE",name:"Tertiary"}]}}},styles:{color:{background:"var(--wp--preset--color--background)",text:"var(--wp--preset--color--foreground)"},elements:{button:{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}},link:{color:{text:"var(--wp--preset--color--foreground)"},":hover":{color:{text:"var(--wp--preset--color--primary)"}}}}}}].map((e=>({...e,styles:{...e.styles,blocks:{"core/button":{color:{background:"var(--wp--preset--color--secondary)"},variations:{outline:{border:{color:"var(--wp--preset--color--secondary)"},color:{text:"var(--wp--preset--color--secondary)"}}}},"core/heading":{color:{text:"var(--wp--preset--color--foreground)"},elements:{link:{color:{text:"var(--wp--preset--color--foreground)"}}}},"core/post-date":{color:{text:"var(--wp--preset--color--foreground)"}},"core/post-title":{color:{text:"var(--wp--preset--color--foreground)"},elements:{link:{":hover":{color:{text:"var(--wp--preset--color--primary)"}},color:{text:"var(--wp--preset--color--foreground)"}}}},"core/pullquote":{border:{color:"var(--wp--preset--color--foreground)",style:"solid",width:"1px 0"}},"core/quote":{border:{color:"var(--wp--preset--color--foreground)",style:"solid",width:"0 0 0 5px"}},"core/separator":{color:{text:"var(--wp--preset--color--foreground)"}},"core/cover":{elements:{heading:{color:{text:"#ffffff"}}}},"core/site-title":{elements:{link:{":hover":{color:{text:"var(--wp--preset--color--foreground)"}},color:{text:"var(--wp--preset--color--foreground)"}}}},...e.styles.blocks},elements:{heading:{color:{text:"var(--wp--preset--color--foreground)"}},button:{":active":{color:{background:"var(--wp--preset--color--foreground)",text:"var(--wp--preset--color--background)"}},":focus":{color:{background:"var(--wp--preset--color--foreground)",text:"var(--wp--preset--color--background)"},outline:{color:"var(--wp--preset--color--primary)",offset:"2px",style:"dotted",width:"1px"}},":hover":{color:{background:"var(--wp--preset--color--secondary)",text:"var(--wp--preset--color--background)"}},":visited":{color:{text:e.styles.elements?.button?e.styles.elements.button.color:"var(--wp--preset--color--background)"}},color:{background:"var(--wp--preset--color--primary)",text:"var(--wp--preset--color--background)"}},link:{":hover":{color:{text:"var(--wp--preset--color--primary)"},typography:{textDecoration:"none"}},color:{text:"var(--wp--preset--color--foreground)"}},...e.styles.elements}}}))),ge=["New - Neutral","Blueberry Sorbet","Ancient Bronze","Crimson Tide","Purple Twilight","Green Thumb","Golden Haze","Golden Indigo","Arctic Dawn","Raspberry Chocolate","Canary","Ice","Rustic Rosewood","Cinnamon Latte","Lightning","Aquamarine Night","Charcoal","Slate"].map((e=>de.find((t=>t.title===e)))),pe={inter:{fontFamily:"Inter",fontWeights:["400","500","600"],fontStyles:["normal"]},"bodoni-moda":{fontFamily:"Bodoni Moda",fontWeights:["400"],fontStyles:["normal"]},overpass:{fontFamily:"Overpass",fontWeights:["300","400"],fontStyles:["normal"]},"albert-sans":{fontFamily:"Albert Sans",fontWeights:["700"],fontStyles:["normal"]},lora:{fontFamily:"Lora",fontWeights:["400"],fontStyles:["normal"]},montserrat:{fontFamily:"Montserrat",fontWeights:["500","700"],fontStyles:["normal"]},arvo:{fontFamily:"Arvo",fontWeights:["400"],fontStyles:["normal"]},rubik:{fontFamily:"Rubik",fontWeights:["400","800"],fontStyles:["normal"]},newsreader:{fontFamily:"Newsreader",fontWeights:["400"],fontStyles:["normal"]},cormorant:{fontFamily:"Cormorant",fontWeights:["400","500"],fontStyles:["normal"]},"work-sans":{fontFamily:"Work Sans",fontWeights:["400"],fontStyles:["normal"]},raleway:{fontFamily:"Raleway",fontWeights:["700"],fontStyles:["normal"]}},ye=[{title:"Inter + Inter",version:2,lookAndFeel:["Contemporary","Bold"],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Inter",slug:"inter"}]}}},styles:{elements:{button:{typography:{fontFamily:"var(--wp--preset--font-family--inter)",fontWeight:"400",lineHeight:"1"}},heading:{typography:{fontFamily:"var(--wp--preset--font-family--inter)",fontStyle:"normal",fontWeight:"600",lineHeight:"1.2"}}},blocks:{"core/site-title":{typography:{fontFamily:"var(--wp--preset--font-family--inter)",fontSize:"var(--wp--preset--font-size--medium)",fontStyle:"normal"}},"core/post-navigation-link":{typography:{fontFamily:"var(--wp--preset--font-family--inter)"}}},typography:{fontFamily:"var(--wp--preset--font-family--inter)",fontSize:"var(--wp--preset--font-size--medium)",fontStyle:"normal",fontWeight:"400",lineHeight:"1.6"}}},{title:"Bodoni Moda + Overpass",version:2,lookAndFeel:["Classic"],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Bodoni Moda",slug:"bodoni-moda"},{fontFamily:"Overpass",slug:"overpass"}]}}},styles:{elements:{button:{typography:{fontFamily:"var(--wp--preset--font-family--overpass)",fontWeight:"400",lineHeight:"1"}},heading:{typography:{fontFamily:"var(--wp--preset--font-family--bodoni-moda)",fontStyle:"normal",fontWeight:"400"}}},blocks:{"core/site-title":{typography:{fontFamily:"var(--wp--preset--font-family--bodoni-moda)"}},"core/post-navigation-link":{typography:{fontFamily:"var(--wp--preset--font-family--bodoni-moda)"}}},typography:{fontFamily:"var(--wp--preset--font-family--overpass)",fontSize:"var(--wp--preset--font-size--medium)",fontStyle:"normal",fontWeight:"300",lineHeight:"1.6"}}},{title:"Albert Sans + Lora",version:2,lookAndFeel:["Contemporary","Bold"],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Albert Sans",slug:"albert-sans"},{fontFamily:"Lora",slug:"lora"}]}}},styles:{elements:{heading:{typography:{fontFamily:"var(--wp--preset--font-family--albert-sans)",fontStyle:"normal",fontWeight:"700"}}},blocks:{"core/site-title":{typography:{fontFamily:"var(--wp--preset--font-family--albert-sans)",fontSize:"var(--wp--preset--font-size--medium)"}}},typography:{fontFamily:"var(--wp--preset--font-family--lora)",fontStyle:"normal",fontWeight:"400",lineHeight:"1.67"}}},{title:"Montserrat + Arvo",version:2,lookAndFeel:["Contemporary","Bold"],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Montserrat",slug:"montserrat"},{fontFamily:"Arvo",slug:"arvo"}]}}},styles:{elements:{button:{typography:{fontFamily:"var(--wp--preset--font-family--arvo)",fontStyle:"normal",fontWeight:"500"}},heading:{typography:{fontFamily:"var(--wp--preset--font-family--montserrat)",fontStyle:"normal",fontWeight:"700",lineHeight:"1.4"}}},blocks:{"core/site-title":{typography:{fontFamily:"var(--wp--preset--font-family--montserrat)",fontWeight:"700"}},"core/post-navigation-link":{typography:{fontFamily:"var(--wp--preset--font-family--montserrat)"}}},typography:{fontFamily:"var(--wp--preset--font-family--arvo)",fontSize:"var(--wp--preset--font-size--small)",fontStyle:"normal",fontWeight:"400",lineHeight:"1.6"}}},{title:"Rubik + Inter",version:2,lookAndFeel:["Bold"],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Rubik",slug:"rubik"},{fontFamily:"Inter",slug:"inter"}]}}},styles:{elements:{button:{typography:{fontFamily:"var(--wp--preset--font-family--inter)",fontWeight:"400",lineHeight:"1"}},heading:{typography:{fontFamily:"var(--wp--preset--font-family--rubik)",fontStyle:"normal",fontWeight:"800"}}},blocks:{"core/site-title":{typography:{fontFamily:"var(--wp--preset--font-family--rubik)",fontWeight:"800"}},"core/post-navigation-link":{typography:{fontFamily:"var(--wp--preset--font-family--rubik)"}}},typography:{fontFamily:"var(--wp--preset--font-family--inter)",fontSize:"var(--wp--preset--font-size--medium)",fontStyle:"normal",fontWeight:"400",lineHeight:"1.6"}}},{title:"Newsreader + Newsreader",version:2,lookAndFeel:["Classic"],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Newsreader",slug:"newsreader"}]}}},styles:{elements:{heading:{typography:{fontFamily:"var(--wp--preset--font-family--newsreader)",fontStyle:"normal",fontWeight:"400"}}},blocks:{"core/site-title":{typography:{fontFamily:"var(--wp--preset--font-family--newsreader)"}}},typography:{fontFamily:"var(--wp--preset--font-family--newsreader)",fontSize:"var(--wp--preset--font-size--medium)",lineHeight:"1.67"}}},{title:"Cormorant + Work Sans",version:2,lookAndFeel:[],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Cormorant",slug:"cormorant"},{fontFamily:"Work Sans",slug:"work-sans"}]}}},styles:{elements:{heading:{typography:{fontFamily:"var(--wp--preset--font-family--cormorant)",fontStyle:"normal",fontWeight:"500"}}},blocks:{"core/site-title":{typography:{fontFamily:"var(--wp--preset--font-family--cormorant)"}}},typography:{fontFamily:"var(--wp--preset--font-family--work-sans)"}}},{title:"Raleway + Cormorant",version:2,lookAndFeel:["Classic","Bold"],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Raleway",slug:"raleway"},{fontFamily:"Cormorant",slug:"cormorant"}]}}},styles:{elements:{heading:{typography:{fontFamily:"var(--wp--preset--font-family--raleway)",fontStyle:"normal",fontWeight:"700"}}},blocks:{"core/site-title":{typography:{fontFamily:"var(--wp--preset--font-family--raleway)"}}},typography:{fontFamily:"var(--wp--preset--font-family--cormorant)",fontSize:"var(--wp--preset--font-size--medium)",lineHeight:"1.67"}}}],he=[{title:"Cardo Font + System Sans-serif",version:2,lookAndFeel:[],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Cardo",slug:"heading"},{fontFamily:"System Sans-serif",slug:"system-sans-serif"}]}}},styles:{elements:{heading:{typography:{fontFamily:"var(--wp--preset--font-family--heading)",fontStyle:"normal",fontWeight:"300"}}},typography:{fontFamily:"var(--wp--preset--font-family--system-sans-serif)"}}},{title:"Inter + Cardo Font",version:2,lookAndFeel:[],settings:{typography:{fontFamilies:{theme:[{fontFamily:"Inter",slug:"body"},{fontFamily:"Cardo",slug:"heading"}]}}},styles:{elements:{heading:{typography:{fontFamily:"var(--wp--preset--font-family--body)",fontStyle:"normal",fontWeight:"300"}}},typography:{fontFamily:"var(--wp--preset--font-family--heading)"}}}];var we=o(74997),Me=o(89363),ke=o(94715);const be=(0,c.createContext)({logoBlockIds:[],setLogoBlockIds:()=>{}}),fe=()=>{const{logoBlockIds:e}=(0,c.useContext)(be),{attributes:t,isAttributesLoading:o}=(0,g.useSelect)((t=>{const o=t(ke.store).getBlocksByClientId(e),r=!o.length||null===o[0];return r?{attributes:{},isAttributesLoading:r}:{attributes:o[0].attributes,isAttributesLoading:r}}),[e]);return{attributes:t,isAttributesLoading:o}},je="twentytwentyfour",Ne=()=>{const{blockPatterns:e,isLoading:t,invalidateCache:o}=(0,g.useSelect)((e=>({blockPatterns:e(a.store).getBlockPatterns(),isLoading:!e(a.store).hasFinishedResolution("getBlockPatterns"),invalidateCache:()=>(0,g.dispatch)(a.store).invalidateResolutionForStoreSelector("getBlockPatterns")})),[]);return{blockPatterns:e,isLoading:t,invalidateCache:o}},Ie=e=>{const{blockPatterns:t,isLoading:o}=Ne(),{attributes:r,isAttributesLoading:s}=fe(),[n,a]=(0,c.useState)(r.width);(0,c.useEffect)((()=>{s||a(r.width)}),[s,r.width,n]);const i=(0,c.useMemo)((()=>(t||[]).filter((t=>t.categories?.includes(e)&&!t.name.includes(je)&&"pattern-directory/theme"!==t.source&&"pattern-directory/core"!==t.source))),[t,e]);return{isLoading:o,patterns:(0,c.useMemo)((()=>i.map((e=>{const t=(0,A.Gy)(e.content,n);return{...e,content:t,blocks:(0,we.parse)(t,{__unstableSkipMigrationLogs:!0})}}))),[i,n])}};var _e=o(97687);const xe="woocommerce-blocks/hero-product-split",De=`${je}//header`,Te=`${je}//footer`,ve={template1:{blocks:["woocommerce-blocks/header-centered-menu"]},template2:{blocks:["woocommerce-blocks/header-essential"]},template3:{blocks:["woocommerce-blocks/header-centered-menu"]}},ze={template1:{blocks:["woocommerce-blocks/footer-with-3-menus"]},template2:{blocks:["woocommerce-blocks/footer-large"]},template3:{blocks:["woocommerce-blocks/footer-with-3-menus"]}},Ee={template1:{blocks:[(0,_e.E)()?xe:"woocommerce-blocks/just-arrived-full-hero","woocommerce-blocks/product-collection-5-columns","woocommerce-blocks/hero-product-3-split","woocommerce-blocks/product-collection-3-columns","woocommerce-blocks/testimonials-3-columns","woocommerce-blocks/featured-category-triple","woocommerce-blocks/social-follow-us-in-social-media"],metadata:{businessType:["e-commerce","large-business"],contentFocus:["featured products"],audience:["general"],design:["contemporary"],features:["fullwidth-image-banner","testimonials","social-media","search"],complexity:"high"}},template2:{blocks:[(0,_e.E)()?xe:"woocommerce-blocks/featured-category-cover-image","woocommerce-blocks/product-collection-4-columns","woocommerce-blocks/hero-product-chessboard","woocommerce-blocks/product-collection-5-columns","woocommerce-blocks/testimonials-3-columns"],metadata:{businessType:["e-commerce","subscription","large-business"],contentFocus:["catalog"],audience:["general"],design:["contemporary"],features:["small-banner","testimonials","newsletter"],complexity:"high"}},template3:{blocks:["woocommerce-blocks/hero-product-split","woocommerce-blocks/product-collection-featured-products-5-columns","woocommerce-blocks/featured-category-triple","woocommerce-blocks/product-query-product-gallery"],metadata:{businessType:["subscription","large-business"],contentFocus:["catalog","call-to-action"],audience:["general"],design:["contemporary"],features:["small-banner","social-media"],complexity:"high"}}},Se=(e,t)=>e.map((e=>{const o=t[e];return o&&o.content?{...o,blocks:(0,we.parse)(o.content,{__unstableSkipMigrationLogs:!0})}:null})).filter((e=>null!==e)),Ae=e=>e.reduce(((e,t)=>(e[t.name]=t,e)),{}),Le=()=>!window.parent?.window.cys_aiFlow&&!window.parent?.window.cys_aiFlow&&window.wcAdminFeatures["pattern-toolkit-full-composability"]&&[ke.BlockPopover].every((e=>null!=e)),Ce=async({homepageTemplateId:e})=>{Le()?await(async()=>{const{invalidateResolutionForStoreSelector:e}=(0,g.dispatch)(a.store);e("getBlockPatterns"),e("getDefaultTemplateId"),(0,Me.registerCoreBlocks)((0,Me.__experimentalGetCoreBlocks)());const t=await(0,g.resolveSelect)(a.store).getBlockPatterns(),o=Object.entries({header:"woocommerce-blocks/header-essential",intro:"woocommerce-blocks/centered-content-with-image-below",footer:"woocommerce-blocks/footer-with-3-menus"}).reduce(((e,[o,r])=>{const s=t.find((e=>e.name===r)),n=s?(e=>{const t=(0,we.parse)(e.content);return t.length>0&&(t[0].attributes={...t[0].attributes,metadata:{...t[0].attributes.metadata||{},categories:e.categories,patternName:e.name,name:t[0].attributes.metadata?.name||e.title}}),t})(s):[];return{...e,[o]:n}}),{footer:[],intro:[],header:[]}),r=(0,we.serialize)(o.header),s=(0,we.serialize)(o.footer);let n=(0,we.serialize)(o.intro);n=`\x3c!-- wp:template-part {"slug":"header", "theme": "${je}"} /--\x3e`+n+`\x3c!-- wp:template-part {"slug":"footer", "theme": "${je}"} /--\x3e`,n=(0,A.Gy)(n);const i=await(0,g.resolveSelect)(a.store).getDefaultTemplateId({slug:"home"}),{saveEntityRecord:c}=(0,g.dispatch)(a.store);await Promise.all([c("postType","wp_template_part",{id:`${je}//header`,content:r},{throwOnError:!0}),c("postType","wp_template_part",{id:`${je}//footer`,content:s},{throwOnError:!0}),c("postType","wp_template",{id:i,content:n},{throwOnError:!0})])})():await(async({homepageTemplateId:e})=>{const{invalidateResolutionForStoreSelector:t}=(0,g.dispatch)(a.store);t("getBlockPatterns"),t("getDefaultTemplateId");const o=await(0,g.resolveSelect)(a.store).getBlockPatterns(),r=Ae(o),s=Se(Ee[e].blocks,r),n=Se(ve[e].blocks,r),i=Se(ze[e].blocks,r),c=[...n].filter(Boolean).map((e=>e.content)).join("\n\n"),l=[...i].filter(Boolean).map((e=>e.content)).join("\n\n");let m=[...s].filter(Boolean).map((e=>e.content)).join("\n\n");m=`\x3c!-- wp:template-part {"slug":"header", "theme": "${je}"} /--\x3e`+m+`\x3c!-- wp:template-part {"slug":"footer", "theme": "${je}"} /--\x3e`,m=(0,A.Gy)(m);const u=await(0,g.resolveSelect)(a.store).getDefaultTemplateId({slug:"home"}),{saveEntityRecord:d}=(0,g.dispatch)(a.store);await Promise.all([d("postType","wp_template_part",{id:`${je}//header`,content:c},{throwOnError:!0}),d("postType","wp_template_part",{id:`${je}//footer`,content:l},{throwOnError:!0}),d("postType","wp_template",{id:u,content:m},{throwOnError:!0})])})({homepageTemplateId:e})},Oe=async(e,t)=>{const{fontFamilyId:o,...r}=e,s=await async function(e){try{const t=await(await fetch(new Request(e))).blob(),o=e.split("/").pop();return new File([t],o,{type:t.type})}catch(t){throw new Error(`Error downloading font face asset from ${e}`)}}(Array.isArray(r.src)?r.src[0]:r.src),n=new FormData,a=await function(e,t,o){const r=`file-${o}`;return t.append(r,e,e.name),r}(s,n,t);n.append("font_face_settings",JSON.stringify({...r,src:a}));const i={path:`/wp/v2/font-families/${e.fontFamilyId}/font-faces/`,method:"POST",body:n};return K()(i)},Fe=async()=>{const e=await(0,g.resolveSelect)("core").getEntityRecords("postType","wp_font_family",{per_page:-1}),t=await Promise.all(e.map((async e=>{const t=await K()({path:`/wp/v2/font-families/${e.id}/font-faces`,method:"GET"});return{...e,font_face:t}}))),o=await K()({path:"/wp/v2/font-collections/google-fonts",method:"GET"}),{fontFacesToInstall:r,fontFamiliesWithFontFacesToInstall:s}=((e,t)=>Object.entries(pe).reduce(((o,[r,s])=>{const n=((e,t,o)=>{const r=e.font_families.find((({font_family_settings:e})=>e.slug===t));if(!r)return null;const s=r?.font_family_settings.fontFace.filter((({fontWeight:e})=>o.fontWeights.includes(e)));return{...r?.font_family_settings,fontFace:s}})(e,r,s);if(!n)return o;const a=((e,t)=>e.find((({font_family_settings:e})=>e.slug===t)))(t,n.slug);if(!a)return{...o,fontFamiliesWithFontFacesToInstall:[...o.fontFamiliesWithFontFacesToInstall,n]};const i=a.font_face.filter((({fontWeight:e})=>s.fontWeights.includes(e)));return{...o,fontFacesToInstall:[...o.fontFacesToInstall,...i.map((e=>({...e,fontFamilyId:a.id})))]}}),{fontFamiliesWithFontFacesToInstall:[],fontFacesToInstall:[]}))(o,t),n=s.map((async e=>{const t=await(e=>{const t={path:"/wp/v2/font-families",method:"POST",data:{font_family_settings:JSON.stringify({name:e.name,slug:e.slug,fontFamily:e.fontFamily,preview:e.preview})}};return K()(t)})(e);return Promise.all(e.fontFace.map((async(e,o)=>Oe({...e,fontFamilyId:t.id},o))))})),a=r.map(Oe);return await Promise.all([...n,...a])},Be=async e=>{const t=de[0],o="yes"===await(0,g.resolveSelect)(d.optionsStore).getOption("woocommerce_allow_tracking"),r=e.isFontLibraryAvailable&&o?ye[0]:he[0],s=await(async()=>{const e=await(async()=>{let e=3;for(;e>0;){const t=await(0,g.resolveSelect)("core").getEntityRecords("root","theme",{status:"active"},!0);if(t)return t;e--}return null})();if(!e)return null;const t=e[0]?._links,o=t?.["wp:user-global-styles"]?.[0]?.href;return(await K()({url:o})).id})();if(!s)return;const{saveEntityRecord:n}=(0,g.dispatch)(a.store);await n("root","globalStyles",{id:s,styles:(0,ue.n)(t?.styles||{},r?.styles||{}),settings:(0,ue.n)(t?.settings||{},r?.settings||{})},{throwOnError:!0})},Pe=async()=>{try{await(0,g.dispatch)(d.optionsStore).updateOptions({woocommerce_allow_tracking:"yes"}),window.wcTracks.isEnabled=!0}catch(e){throw e}},Ue={assembleSite:async()=>{await Ce({homepageTemplateId:"template1"})},browserPopstateHandler:()=>e=>{const t=()=>{e({type:"EXTERNAL_URL_UPDATE"})};return window.addEventListener("popstate",t),()=>{window.removeEventListener("popstate",t)}},installAndActivateTheme:async e=>{try{await(async e=>{await K()({path:`/wc-admin/onboarding/themes/install?theme=${e}`,method:"POST"}),await K()({path:`/wc-admin/onboarding/themes/activate?theme=${e}&theme_switch_via_cys_ai_loader=1`,method:"POST"})})(je),await Be(e)}catch(e){throw(0,L.s)("customize_your_store__no_ai_install_and_activate_theme_error",{theme:je,error:e instanceof Error?e.message:"unknown"}),e}},createProducts:async()=>{try{const{success:e}=await K()({path:"/wc-admin/onboarding/products",method:"POST"});if(!e)throw new Error("Product creation failed")}catch(e){throw e}},installFontFamilies:async()=>{if(window.wcTracks?.isEnabled)try{await Fe()}catch(e){throw e}},installPatterns:async()=>{if(window.wcAdminFeatures["pattern-toolkit-full-composability"]&&window.wcTracks?.isEnabled)try{const{success:e}=await K()({path:"/wc/private/patterns",method:"POST"});if(!e)throw new Error("Fetching patterns failed")}catch(e){throw e}},updateGlobalStylesWithDefaultValues:Be,enableTracking:Pe,updateShowOnFront:async()=>{try{await K()({path:"/wp/v2/settings",method:"POST",data:{show_on_front:"posts"}})}catch(e){throw e}}},Qe={redirectToAssemblerHub:async()=>{window.parent.__wcCustomizeStore.activeThemeHasMods=!0},redirectToIntroWithError:(0,C.sendParent)(((e,t)=>{const o=t;return{type:"NO_AI_FLOW_ERROR",errorStatus:o?.data?.data?.status}})),redirectToAssemblerHubSection:(e,t,{action:o})=>{const{section:r}=o;(0,A.$g)(window,(0,u.getNewPath)({customizing:!0},`/customize-store/assembler-hub/${r}`,{}))}},Ye={initial:"checkFontLibrary",states:{checkFontLibrary:{always:[{cond:{type:"isFontLibraryAvailable"},target:"pending"},{target:"success"}]},pending:{invoke:{src:"installFontFamilies",onDone:{target:"success"},onError:{actions:"redirectToIntroWithError"}}},success:{type:"final"}}},Re=(0,i.O)({id:"designWithoutAI",predictableActionArguments:!0,preserveActionOrder:!0,schema:{context:{},events:{}},invoke:{src:"browserPopstateHandler"},on:{EXTERNAL_URL_UPDATE:{target:"navigate"},INSTALL_FONTS:{target:"installFontFamilies"}},context:{startLoadingTime:null,apiCallLoader:{hasErrors:!1},isFontLibraryAvailable:!1,isPTKPatternsAPIAvailable:!1,isBlockTheme:!1},initial:"navigate",states:{navigate:{always:[{cond:{type:"hasFontInstallInUrl",step:"design"},target:"installFontFamilies"},{cond:{type:"hasPatternInstallInUrl",step:"design"},target:"installPatterns"},{cond:{type:"hasStepInUrl",step:"design"},target:"preAssembleSite"}]},installFontFamilies:{meta:{component:le},initial:"enableTracking",states:{enableTracking:{invoke:{src:"enableTracking",onDone:{target:"checkFontLibrary"}}},checkFontLibrary:Ye.states.checkFontLibrary,pending:Ye.states.pending,success:{type:"final"}},onDone:{target:"#designWithoutAI.showAssembleHubTypography"}},installPatterns:{meta:{component:le},initial:"enableTracking",states:{enableTracking:{invoke:{src:"enableTracking",onDone:{target:"fetchPatterns"}}},fetchPatterns:{invoke:{src:"installPatterns",onDone:{target:"success"},onError:{actions:"redirectToIntroWithError"}}},success:{type:"final"}},onDone:{target:"#designWithoutAI.showAssembleHubHomepage"}},preAssembleSite:{initial:"preApiCallLoader",id:"preAssembleSite",states:{preApiCallLoader:{meta:{component:le},type:"parallel",states:{updateShowOnFront:{initial:"pending",states:{pending:{invoke:{src:"updateShowOnFront",onDone:{target:"success"},onError:{actions:"redirectToIntroWithError"}}},success:{type:"final"}}},installAndActivateTheme:{initial:"pending",states:{pending:{invoke:{src:"installAndActivateTheme",onDone:{target:"success"},onError:{actions:"redirectToIntroWithError"}}},success:{type:"final"}}},createProducts:{initial:"pending",states:{pending:{invoke:{src:"createProducts",onDone:{target:"success"},onError:{actions:"redirectToIntroWithError"}}},success:{type:"final"}}},installFontFamilies:{initial:Ye.initial,states:{checkFontLibrary:Ye.states.checkFontLibrary,pending:Ye.states.pending,success:{type:"final"}}},installPatterns:{initial:"pending",states:{pending:{invoke:{src:"installPatterns",onDone:{target:"success"},onError:{target:"success"}}},success:{type:"final"}}}},onDone:{target:"assembleSite"}},assembleSite:{meta:{component:le},initial:"pending",states:{pending:{invoke:{src:"assembleSite",onDone:{target:"success"},onError:{actions:"redirectToIntroWithError"}}},success:{type:"final"}},onDone:{target:"#designWithoutAI.showAssembleHub"}}}},showAssembleHub:{id:"showAssembleHub",meta:{component:({sendEvent:e})=>{const t=(0,A.am)(ce.slice(-2),10),[o,r]=(0,c.useState)(t[0].progress);return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)(w.Loader,{children:[(0,x.jsx)(w.Loader.Sequence,{interval:2e3/(t.length-1),shouldLoop:!1,onChange:e=>{setTimeout((()=>{r(t[e].progress)}),0)},children:t.map(((e,t)=>(0,x.jsxs)(w.Loader.Layout,{children:[(0,x.jsx)(w.Loader.Illustration,{children:e.image}),(0,x.jsx)(w.Loader.Title,{children:e.title})]},t)))}),(0,x.jsx)(w.Loader.ProgressBar,{className:"smooth-transition",progress:o||0})]}),(0,x.jsx)(me,{sendEvent:e})]})}},entry:["redirectToAssemblerHub"]},showAssembleHubHomepage:{entry:[{type:"redirectToAssemblerHubSection",section:"homepage"}]},showAssembleHubTypography:{entry:[{type:"redirectToAssemblerHubSection",section:"typography"}]}}},{actions:Qe,services:Ue,guards:{hasStepInUrl:(e,t,{cond:o})=>{const{path:r=""}=(0,u.getQuery)();return r.split("/")[2]===o.step},isFontLibraryAvailable:e=>e.isFontLibraryAvailable,hasFontInstallInUrl:()=>{const{path:e=""}=(0,u.getQuery)(),t=e.split("/");return"design"===t[2]&&"install-fonts"===t[3]},hasPatternInstallInUrl:()=>{const{path:e=""}=(0,u.getQuery)(),t=e.split("/");return"design"===t[2]&&"install-patterns"===t[3]}}});var We=o(42843),Ge=o(89677);const He=({parentMachine:e,parentContext:t})=>{var o,r;const s=(0,g.useSelect)((e=>e("core").getCurrentTheme()),[]),n=s?.is_block_theme,{versionEnabled:a}=(0,Ge.D)(),[,i,c]=(0,l.z)(Re,{devTools:"V4"===a,parent:e,context:{...Re.context,isFontLibraryAvailable:null!==(o=t?.isFontLibraryAvailable)&&void 0!==o&&o,isPTKPatternsAPIAvailable:null!==(r=t?.isPTKPatternsAPIAvailable)&&void 0!==r&&r,isBlockTheme:n}}),u=(0,m.d)(c,(e=>{var t;return(0,We.Q)(null!==(t=e?.meta)&&void 0!==t?t:void 0)})),d=u?.component;return(0,x.jsx)("div",{className:"woocommerce-design-without-ai__container",children:d?(0,x.jsx)(d,{sendEvent:i}):(0,x.jsx)("div",{})})},Ze=({parentMachine:e,context:t})=>(0,x.jsx)(He,{parentMachine:e,parentContext:t});var Ve=o(71628),Je=o(70351),Ke=o(53031),qe=o(41233),Xe=o(43656),$e=o(87214),et=o(16480),tt=o(52619),ot=o(4921),rt=o(18890),st=o(34465),nt=o(12467),at=o(93090),it=o(71718),ct=o(95831),lt=o(2923),mt=o(33396),ut=o(51609);const dt=e=>"string"==typeof e&&e!==decodeURIComponent(e),gt=({fontFamilies:e,iframeInstance:t,onLoad:o})=>{const{site:r,currentTheme:s}=(0,g.useSelect)((e=>({site:e(a.store).getSite(),currentTheme:e(a.store).getCurrentTheme()})),[]);return(0,c.useEffect)((()=>{if(!Array.isArray(e)||!r)return;const n=r?.url+"/wp-content/themes/"+s?.stylesheet;e.forEach((async e=>{e.fontFace?.forEach((async e=>{const r=`url(${((e,t)=>{if(e){if(e.startsWith("file:.")&&t){const o=e.replace("file:.",t);return dt(o)?o:encodeURI(o)}return dt(e)?e:encodeURI(e)}})(Array.isArray(e.src)?e.src[0]:e.src,n)})`,s=new FontFace(e.fontFamily,r,{style:e.fontStyle,weight:e.fontWeight}),a=await s.load();document.fonts.add(a),t&&t.contentDocument?.fonts.add(a),o&&o()}))}))}),[s?.stylesheet,e,t,o,r]),(0,x.jsx)(x.Fragment,{})},{useGlobalSetting:pt}=(0,Je.T)(ke.privateApis),yt=()=>{var e;const[t]=pt("typography.fontFamilies"),[o]=pt("typography.fontFamilies",void 0,"base"),r=(0,c.useMemo)((()=>document.querySelector(".block-editor-block-preview__content iframe")),[]);return(0,x.jsx)(x.Fragment,{children:(0,x.jsx)(gt,{fontFamilies:[...null!==(e=t.custom)&&void 0!==e?e:[],...o.theme],iframeInstance:r})})},ht=(e,{selectBlockByClientId:t})=>{const o=(e=>{for(;e&&e.nodeType!==e.ELEMENT_NODE;)e=e.parentNode;if(!e)return;const t=e.closest("[data-is-parent-block='true'], header, footer");return t?t.id.slice(6):void 0})(e.target);if(o)return t(o,null),o},wt="enable-click",Mt=e=>{e.setAttribute("inert","true")},kt=e=>{e.removeAttribute("inert")};let bt=function(e){return e.VISIBLE="VISIBLE",e.HIDDEN="HIDDEN",e}({});const ft=(e=0,t=0)=>()=>({width:0,height:0,top:t,right:e,bottom:t,left:e});let jt=null,Nt=null;var It=o(61208);const _t=(0,c.createContext)({selectedBlockRef:null,setSelectedBlockRef:()=>{}}),xt=({children:e})=>{const[t,o]=(0,c.useState)(null);return(0,x.jsx)(_t.Provider,{value:{selectedBlockRef:t,setSelectedBlockRef:e=>{o(e)}},children:e})},Dt=(e,t)=>{const[o,r,s]=(0,a.useEntityBlockEditor)("postType",e,{id:t});return[null!=o?o:[],r,s]},Tt=e=>"core/cover"===e.name&&e.attributes.url.includes("music-black-and-white-white-photography.jpg"),vt=e=>"Featured Category Cover Image"===e.attributes?.metadata?.name,zt=(e,t)=>{const o=e.filter(Tt);o&&((e,t)=>{const o=e.map((e=>e?.innerBlocks[0].innerBlocks.find((e=>"core/buttons"===e.name)))).map((e=>e?.innerBlocks[0])).filter(Boolean);o&&t(o)})(o,t);const r=e.filter(vt);return r&&((e,t)=>{t(e.map((e=>e.innerBlocks.find((e=>"core/cover"===e.name)))).map((e=>e?.innerBlocks.find((e=>"core/buttons"===e.name)))).map((e=>e?.innerBlocks[0])))})(r,t),e},Et={color:{background:"#ffffff",text:"#000000"}};var St=o(66087);const{GlobalStylesContext:At}=(0,Je.T)(ke.privateApis),Lt=()=>{const{user:e}=(0,c.useContext)(At);return(0,c.useMemo)((()=>(0,St.isEqual)(de[0].settings.color,e.settings.color)),[e])},Ct=()=>{const e=Lt(),t=(0,g.useSelect)((e=>e(a.store).getDefaultTemplateId({slug:"home"})),[]),[o]=Dt("wp_template",t||""),r=(0,c.useRef)(null),{insertBlocks:s}=(0,g.useDispatch)(ke.store),n=(0,c.useMemo)((()=>o.findLastIndex((e=>"core/template-part"===e.name))),[o]);return{insertPattern:(0,c.useCallback)((t=>{const o=(0,Je.T)((0,g.select)(ke.store)).__experimentalGetParsedPattern(t.name).blocks.map((e=>(0,we.cloneBlock)(e)));if(e){const e=zt(o,(e=>{e.forEach((e=>e.attributes.style=Et))}));s(e,n,void 0,!1),r.current=e[0].clientId}else{const e=zt(o,(e=>{e.forEach((e=>e.attributes.style={}))}));s(e,n,void 0,!1),r.current=e[0].clientId}(0,L.s)("customize_your_store_assembler_pattern_sidebar_click",{pattern:t.name})}),[s,n,e]),insertedPattern:r}},{Provider:Ot}=j.Disabled.Context;let Ft;const Bt=2e3;function Pt({viewportWidth:e,containerWidth:t,settings:o,additionalStyles:r,isScrollable:s=!0,autoScale:n=!0,isPatternPreview:a,CustomIframeComponent:i=ke.__unstableIframe}){const[l,m]=(0,c.useState)(null),{setLogoBlockIds:d,logoBlockIds:p}=(0,c.useContext)(be);e||(e=t);const[y,h]=(0,c.useState)(null),[w,M,b,f]=(()=>{const[e,t]=(0,ut.useState)(bt.HIDDEN),o={getBoundingClientRect:ft()},[r,s]=(0,ut.useState)(o),n=()=>{t(bt.HIDDEN),jt=null,Nt=null};return[e,r,({event:o,clickedBlockClientId:r,hoveredBlockClientId:a})=>{const i=window.document.querySelector('.woocommerce-customize-store-assembler > iframe[name="editor-canvas"]');if(o.target.classList.contains(wt))n();else if(jt=null===r?jt:r,Nt=null===a?Nt:a,jt!==Nt)t(bt.HIDDEN),jt=null;else{e===bt.HIDDEN&&t(bt.VISIBLE);const r=i.getBoundingClientRect(),n={getBoundingClientRect:ft(o.clientX+r.left,o.clientY+r.top+20)};s(n)}},n,t]})(),{selectBlock:N,setBlockEditingMode:I}=(0,g.useDispatch)(ke.store),{getBlockParents:_}=(0,g.useSelect)(ke.store),{setSelectedBlockRef:D}=(0,c.useContext)(_t),T=(0,g.useSelect)((e=>{const t=e(ke.store).getSelectedBlock();return t?.clientId}),[]);(0,c.useEffect)((()=>{if(T&&y){const e=y.querySelector(`#block-${T}`);if(!e)return;const t=new MutationObserver((()=>{D(e)}));return t.observe(e,{attributes:!0}),()=>{t.disconnect()}}}),[y,T,D]);const v=(0,c.useMemo)((()=>!s&&o.styles?[...o.styles,{css:"body{height:auto;overflow:hidden;border:none;padding:0;}",__unstableType:"presets"}]:o.styles),[o.styles,s]),z=t/e,E=l?t/(l*z):0;Ft=Ft||(0,c.memo)(ke.BlockList);const S=(0,c.useContext)(It.n),A=(0,u.useQuery)(),{insertPatternByName:L}=(()=>{const{blockPatterns:e,isLoading:t}=Ne(),{insertPattern:o}=Ct();return{insertPatternByName:r=>{if(t)return;const s=e.find((e=>e.name===r));s&&o(s)},isLoading:t}})();return(({documentElement:e,autoScale:t,isPatternPreview:o,logoBlockIds:r,query:s},{selectBlockOnHover:n,selectBlock:a,getBlockParents:i,setBlockEditingMode:l,updatePopoverPosition:m,setLogoBlockIds:u,setContentHeight:d,hidePopover:g,insertPatternByName:p})=>{(0,c.useEffect)((()=>{const r=[],c=[];if(!e)return;if(o){const o=((e,t,{setContentHeight:o})=>{const r=new window.MutationObserver((()=>{if(t){const t=e.ownerDocument.body.querySelector(".is-root-container");o(t?t.clientHeight:null)}}));return r.observe(e,{attributes:!0,characterData:!1,subtree:!0,childList:!0}),r})(e,t,{setContentHeight:d});r.push(o)}(e=>{const t=e.ownerDocument.documentElement;t.classList.add("block-editor-block-preview__content-iframe"),t.style.position="absolute",t.style.width="100%";const o="enable-click-styles";if(!e.ownerDocument.head.querySelector(`#${o}`)){const t=e.ownerDocument.createElement("style");t.setAttribute("type","text/css"),t.setAttribute("id",o),t.innerHTML=`\n\t\t\t.${wt}[data-type="core/button"]:hover {\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\t\t\t.${wt}:focus::after,\n\t\t\t.${wt}.is-selected::after {\n\t\t\t\tcontent: none !important;\n\t\t\t}\n\t\t`,e.ownerDocument.head.appendChild(t)}e.style.boxSizing="border-box",e.style.position="absolute",e.style.width="100%"})(e);const y=(({documentElement:e},{setLogoBlockIds:t})=>{const o=new window.MutationObserver((()=>{const o=e.querySelectorAll(".wp-block-site-logo"),r=Array.from(o).map((e=>e.getAttribute("data-block"))).filter(Boolean);t(r)}));return o.observe(e,{subtree:!0,childList:!0}),o})({autoScale:t,documentElement:e},{setLogoBlockIds:u});if(r.push(y),Le()&&!o){const t=((e,{hidePopover:t,selectBlock:o})=>{const r=e=>{(e.clientX<0||e.clientY<0)&&o(""),t()};return e&&e.addEventListener("mouseleave",r),()=>{e&&e.removeEventListener("mouseleave",r)}})(e,{hidePopover:g,selectBlock:a}),o=((e,{selectBlock:t,selectBlockOnHover:o,getBlockParents:r,setBlockEditingMode:s,updatePopoverPosition:n})=>{const a=e.ownerDocument.body,i=e=>{const a=o(e,{selectBlockByClientId:t,getBlockParents:r,setBlockEditingMode:s});n({event:e,hoveredBlockClientId:null,clickedBlockClientId:a}),e.target.focus()},c=e=>{const s=o(e,{selectBlockByClientId:t,getBlockParents:r,setBlockEditingMode:()=>{}});s&&n({event:e,hoveredBlockClientId:s,clickedBlockClientId:null})};return a.addEventListener("click",i),a.addEventListener("mousemove",c),()=>{a.removeEventListener("click",i),a.removeEventListener("mousemove",c)}})(e,{selectBlock:a,selectBlockOnHover:n,getBlockParents:i,setBlockEditingMode:l,updatePopoverPosition:m}),u=(e=>{const t=e.ownerDocument.body,o=new window.MutationObserver((()=>{var o;const r=null!==(o=t.getElementsByClassName("block-editor-block-list__layout")[0]?.children)&&void 0!==o?o:[];for(const e of r)e.setAttribute("data-is-parent-block","true");for(const t of e.querySelectorAll("[data-is-parent-block='true'] *"))t.classList.contains(wt)||Mt(t)}));return o.observe(t,{childList:!0}),o})(e);r.push(u);const d=((e,t)=>{const o=e.ownerDocument.body,r={"/customize-store/assembler-hub/header":"header[data-type='core/template-part']","/customize-store/assembler-hub/footer":"footer[data-type='core/template-part']","/customize-store/assembler-hub/homepage":"[data-is-parent-block='true']:not([data-type='core/template-part']):not(.disable-click)"},s=t.includes("/customize-store/assembler-hub/homepage")?r["/customize-store/assembler-hub/homepage"]:r[t],n=()=>{for(const t of e.querySelectorAll("[data-is-parent-block='true']"))t.classList.contains(wt)||Mt(t);for(const t of e.querySelectorAll(s))kt(t)};n();const a=new window.MutationObserver(n);return a.observe(o,{childList:!0}),a})(e,s?.path);r.push(d),c.push(o),c.push(t)}const h=((e,t)=>{const o=()=>{t("woocommerce-blocks/centered-content-with-image-below")},r=e.querySelector(".no-blocks-insert-pattern-button");return r&&r.addEventListener("click",o),()=>{r&&r.removeEventListener("click",o)}})(e,p);return c.push(h),()=>{r.forEach((e=>e.disconnect())),c.forEach((e=>e()))}}),[e,r,s])})({documentElement:y,autoScale:n,isPatternPreview:a,contentHeight:l,logoBlockIds:p,query:A},{hidePopover:f,selectBlockOnHover:ht,selectBlock:N,getBlockParents:_,setBlockEditingMode:I,updatePopoverPosition:b,setLogoBlockIds:d,setContentHeight:m,insertPatternByName:L}),(0,x.jsxs)(x.Fragment,{children:[!a&&M&&w===bt.VISIBLE&&!S&&(0,x.jsx)(j.Popover,{anchor:M,as:"div",variant:"unstyled",className:"components-tooltip woocommerce-customize-store_popover-tooltip",children:(0,x.jsx)("span",{children:(0,k.__)("You can edit your content later in the Editor","woocommerce")})}),(0,x.jsx)(Ot,{value:!0,children:(0,x.jsx)("div",{className:(0,ot.A)("block-editor-block-preview__content",{"woocommerce-customize-store-assembler":!a}),style:n?{transform:`scale(${z})`,aspectRatio:E,maxHeight:null!==l&&l>Bt?Bt*z:void 0}:{},children:(0,x.jsxs)(i,{"aria-hidden":!0,scrolling:s?"yes":"no",tabIndex:-1,canEnableZoomOutView:!0,readonly:!Le(),style:n?{position:"absolute",width:e,pointerEvents:"none",height:l,maxHeight:Bt}:{},contentRef:e=>{if(!e||null!==y)return;const t=e.ownerDocument.documentElement;h(t)},children:[(0,x.jsx)(ke.__unstableEditorStyles,{styles:v}),(0,x.jsx)("style",{children:`\n\t\t\t\t\t\t.block-editor-block-list__block::before,\n\t\t\t\t\t\t.has-child-selected > .is-selected::after,\n\t\t\t\t\t\t.is-hovered:not(.is-selected.is-hovered)::after,\n\t\t\t\t\t\t.block-list-appender {\n\t\t\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.block-editor-block-list__block.is-selected {\n\t\t\t\t\t\t\tbox-shadow: none !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.block-editor-rich-text__editable {\n\t\t\t\t\t\t\tpointer-events: none !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.wp-block-site-title .block-editor-rich-text__editable {\n\t\t\t\t\t\t\tpointer-events: all !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.wp-block-navigation-item .wp-block-navigation-item__content,\n\t\t\t\t\t\t.wp-block-navigation .wp-block-pages-list__item__link {\n\t\t\t\t\t\t\tpointer-events: all !important;\n\t\t\t\t\t\t\tcursor: pointer !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.components-resizable-box__handle {\n\t\t\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tfooter.is-selected::after,\n\t\t\t\t\t\theader.is-selected::after {\n\t\t\t\t\t\t\toutline-color: var(--wp-admin-theme-color) !important;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\theader.is-selected::after {\n\t\t\t\t\t\t    border-top-left-radius: 20px;\n\t\t\t\t\t    }\n\n\t\t\t\t\t\tfooter.is-selected::after {\n\t\t\t\t\t\t    border-bottom-left-radius: 20px;\n\t\t\t\t\t    }\n\n\t\t\t\t\t\t${r}\n\t\t\t\t\t`}),(0,x.jsx)(Ft,{renderAppender:!1}),(0,x.jsx)(yt,{})]})})})]})}const Ut=e=>{const[t,{width:o}]=(0,_.useResizeObserver)();return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("div",{style:{position:"relative",width:"100%",height:0},children:t}),(0,x.jsx)("div",{className:"auto-block-preview__container",children:!!o&&(0,x.jsx)(Pt,{...e,containerWidth:o})})]})},Qt={intro:{label:(0,k.__)("Intro","woocommerce"),description:(0,k.__)("Welcome shoppers to your store with one of our introductory patterns.","woocommerce")},"featured-selling":{label:(0,k.__)("Featured selling","woocommerce"),description:(0,k.__)("Put the spotlight on one or more of your products or product categories.","woocommerce")},about:{label:(0,k.__)("About","woocommerce"),description:(0,k.__)("Show your shoppers what’s special about your business.","woocommerce")},services:{label:(0,k.__)("Services","woocommerce"),description:(0,k.__)("Share information on any services that your business can provide.","woocommerce")},reviews:{label:(0,k.__)("Reviews","woocommerce"),description:(0,k.__)("Encourage sales by sharing positive feedback from happy shoppers.","woocommerce")},"social-media":{label:(0,k.__)("Social media","woocommerce"),description:(0,k.__)("Promote your social channels and give shoppers a way to see your latest products and news.","woocommerce")}},Yt=(0,x.jsx)(j.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/SVG",children:(0,x.jsx)(j.Path,{d:"M17.192 6.75L15.47 5.03l1.06-1.06 3.537 3.53-3.537 3.53-1.06-1.06 1.723-1.72h-3.19c-.602 0-.993.202-1.28.498-.309.319-.538.792-.695 1.383-.13.488-.222 1.023-.296 1.508-.034.664-.116 1.413-.303 2.117-.193.721-.513 1.467-1.068 2.04-.575.594-1.359.954-2.357.954H4v-1.5h4.003c.601 0 .993-.202 1.28-.498.308-.319.538-.792.695-1.383.149-.557.216-1.093.288-1.662l.039-.31a9.653 9.653 0 0 1 .272-1.653c.193-.722.513-1.467 1.067-2.04.576-.594 1.36-.954 2.358-.954h3.19zM8.004 6.75c.8 0 1.46.23 1.988.628a6.24 6.24 0 0 0-.684 1.396 1.725 1.725 0 0 0-.024-.026c-.287-.296-.679-.498-1.28-.498H4v-1.5h4.003zM12.699 14.726c-.161.459-.38.94-.684 1.396.527.397 1.188.628 1.988.628h3.19l-1.722 1.72 1.06 1.06L20.067 16l-3.537-3.53-1.06 1.06 1.723 1.72h-3.19c-.602 0-.993-.202-1.28-.498a1.96 1.96 0 0 1-.024-.026z"})});function Rt({clientId:e}){const{category:t,patternName:o}=(0,g.useSelect)((t=>{const{getBlockAttributes:o}=t(ke.store),r=o(e),s=r?.metadata?.categories,n=Object.keys(Qt).find((e=>s?.includes(e))),a=r?.metadata?.patternName;return{category:n,patternName:a}}),[e]),{patterns:r}=Ie(t),{replaceBlocks:s}=(0,g.useDispatch)(ke.store);if(r.length<2)return null;const n=(e=>{for(const t in Qt)if(e.includes(t))return Qt[t].label})([t]);return(0,x.jsx)(j.ToolbarGroup,{className:"woocommerce-customize-your-store-toolbar-shuffle-container",children:(0,x.jsx)(j.Button,{icon:Yt,label:(0,k.__)("Shuffle","woocommerce"),onClick:()=>{const n=((e,t)=>{const o=e.length,r=e.findIndex((({name:e})=>e===t)),s=Math.floor(Math.random()*o);return s!==r?e[s]:r===s?0===s?e[1]:s===o?e[0]:e[s-1]:e[0]})(r,o);n.blocks[0].attributes={...n.blocks[0].attributes,metadata:{...n.blocks[0].attributes.metadata,categories:[t],patternName:n.name}},s(e,n.blocks),(0,L.s)("customize_your_store_assembler_pattern_shuffle_click",{category:t,pattern:n.name})},children:n&&(0,x.jsx)("span",{children:(0,St.capitalize)(n)})})})}var Wt=o(39194);function Gt({clientId:e,currentBlockName:t,nextBlockClientId:o}){const{removeBlock:r,selectBlock:s}=(0,g.useDispatch)(ke.store);return(0,x.jsx)(j.ToolbarGroup,{children:(0,x.jsx)(j.ToolbarButton,{showTooltip:!0,label:(0,k.__)("Delete","woocommerce"),icon:Wt.A,onClick:()=>{r(e),o&&s(o),(0,L.s)("customize_your_store_assembler_pattern_delete_click",{pattern:t})}})})}let Ht;const Zt=e=>void 0!==e.find((e=>e.clientId===Ht)),Vt=()=>{var e;const[t,o]=(0,c.useState)(!1),{currentBlock:r,nextBlock:s,previousBlock:n,allBlocks:a}=(0,g.useSelect)((e=>{const{getSelectedBlockClientId:t,getNextBlockClientId:o,getPreviousBlockClientId:r,getBlocksByClientId:s,getBlocks:n}=e(ke.store),a=t(),i=o(),c=r(),[l]=s(a?[a]:[]),[m]=s(i?[i]:[]),[u]=s(c?[c]:[]);return{currentBlock:l,nextBlock:m,previousBlock:u,allBlocks:n()}}),[]),i=(0,u.useQuery)();(0,c.useEffect)((()=>{const e=i.path;e&&o((e=>e.includes("/customize-store/assembler-hub/homepage"))(e))}),[i]);const l=null!==(e=r?.clientId)&&void 0!==e?e:null,{isBlockMoverUpButtonDisabled:m,isBlockMoverDownButtonDisabled:d}=(0,c.useMemo)((()=>{const e="core/template-part"===n?.name,t="core/template-part"===s?.name;return{isBlockMoverUpButtonDisabled:e||a[1]?.clientId===l,isBlockMoverDownButtonDisabled:t||a[a.length-2]?.clientId===l}}),[a,s?.name,n?.name,l]),p=Zt(a),y=(0,c.useMemo)((()=>{const e=a.find((({clientId:e})=>e===l));return"core/template-part"===e?.name}),[a,l]),{selectedBlockRef:h}=(0,c.useContext)(_t),w=(0,c.useRef)(null),M=(0,c.useMemo)((()=>{if(h&&l)return{getBoundingClientRect(){const{top:e,width:t,height:o}=h.getBoundingClientRect(),r=window.document.querySelector('.woocommerce-customize-store-assembler > iframe[name="editor-canvas"]')?.getBoundingClientRect();return r?new window.DOMRect(r?.left+10,Math.max(e+70+r.top,100),t,o):new window.DOMRect(0,0,0,0)}}}),[h,l]);return t&&l&&!p&&!y&&M?(0,x.jsx)(j.Popover,{as:"div",animate:!1,className:"components-tooltip woocommerce-customize-store_block-toolbar-popover",variant:"unstyled",resize:!1,flip:!1,shift:!0,anchor:M,placement:"top-start",ref:w,children:(0,x.jsx)("div",{className:"woocommerce-customize-store-block-toolbar",children:(0,x.jsx)(j.Toolbar,{label:"Options",children:(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(j.ToolbarGroup,{children:(0,x.jsx)(ke.BlockMover,{clientIds:[l],isBlockMoverUpButtonDisabled:m,isBlockMoverDownButtonDisabled:d})}),(0,x.jsx)(Rt,{clientId:l}),(0,x.jsx)(Gt,{clientId:l,currentBlockName:r?.name,nextBlockClientId:s?.clientId})]})})})}):null},Jt=(0,c.memo)((({blocks:e,settings:t,useSubRegistry:o=!0,onChange:r,isPatternPreview:s,...n})=>{const a=Array.isArray(e)?e:[e],i=(0,c.useContext)(It.n);return(0,x.jsx)(x.Fragment,{children:(0,x.jsx)(ke.BlockEditorProvider,{value:a,settings:t,onChange:r,useSubRegistry:o,children:(0,x.jsxs)(xt,{children:[Le()&&!s&&!i&&(0,x.jsx)(Vt,{}),(0,x.jsx)(Ut,{isPatternPreview:s,settings:t,...n})]})})})}));function Kt({contentRef:e,children:t,tabIndex:o=0,scale:r=1,frameSize:s=0,expand:n=!1,readonly:a,forwardedRef:i,loadStyles:l=!0,loadScripts:m=!1,...u}){const[d,p]=(0,c.useState)(),[y,h]=(0,c.useState)([]),{resolvedAssets:w}=(0,g.useSelect)((e=>({resolvedAssets:e(ke.store).getSettings().__unstableResolvedAssets})),[]),{styles:M="",scripts:b=""}=w,[f,{height:N}]=(0,_.useResizeObserver)(),I=(0,_.useRefEffect)((e=>{function t(){const{contentDocument:t,ownerDocument:o}=e,{documentElement:r}=t;r.classList.add("block-editor-iframe__html"),h(Array.from(o?.body.classList).filter((e=>e.startsWith("admin-color-")||e.startsWith("post-type-")||"wp-embed-responsive"===e)))}return e._load=()=>{p(e.contentDocument)},e.addEventListener("load",t),()=>{delete e._load,e.removeEventListener("load",t)}}),[]),D=(0,_.useDisabled)({isDisabled:!a}),T=(0,_.useMergeRefs)([e,D]),v=`<!doctype html>\n<html>\n\t<head>\n\t\t<script>window.frameElement._load()<\/script>\n\t\t<style>html{height:auto!important;min-height:100%;}body{margin:0}</style>\n\t\t${l?M:""}\n\t\t${m?b:""}\n\t</head>\n\t<body>\n\t\t<script>document.currentScript.parentElement.remove()<\/script>\n\t</body>\n</html>`,[z,E]=(0,c.useMemo)((()=>{const e=URL.createObjectURL(new window.Blob([v],{type:"text/html"}));return[e,()=>URL.revokeObjectURL(e)]}),[v]);(0,c.useEffect)((()=>E),[E]);const S=N*(1-r)/2;return(0,x.jsx)(x.Fragment,{children:(0,x.jsx)("iframe",{...u,style:{...u.style,height:n?N:u.style?.height,marginTop:1!==r?-S+s:u.style?.marginTop,marginBottom:1!==r?-S+s:u.style?.marginBottom,transform:1!==r?`scale( ${r} )`:u.style?.transform,transition:"all .3s"},ref:(0,_.useMergeRefs)([i,I]),tabIndex:o,src:z,title:(0,k.__)("Editor canvas","woocommerce"),name:"editor-canvas",children:d&&(0,c.createPortal)((0,x.jsxs)("body",{ref:T,className:(0,ot.A)("block-editor-iframe__body","editor-styles-wrapper",...y),children:[f,(0,x.jsx)(j.__experimentalStyleProvider,{document:d,children:t})]}),d.documentElement)})})}const qt=(0,c.forwardRef)((function(e,t){return(0,g.useSelect)((e=>e(ke.store).getSettings().__internalIsInitialized),[])?(0,x.jsx)(Kt,{...e,forwardedRef:t}):null})),Xt=(0,ut.memo)((({renderedBlocks:e,settings:t,additionalStyles:o,isScrollable:r,onChange:s})=>(0,x.jsx)("div",{className:"woocommerce-customize-store__block-editor",children:(0,x.jsx)("div",{className:"woocommerce-block-preview-container",children:(0,x.jsx)(Jt,{blocks:e,onChange:s,settings:t,additionalStyles:o,isScrollable:r,useSubRegistry:!1,autoScale:!1,setLogoBlockContext:!0,CustomIframeComponent:qt,isPatternPreview:!1})})}))),$t=(0,c.createContext)({highlightedBlockClientId:null,setHighlightedBlockClientId:e=>{},resetHighlightedBlockClientId:()=>{}}),eo=({children:e})=>{const[t,o]=(0,c.useState)(null);return(0,x.jsx)($t.Provider,{value:{highlightedBlockClientId:t,setHighlightedBlockClientId:o,resetHighlightedBlockClientId:()=>{o(null)}},children:e})},to=()=>{const e=(0,mt.A)(),t=(0,g.useSelect)((e=>e(a.store).getDefaultTemplateId({slug:"home"})),[]),{templateType:o}=(0,g.useSelect)((e=>{const{getEditedPostType:t}=(0,Je.T)(e($e.M));return{templateType:t()}}),[]),[r,,s]=Dt(o,t||""),n=(0,u.useQuery)(),{currentState:i}=(0,c.useContext)(Jr),l=((e,t="topDown",o=.2)=>{const[r,s]=(0,c.useState)(.05),n=(0,st.O)();return(0,c.useEffect)((()=>{let r=document.querySelector(e);const n="IFRAME"===r?.tagName;n&&(r=r.contentDocument);const a=()=>{if(!r)return;const e=n?r.documentElement:r,a=e.clientWidth>480?o:.05,i=e.scrollHeight-e.clientHeight,c=e.scrollTop,l=i*a;let m;m="bottomUp"===t?i/l-c/l:c/l,m=.1+.9*m,m=Math.max(.1,Math.min(m,1)),s(m)};return r&&r.addEventListener("scroll",a),()=>{r&&r.removeEventListener("scroll",a)}}),[e,t,o,n]),r})(".woocommerce-customize-store__block-editor iframe","/customize-store/assembler-hub/footer"===n.path?"bottomUp":"topDown"),{highlightedBlockClientId:m}=(0,c.useContext)($t),d=null!==m,p=d?`\n\t\t.wp-block.preview-opacity {\n\t\t\topacity: ${l};\n\t\t}\n\t`:"",y=r.map((e=>e.clientId)),{updateBlockAttributes:h}=(0,g.useDispatch)(ke.store),w=Lt();(0,c.useEffect)((()=>{zt(r,w?e=>{const t=e.map((({clientId:e})=>e));h(t,{style:Et,className:""})}:e=>{const t=e.map((({clientId:e})=>e));h(t,{style:{}})})}),[w,h]);const{insertBlock:M,removeBlock:b}=(0,g.useDispatch)(ke.store);(({blocks:e,createBlock:t,insertBlock:o,removeBlock:r})=>{(0,c.useEffect)((()=>{if(2===e.length&&e.every((e=>"core/template-part"===e.name))){const e=t("core/group",{__noBlocksPlaceholder:!0,className:wt,style:{dimensions:{minHeight:"60vh"},color:{background:"#FAFAFA"},spacing:{padding:{top:"40px",bottom:"40px"}}},layout:{type:"flex",orientation:"vertical",justifyContent:"center",verticalAlignment:"center"}},[t("core/image",{url:"data:image/png;base64,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",align:"center",className:wt}),t("core/group",{layout:{type:"constrained",contentSize:"350px"},className:wt},[t("core/paragraph",{className:wt,align:"center",fontFamily:"inter",style:{color:{text:"#2F2F2F"}},content:(0,k.__)("Add one or more of our homepage patterns to create a page that welcomes shoppers.","woocommerce")}),t("core/button",{align:"center",fontFamily:"inter",className:`is-style-outline ${wt} no-blocks-insert-pattern-button`,style:{border:{radius:"2px",color:"#007cba",width:"1px"},color:{text:"#007cba"}},text:(0,k.__)("Add patterns","woocommerce")})])]);o(e,1),Ht=e.clientId}e.length>3&&Ht&&r(Ht)}),[e,t,o,r])})({blocks:r,createBlock:we.createBlock,insertBlock:M,removeBlock:b}),(0,c.useEffect)((()=>{const{blockIdToHighlight:e,restOfBlockIds:t}=y.reduce(((e,t)=>d&&t!==m?{blockIdToHighlight:e.blockIdToHighlight,restOfBlockIds:[...e.restOfBlockIds,t]}:{blockIdToHighlight:t,restOfBlockIds:e.restOfBlockIds}),{blockIdToHighlight:null,restOfBlockIds:[]});h(e,{className:""}),h(t,{className:" preview-opacity"})}),[y,m,d,h]);const f=(0,c.useMemo)((()=>!("object"==typeof i&&"transitional"===i.transitionalScreen)),[i]);return(0,x.jsx)(Xt,{renderedBlocks:r,isScrollable:f,onChange:s,settings:e,additionalStyles:p})},oo=({isLoading:e})=>{const{context:t,hasPageContentFocus:o}=(0,g.useSelect)((e=>{const{getEditedPostContext:t,hasPageContentFocus:o}=(0,Je.T)(e($e.M));return{context:t(),hasPageContentFocus:o}}),[]),{setEditedPostContext:r}=(0,g.useDispatch)($e.M),s=(0,c.useMemo)((()=>{const{postType:e,postId:s,...n}=null!=t?t:{};return{...o?t:n,queryContext:[t?.queryContext||{page:1},e=>r({...t,queryContext:{...t?.queryContext,...e}})]}}),[o,t,r]);return(0,c.useEffect)((()=>{e||((0,A.j1)(),(0,L.s)("customize_your_store_assembler_hub_editor_loaded"))}),[e]),(0,x.jsxs)(x.Fragment,{children:[e?(0,x.jsx)(ct.A,{}):null,(0,x.jsx)(ke.BlockContextProvider,{value:s,children:(0,x.jsx)(at.Du,{enableRegionNavigation:!1,className:(0,ot.A)("woocommerce-customize-store__edit-site-editor","woocommerce-edit-site-editor__interface-skeleton",{"show-icon-labels":!1,"is-loading":e}),content:(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(lt.J,{}),(0,x.jsx)(to,{})]})})})]})};var ro=o(45260),so=o(61288);const no=({setOpenWarningModal:e,onExitClicked:t,classname:o="woocommerce-customize-store__design-change-warning-modal"})=>(0,x.jsxs)(j.Modal,{className:o,title:(0,k.__)("Are you sure you want to exit?","woocommerce"),onRequestClose:()=>e(!1),shouldCloseOnClickOutside:!1,children:[(0,x.jsx)("p",{children:(0,k.__)("You'll lose any changes you've made to your store's design and will start the process again.","woocommerce")}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__design-change-warning-modal-footer",children:[(0,x.jsx)(j.Button,{onClick:t,variant:"link",children:(0,k.__)("Exit and lose changes","woocommerce")}),(0,x.jsx)(j.Button,{onClick:()=>e(!1),variant:"primary",children:(0,k.__)("Continue designing","woocommerce")})]})]});var ao=o(38966);const{useLocation:io}=(0,Je.T)(Ve.privateApis),co=({isRoot:e,title:t,actions:o,meta:r,content:s,footer:n,description:a,backPath:i,onNavigateBackClick:l})=>{const[m,u]=(0,c.useState)(!1),d=io(),g=(0,j.__experimentalUseNavigator)(),p=(0,k.isRTL)()?ro.A:b.A;return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)(j.__experimentalVStack,{className:(0,ot.A)("woocommerce-edit-site-sidebar-navigation-screen__main",{"has-footer":!!n}),spacing:0,justify:"flex-start",children:[(0,x.jsxs)(j.__experimentalHStack,{spacing:4,alignment:"flex-start",className:"woocommerce-edit-site-sidebar-navigation-screen__title-icon",children:[!e&&(0,x.jsx)(so.A,{onClick:()=>{l?.();const e=null!=i?i:d.state?.backPath;e?g.goTo(e,{isBack:!0}):g.goToParent()},icon:p,label:(0,k.__)("Back","woocommerce"),showTooltip:!1}),e&&!(0,ao.ex)()&&(0,x.jsx)(so.A,{onClick:()=>{u(!0)},icon:p,label:(0,k.__)("Back","woocommerce"),showTooltip:!1}),(0,x.jsx)(j.__experimentalHeading,{className:"woocommerce-edit-site-sidebar-navigation-screen__title",style:(0,ao.ex)()?{padding:"0 16px"}:{},color:"#e0e0e0",level:1,as:"h1",children:t}),o&&(0,x.jsx)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen__actions",children:o})]}),r&&(0,x.jsx)(x.Fragment,{children:(0,x.jsx)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen__meta",children:r})}),(0,x.jsxs)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen__content",children:[a&&(0,x.jsx)("p",{className:"woocommerce-edit-site-sidebar-navigation-screen__description",children:a}),s]})]}),n&&(0,x.jsx)("footer",{className:"woocommerce-edit-site-sidebar-navigation-screen__footer",children:n}),m&&(0,x.jsx)(no,{setOpenWarningModal:u,onExitClicked:()=>{window.parent.__wcCustomizeStore.sendEventToIntroMachine({type:"GO_BACK_TO_DESIGN_WITHOUT_AI"})}})]})};var lo=o(48558);const{GlobalStylesContext:mo}=(0,Je.T)(ke.privateApis),uo=({variation:e,children:t})=>{const{base:o,user:r,setUserConfig:s}=(0,c.useContext)(mo),n=(0,c.useMemo)((()=>{var t,r;return{user:{settings:null!==(t=e.settings)&&void 0!==t?t:{},styles:null!==(r=e.styles)&&void 0!==r?r:{}},base:o,merged:(0,ue.n)(o,e),setUserConfig:()=>{}}}),[e,o]),a=()=>{if(e.settings.color&&r.settings.color&&r.settings.color.palette.hasCreatedOwnColors){delete r.settings.color.palette.hasCreatedOwnColors,delete r.styles.color;for(const e in r.styles.elements)r.styles.elements[e].color&&delete r.styles.elements[e].color}const t=((e,t)=>{if(e.settings.typography){delete t.typography;for(const e in t.elements)t.elements[e].typography&&delete t.elements[e].typography}return t})(e,r.styles),o=((e,t)=>("New - Neutral"===e.title&&delete t.blocks["core/button"],t))(e,t);s((()=>({settings:(0,ue.n)(r.settings,e.settings),styles:(0,ue.n)(o,e.styles)}))),e.settings.color?.palette&&(0,L.s)("customize_your_store_assembler_hub_color_palette_item_click",{item:e.title}),e.settings.typography&&(0,L.s)("customize_your_store_assembler_hub_typography_item_click",{item:e.title})},i=(0,c.useMemo)((()=>{if(e.settings.color)return(0,St.isEqual)(e.settings.color,r.settings.color);const{theme:t}=r.settings.typography.fontFamilies;return e.settings.typography?.fontFamilies.theme.every((({slug:e})=>t.some((({slug:t})=>t===e))))&&t.length===e.settings.typography?.fontFamilies.theme.length}),[r,e]);let l=e?.title;return e?.description&&(l=(0,k.sprintf)((0,k.__)("%1$s (%2$s)","woocommerce"),e?.title,e?.description)),(0,x.jsx)(ke.BlockEditorProvider,{onChange:St.noop,onInput:St.noop,settings:{},useSubRegistry:!0,children:(0,x.jsx)(mo.Provider,{value:n,children:(0,x.jsx)("div",{className:(0,ot.A)("woocommerce-customize-store_global-styles-variations_item",{"is-active":i}),role:"button",onClick:a,onKeyDown:e=>{e.keyCode===lo.ENTER&&(e.preventDefault(),a())},tabIndex:"0","aria-label":l,"aria-current":i,children:(0,x.jsx)("div",{className:"woocommerce-customize-store_global-styles-variations_item-preview",children:t})})})})},{useGlobalStylesOutput:go}=(0,Je.T)(ke.privateApis),po=({width:e,height:t,inlineCss:o,containerResizeListener:r,children:s,onFocusOut:n,iframeInstance:a,...i})=>{const[c]=go(),l=(0,ut.useMemo)((()=>c?[...c,...o?[{css:o,isGlobalStyles:!0}]:[],{css:"html{overflow:hidden}body{min-width: 0;padding: 0;border: none;transform:scale(1);}",isGlobalStyles:!0}]:c),[o,c]);return(0,x.jsxs)(qt,{ref:a,className:"global-styles-variation-container__iframe",style:{height:t,visibility:e?"visible":"hidden"},tabIndex:-1,loadStyles:!1,contentRef:(0,_.useRefEffect)((e=>{const t=e=>{e.stopImmediatePropagation(),n?.()};return e.addEventListener("focusout",t),()=>{e.removeEventListener("focusout",t)}}),[]),scrolling:"no",...i,children:[(0,x.jsx)(ke.__unstableEditorStyles,{styles:null!=l?l:[]}),r,s]})},{useGlobalSetting:yo,useGlobalStyle:ho}=(0,Je.T)(ke.privateApis),wo=({title:e})=>{const[t]=ho("typography.fontWeight"),[o="serif"]=ho("typography.fontFamily"),[r=o]=ho("elements.h1.typography.fontFamily"),[s=t]=ho("elements.h1.typography.fontWeight"),[n="black"]=ho("color.text"),[a=n]=ho("elements.h1.color.text"),[i="white"]=ho("color.background"),[c]=ho("color.gradient"),[l]=yo("color.palette.theme"),[m,{width:u}]=(0,_.useResizeObserver)(),d=[...new Set(l.map((({color:e})=>e)))],g=d.filter((e=>e!==i)).slice(0,2);return(0,x.jsx)(po,{width:u,height:44,containerResizeListener:m,children:(0,x.jsx)("div",{style:{height:u?44:0,width:"100%",background:null!=c?c:i,cursor:"pointer"},children:(0,x.jsx)("div",{style:{height:"100%",overflow:"hidden"},children:e?(0,x.jsx)(j.__experimentalHStack,{spacing:1.8875,justify:"center",style:{height:"100%",overflow:"hidden"},children:g.map(((e,t)=>(0,x.jsx)("div",{style:{height:16,width:16,background:e,borderRadius:8}},t)))}):(0,x.jsx)(j.__experimentalVStack,{spacing:3,justify:"center",style:{height:"100%",overflow:"hidden",padding:10,boxSizing:"border-box"},children:(0,x.jsx)("div",{style:{fontSize:40,fontFamily:r,color:a,fontWeight:s,lineHeight:"1em",textAlign:"center"},children:(0,k.__)("Default","woocommerce")})})})})})},Mo=()=>(0,x.jsx)(j.__experimentalGrid,{columns:3,className:"woocommerce-customize-store_color-palette-container",children:ge?.map(((e,t)=>(0,x.jsx)(uo,{variation:e,children:(0,x.jsx)(wo,{title:e?.title})},t)))}),{useGlobalStyle:ko,useGlobalSetting:bo,useSettingsForBlockElement:fo,ColorPanel:jo,GlobalStylesContext:No}=(0,Je.T)(ke.privateApis),Io=()=>{const{setUserConfig:e}=(0,c.useContext)(No),[t]=ko("",void 0,"user",{shouldDecodeEncode:!1}),[o,r]=ko("",void 0,"all",{shouldDecodeEncode:!1}),[s]=bo(""),n=fo(s);return(0,x.jsx)(jo,{inheritedValue:o,value:t,onChange:(0,St.debounce)((t=>{r({...t,blocks:{...t.blocks,"core/button":{color:{}},"core/heading":{color:{}}}}),e((e=>({...e,settings:(0,ue.n)(e.settings,{color:{palette:{hasCreatedOwnColors:!0}}})})))}),100),settings:n})},{useGlobalStyle:_o,useGlobalSetting:xo}=(0,Je.T)(ke.privateApis),Do={fontSize:"13vw",lineHeight:"20px",color:"#000000"},To=e=>e.split(",").map((e=>(e=e.trim()).startsWith('"')?`'${e}'`:e)).join(", "),vo=()=>{const[e]=xo("typography.fontFamilies.theme"),[t="serif"]=_o("typography.fontFamily"),[o="normal"]=_o("typography.fontStyle"),[r="-0.15px"]=_o("typography.letterSpacing"),[s=400]=_o("typography.fontWeight"),[n=t]=_o("elements.heading.typography.fontFamily"),[a=o]=_o("elements.heading.typography.fontStyle"),[i=s]=_o("elements.heading.typography.fontWeight"),[l=r]=_o("elements.heading.typography.letterSpacing"),[m,{width:u}]=(0,_.useResizeObserver)(),d=(0,_.useViewportMatch)("large"),g=d?106:74,p=u?u/(d?136:120):1,y=Math.ceil(g*p),[h,w]=(0,c.useState)(!1),M=t=>{const o=e.find((({fontFamily:e})=>e===t));return o?.name||o?.fontFamily||t},k=(0,c.useMemo)((()=>M(t)),[t,e]),b=(0,c.useMemo)((()=>M(n)),[n,e]),f=(0,c.useRef)(null);return(0,x.jsx)(po,{width:u,height:y,containerResizeListener:m,iframeInstance:f,children:(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("div",{style:{height:u?y:0,width:"100%",background:"white",cursor:"pointer"},children:(0,x.jsx)("div",{style:{height:"100%",overflow:"hidden",opacity:h?1:0},children:(0,x.jsx)(j.__experimentalHStack,{spacing:10*p,justify:"flex-start",style:{height:"100%",overflow:"hidden"},children:(0,x.jsxs)(j.__experimentalVStack,{spacing:1,style:{margin:"10px",width:"100%",textAlign:d?"center":"left"},children:[(0,x.jsx)("div",{"aria-label":b,style:{...Do,letterSpacing:l,fontWeight:i,fontFamily:To(n),fontStyle:a},children:b}),(0,x.jsx)("div",{"aria-label":k,style:{...Do,fontSize:"13px",letterSpacing:r,fontWeight:s,fontFamily:To(t),fontStyle:o},children:k})]})})})}),(0,x.jsx)(gt,{fontFamilies:e,onLoad:()=>w(!0),iframeInstance:f.current})]})})};let zo=function(e){return e.IDLE="IDLE",e.LOADING="LOADING",e.DONE="DONE",e}({});const Eo=(0,c.createContext)({optInFlowStatus:zo.IDLE,setOptInFlowStatus:()=>{}}),So=({children:e})=>{const t=(0,g.useSelect)((e=>"yes"===e(d.optionsStore).getOption("woocommerce_allow_tracking")),[]),[o,r]=(0,c.useState)(t?zo.DONE:zo.IDLE);return(0,x.jsx)(Eo.Provider,{value:{optInFlowStatus:o,setOptInFlowStatus:r},children:e})},Ao=()=>{const{useGlobalSetting:e}=(0,Je.T)(ke.privateApis),[t]=e("typography.fontFamilies.custom"),[o]=e("typography.fontFamilies",void 0,"base"),{context:r}=(0,c.useContext)(Jr),s=r.isFontLibraryAvailable,n=(0,g.useSelect)((e=>"yes"===e(d.optionsStore).getOption("woocommerce_allow_tracking")),[]),{optInFlowStatus:a}=(0,c.useContext)(Eo),i=(0,c.useMemo)((()=>{const e=he.map((e=>{const t=e.settings.typography.fontFamilies,r=o.theme.filter((e=>t.theme.some((t=>t.fontFamily===e.name))));return{...e,settings:{typography:{fontFamilies:{theme:r}}}}}));if(!n||!s||a!==zo.DONE)return e;const r=ye.map((e=>{var o;const r=e.settings.typography.fontFamilies,s=null!==(o=t?.filter((e=>r.theme.some((t=>t.slug===e.slug)))))&&void 0!==o?o:[];return{...e,settings:{typography:{fontFamilies:{theme:s}}}}}));return[...e,...r]}),[o.theme,t,s,a,n]);return a===zo.LOADING?(0,x.jsx)("div",{className:"woocommerce-customize-store_font-pairing-spinner-container",children:(0,x.jsx)(j.Spinner,{})}):(0,x.jsx)(j.__experimentalGrid,{columns:2,gap:3,className:"woocommerce-customize-store_font-pairing-container",style:{opacity:0,animation:"containerFadeIn 300ms ease-in-out forwards"},children:i.map(((e,t)=>(0,x.jsx)(uo,{variation:e,children:(0,x.jsx)(vo,{})},t)))})},{GlobalStylesContext:Lo}=(0,Je.T)(ke.privateApis),Co=()=>{const{user:e}=(0,c.useContext)(Lo),t=!(!e.settings.color||!e.settings.color.palette.hasCreatedOwnColors);return(0,x.jsxs)("div",{className:"woocommerce-customize-store_sidebar-color-content",style:{opacity:0,animation:"containerFadeIn 300ms ease-in-out forwards"},children:[(0,x.jsx)(Mo,{}),(0,x.jsx)(j.PanelBody,{className:"woocommerce-customize-store__color-panel-container",title:(0,k.__)("or create your own","woocommerce"),initialOpen:t,onToggle:function(e){(0,L.s)("customize_your_store_assembler_hub_color_palette_create_toggle",{open:e})},children:(0,x.jsx)(Io,{})})]})},Oo=({onNavigateBackClick:e})=>{const t=(0,k.__)("Choose your color palette","woocommerce"),o=(0,k.__)("Choose the color palette that best suits your brand. Want to change it? Create your custom color palette below, or update it later in Editor.","woocommerce");return(0,x.jsx)(co,{title:t,onNavigateBackClick:e,description:o,content:(0,x.jsx)(Co,{})})},Fo=({editorSelector:e,scrollDirection:t="bottom"})=>{const o=(0,st.O)(),r=(0,c.useCallback)((()=>{const o=document.querySelector(e);o&&o.contentWindow?.scrollTo({left:0,top:"bottom"===t&&o.contentDocument?.body.scrollHeight||0})}),[t,e]);return(0,c.useEffect)((()=>{o||r()}),[o,r]),{scroll:r}},Bo=(e=".woocommerce-edit-site-sidebar-navigation-screen__content .block-editor-block-patterns-list__item")=>{const[t,o]=(0,c.useState)();return(0,c.useEffect)((()=>{document.querySelectorAll(e).forEach((e=>{e.getAttribute("aria-label")===t?.title?e.classList.add("is-selected"):e.classList.remove("is-selected")}))}),[t,e]),{selectedPattern:t,setSelectedPattern:o}},Po=(e,t)=>{const o=t.attributes;return o.className&&o.className.includes("preview-opacity")&&(o.className=o.className.replaceAll(" preview-opacity","")),e.find((e=>e.blocks[0].attributes.className===t.attributes.className))},Uo=["woocommerce-blocks/footer-with-3-menus","woocommerce-blocks/footer-simple-menu","woocommerce-blocks/footer-large"],Qo=({onNavigateBackClick:e})=>{const{scroll:t}=Fo({editorSelector:".woocommerce-customize-store__block-editor iframe",scrollDirection:"bottom"}),{isLoading:o,patterns:r}=Ie("woo-commerce"),s=(0,g.useSelect)((e=>e(a.store).getDefaultTemplateId({slug:"home"})),[]),[n]=Dt("wp_template",s||""),[i,,l]=Dt("wp_template_part",Te),m=n.find((e=>"footer"===e.attributes.slug)),{setHighlightedBlockClientId:u,resetHighlightedBlockClientId:d}=(0,c.useContext)($t),{selectedPattern:p,setSelectedPattern:y}=Bo();(0,c.useEffect)((()=>{var e;u(null!==(e=m?.clientId)&&void 0!==e?e:null)}),[m?.clientId,u]);const h=(0,c.useMemo)((()=>r.filter((e=>Uo.includes(e.name))).sort(((e,t)=>Uo.indexOf(e.name)-Uo.indexOf(t.name)))),[r]);(0,c.useEffect)((()=>{if(p||!i.length||!h.length)return;const e=Po(h,i[i.length-1]);y(e)}),[i,h]);const w=(0,c.useCallback)(((e,o)=>{y(e),l([...i.slice(0,-1),o[0]],{selection:{}}),t()}),[i,l,y,t]),M=(0,k.__)("Choose your footer","woocommerce"),b=(0,k.__)("Select a footer from the options below. Your footer includes your site's secondary navigation and will be added to every page. You can continue customizing this via the Editor later.","woocommerce");return(0,x.jsx)(co,{title:M,onNavigateBackClick:()=>{d(),e()},description:b,content:(0,x.jsx)(x.Fragment,{children:(0,x.jsxs)("div",{className:"woocommerce-customize-store__sidebar-footer-content",children:[o&&(0,x.jsx)("span",{className:"components-placeholder__preview",children:(0,x.jsx)(j.Spinner,{})}),!o&&(0,x.jsx)(ke.__experimentalBlockPatternsList,{shownPatterns:h,blockPatterns:h,onClickPattern:w,label:"Footers",orientation:"vertical",isDraggable:!1,onHover:()=>{},showTitlesAsTooltip:!0})]})})})},Yo=["woocommerce-blocks/header-centered-menu","woocommerce-blocks/header-essential","woocommerce-blocks/header-minimal","woocommerce-blocks/header-large","woocommerce-blocks/header-distraction-free"],Ro=({onNavigateBackClick:e})=>{const{scroll:t}=Fo({editorSelector:".woocommerce-customize-store__block-editor iframe",scrollDirection:"top"}),{isLoading:o,patterns:r}=Ie("woo-commerce"),s=(0,g.useSelect)((e=>e(a.store).getDefaultTemplateId({slug:"home"})),[]),[n]=Dt("wp_template",s||""),[i,,l]=Dt("wp_template_part",De),m=n.find((e=>"header"===e.attributes.slug)),{setHighlightedBlockClientId:u,resetHighlightedBlockClientId:d}=(0,c.useContext)($t),{selectedPattern:p,setSelectedPattern:y}=Bo();(0,c.useEffect)((()=>{var e;u(null!==(e=m?.clientId)&&void 0!==e?e:null)}),[m?.clientId,u]);const h=(0,c.useMemo)((()=>r.filter((e=>Yo.includes(e.name))).sort(((e,t)=>Yo.indexOf(e.name)-Yo.indexOf(t.name)))),[r]);(0,c.useEffect)((()=>{if(p||!i.length||!h.length)return;const e=Po(h,i[0]);y(e)}),[i,h]);const w=(0,c.useCallback)(((e,o)=>{y(e),l([o[0],...i.slice(1)],{selection:{}}),t()}),[i,l,y,t]),M=(0,k.__)("Choose your header","woocommerce");return(0,x.jsx)(co,{title:M,onNavigateBackClick:()=>{d(),e()},description:(0,k.__)("Select a new header from the options below. Your header includes your site's navigation and will be added to every page. You can continue customizing this via the Editor.","woocommerce"),content:(0,x.jsx)(x.Fragment,{children:(0,x.jsxs)("div",{className:"woocommerce-customize-store__sidebar-header-content",children:[o&&(0,x.jsx)("span",{className:"components-placeholder__preview",children:(0,x.jsx)(j.Spinner,{})}),!o&&(0,x.jsx)(ke.__experimentalBlockPatternsList,{shownPatterns:h,blockPatterns:h,onClickPattern:w,label:"Headers",orientation:"vertical",isDraggable:!1,onHover:()=>{},showTitlesAsTooltip:!0})]})})})},Wo=({onNavigateBackClick:e})=>{const{scroll:t}=Fo({editorSelector:".woocommerce-customize-store__block-editor iframe",scrollDirection:"top"}),{isLoading:o,homeTemplates:r}=(()=>{const{blockPatterns:e,isLoading:t}=Ne(),o=(0,c.useMemo)((()=>Ae(e)),[e]);return{homeTemplates:(0,c.useMemo)((()=>{if(t)return{};const e=Ee;return Object.entries(e).reduce(((t,[r,s])=>(r in e&&(t[r]=Se(s.blocks,o)),t)),{})}),[t,o]),isLoading:t}})(),{selectedPattern:s,setSelectedPattern:n}=Bo(),i=(0,g.useSelect)((e=>e(a.store).getDefaultTemplateId({slug:"home"})),[]),[l,,m]=Dt("wp_template",i||""),u=(0,c.useCallback)(((e,o)=>{e!==s&&(n(e),m([l[0],...o,l[l.length-1]],{selection:{}}),t())}),[s,n,m,l,t]),d=(0,st.O)(),p=Lt(),y=(0,c.useMemo)((()=>Object.entries(r).map((([e,t])=>"template1"===e?{name:e,title:e,blocks:t.reduce(((e,t)=>{const o=(0,Je.T)((0,g.select)(ke.store)).__experimentalGetParsedPattern(t.name);if(!o)return e;if(!p){const t=zt(o.blocks,(e=>{e.forEach((e=>{e.attributes.style={}}))}));return[...e,...t]}const r=zt(o.blocks,(e=>{e.forEach((e=>{e.attributes.style=Et}))}));return[...e,...r]}),[]),blockTypes:[""],categories:[""],content:"",source:""}:{name:e,title:e,blocks:t.reduce(((e,t)=>{const o=(0,Je.T)((0,g.select)(ke.store)).__experimentalGetParsedPattern(t.name);return o?[...e,...o.blocks]:e}),[]),blockTypes:[""],categories:[""],content:"",source:""}))),[r,p]);(0,c.useEffect)((()=>{if(s||!l.length||!y.length||o||d)return;const e=y.find((e=>{const t=l.slice(1,l.length-1);return e.blocks.length===t.length&&t.every(((t,o)=>t.name===e.blocks[o].name))}));n(e)}),[l,y,o,d]);const h=(0,k.__)("Choose your homepage","woocommerce"),w=(0,k.__)("Create an engaging homepage by selecting one of our pre-designed layouts. You can continue customizing this page, including the content, later via the Editor.","woocommerce");return(0,x.jsx)(co,{title:h,onNavigateBackClick:e,description:w,content:(0,x.jsx)("div",{className:"woocommerce-customize-store__sidebar-homepage-content",children:(0,x.jsx)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen-patterns__group-homepage",children:o||d?(0,x.jsx)("span",{className:"components-placeholder__preview",children:(0,x.jsx)(j.Spinner,{})}):(0,x.jsx)(ke.__experimentalBlockPatternsList,{shownPatterns:y,blockPatterns:y,onClickPattern:u,label:"Homepage",orientation:"vertical",category:"homepage",isDraggable:!1,showTitlesAsTooltip:!1})})})})};var Go=o(59613),Ho=o(48443),Zo=o(25031),Vo=o(31667),Jo=o(31613),Ko=o(60649),qo=o(59783);let Xo=function(e){return e.Forward="forward",e.Back="back",e}({});const $o=(0,c.createContext)({navigate:()=>{}});function er({children:e}){const[t,o]=(0,c.useState)({direction:null}),r=(0,c.useCallback)((e=>{o({direction:e})}),[]),s=(0,ot.A)("woocommerce-customize-store-edit-site-sidebar__screen-wrapper",{"slide-from-left":"back"===t.direction,"slide-from-right":"forward"===t.direction});return(0,x.jsx)($o.Provider,{value:{navigate:r},children:(0,x.jsx)("div",{className:s,children:e})})}const tr=()=>{const{navigate:e}=(0,c.useContext)($o);return(0,x.jsx)(co,{isRoot:!0,title:(0,k.__)("Let's get creative","woocommerce"),description:(0,k.__)("Use our style and layout tools to customize the design of your store. Content and images can be added or changed via the Editor later.","woocommerce"),content:(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen-patterns__group-header",children:(0,x.jsx)(j.__experimentalHeading,{level:2,children:(0,k.__)("Style","woocommerce")})}),(0,x.jsxs)(j.__experimentalItemGroup,{children:[(0,x.jsx)(j.__experimentalNavigatorButton,{as:qo.A,path:"/customize-store/assembler-hub/logo",withChevron:!0,icon:Go.A,onClick:()=>{const t=(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub/logo",{});(0,u.navigateTo)({url:t}),e(Xo.Forward),(0,L.s)("customize_your_store_assembler_hub_sidebar_item_click",{item:"logo"})},children:(0,k.__)("Add your logo","woocommerce")}),(0,x.jsx)(j.__experimentalNavigatorButton,{as:qo.A,path:"/customize-store/assembler-hub/color-palette",withChevron:!0,icon:Ho.A,onClick:()=>{const t=(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub/color-palette",{});(0,u.navigateTo)({url:t}),e(Xo.Forward),(0,L.s)("customize_your_store_assembler_hub_sidebar_item_click",{item:"color-palette"})},children:(0,k.__)("Choose your color palette","woocommerce")}),(0,x.jsx)(j.__experimentalNavigatorButton,{as:qo.A,path:"/customize-store/assembler-hub/typography",withChevron:!0,icon:Zo.A,onClick:()=>{const t=(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub/typography",{});(0,u.navigateTo)({url:t}),e(Xo.Forward),(0,L.s)("customize_your_store_assembler_hub_sidebar_item_click",{item:"typography"})},children:(0,k.__)("Choose fonts","woocommerce")})]}),(0,x.jsx)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen-patterns__group-header",children:(0,x.jsx)(j.__experimentalHeading,{level:2,children:(0,k.__)("Layout","woocommerce")})}),(0,x.jsxs)(j.__experimentalItemGroup,{children:[(0,x.jsx)(j.__experimentalNavigatorButton,{as:qo.A,path:"/customize-store/assembler-hub/header",withChevron:!0,icon:Vo.A,onClick:()=>{const t=(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub/header",{});(0,u.navigateTo)({url:t}),e(Xo.Forward),(0,L.s)("customize_your_store_assembler_hub_sidebar_item_click",{item:"header"})},children:(0,k.__)("Choose your header","woocommerce")}),(0,x.jsx)(j.__experimentalNavigatorButton,{as:qo.A,path:"/customize-store/assembler-hub/homepage",withChevron:!0,icon:Jo.A,onClick:()=>{const t=Le()?(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub/homepage/intro",{}):(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub/homepage",{});(0,u.navigateTo)({url:t}),e(Xo.Forward),(0,L.s)("customize_your_store_assembler_hub_sidebar_item_click",{item:"home"})},children:(0,k.__)("Design your homepage","woocommerce")}),(0,x.jsx)(j.__experimentalNavigatorButton,{as:qo.A,path:"/customize-store/assembler-hub/footer",withChevron:!0,icon:Ko.A,onClick:()=>{const t=(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub/footer",{});(0,u.navigateTo)({url:t}),e(Xo.Forward),(0,L.s)("customize_your_store_assembler_hub_sidebar_item_click",{item:"footer"})},children:(0,k.__)("Choose your footer","woocommerce")})]})]})})};var or=o(56109);const rr=({onNavigateBackClick:e})=>{const{context:t}=(0,c.useContext)(Jr),o=t.isFontLibraryAvailable,r=(0,k.__)("Choose fonts","woocommerce"),s=(0,k.__)("Select the pair of fonts that best suits your brand. The larger font will be used for headings, and the smaller for supporting content. You can change your font at any time in Editor.","woocommerce"),n=(0,g.useSelect)((e=>e(d.optionsStore).getOption("woocommerce_allow_tracking")),[]),a="no"===n||!n;let i;i=a&&!o?(0,k.__)("Upgrade to the <WordPressLink>latest version of WordPress</WordPressLink> and <OptInModal>opt in to usage tracking</OptInModal> to get access to more fonts.","woocommerce"):a&&o?(0,k.__)("Opt in to <OptInModal>usage tracking</OptInModal> to get access to more fonts.","woocommerce"):n&&!o?(0,k.__)("Upgrade to the <WordPressLink>latest version of WordPress</WordPressLink> to get access to more fonts.","woocommerce"):"";const[l,m]=(0,c.useState)(!1),u=()=>m(!1),[y,h]=(0,c.useState)(!1),[w,M]=(0,c.useState)(!0);return(0,x.jsx)(co,{title:r,onNavigateBackClick:e,description:s,content:(0,x.jsxs)("div",{className:"woocommerce-customize-store_sidebar-typography-content",children:[o&&(0,x.jsx)(Ao,{}),i&&(0,x.jsxs)("div",{className:"woocommerce-customize-store_sidebar-typography-upgrade-notice",children:[(0,x.jsx)("h4",{children:(0,k.__)("Want more font pairings?","woocommerce")}),(0,x.jsx)("p",{children:(0,c.createInterpolateElement)(i,{WordPressLink:(0,x.jsx)(j.Button,{href:`${or.kY}update-core.php`,variant:"link"}),OptInModal:(0,x.jsx)(j.Button,{onClick:()=>{m(!0)},variant:"link"})})}),l&&(0,x.jsxs)(j.Modal,{className:"woocommerce-customize-store__opt-in-usage-tracking-modal",title:(0,k.__)("Access more fonts","woocommerce"),onRequestClose:u,shouldCloseOnClickOutside:!1,children:[(0,x.jsx)(j.CheckboxControl,{className:"core-profiler__checkbox",label:(0,f.A)({mixedString:(0,k.__)("More fonts are available! Opt in to connect your store and access the full font library, plus get more relevant content and a tailored store setup experience. Opting in will enable {{link}}usage tracking{{/link}}, which you can opt out of at any time via WooCommerce settings.","woocommerce"),components:{link:(0,x.jsx)(p.Link,{href:"https://woocommerce.com/usage-tracking?utm_medium=product",target:"_blank",type:"external"})}}),checked:w,onChange:M}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__design-change-warning-modal-footer",children:[(0,x.jsx)(j.Button,{onClick:()=>{(0,L.s)("customize_your_store_assembler_hub_skip_opt_in_usage_tracking"),u()},variant:"link",children:(0,k.__)("Cancel","woocommerce")}),(0,x.jsx)(j.Button,{onClick:async()=>{(0,L.s)("customize_your_store_assembler_hub_opt_in_usage_tracking"),await Pe(),u(),h(!1)},variant:"primary",disabled:!w,children:y?(0,x.jsx)(j.Spinner,{}):(0,k.__)("Opt in","woocommerce")})]})]})]})]})})};var sr=o(692);const nr=[{kind:"postType",name:"wp_navigation"}];let ar=!0;const ir=()=>{const e=(0,u.useQuery)(),{sendEvent:t}=(0,c.useContext)(Jr),[o,r]=(0,c.useState)(!1),s=(0,g.useSelect)((e=>e(a.store).getDefaultTemplateId({slug:"home"})),[]),[n]=Dt("wp_template",s||""),i=Zt(n),l=(0,st.O)(),{__unstableMarkLastChangeAsPersistent:m}=(0,g.useDispatch)(ke.store),{createErrorNotice:d}=(0,g.useDispatch)(sr.store),{dirtyEntityRecords:p,isDirty:y}=(0,Xe.useEntitiesSavedStatesIsDirty)(),{editEntityRecord:h,saveEditedEntityRecord:w,__experimentalSaveSpecifiedEntityEdits:M}=(0,g.useDispatch)(a.store),b=(0,c.useCallback)((async()=>{for(const{kind:e,name:t,key:o,property:r}of p)"root"===e&&"site"===t?await M("root","site",void 0,[r],void 0):(nr.some((o=>o.kind===e&&o.name===t))&&void 0!==o&&h(e,t,o,{status:"publish"}),await w(e,t,o,void 0),m())}),[p,h,w,M,m]),f="/customize-store/assembler-hub"===e.path;(0,c.useEffect)((()=>{l||(f?ar&&y&&(b(),ar=!1):ar=!1)}),[l,y,f,b]);return f?(0,x.jsx)(j.__experimentalHStack,{className:"woocommerce-edit-site-save-hub",alignment:"right",spacing:4,children:(0,x.jsx)(j.Button,{variant:"primary",onClick:async()=>{(0,L.s)("customize_your_store_assembler_hub_done_click"),r(!0);try{await b(),t("FINISH_CUSTOMIZATION")}catch(e){d(`${(0,k.__)("Saving failed.","woocommerce")} ${e}`),r(!1)}},className:"woocommerce-edit-site-save-hub__button",disabled:o||l||i,"aria-disabled":o,__next40pxDefaultSize:!0,children:o?(0,x.jsx)(j.Spinner,{}):(0,k.__)("Finish customizing","woocommerce")})}):null};var cr=o(24148),lr=o(26967),mr=o(38150),ur=o(63162),dr=o(79745);const gr=({shouldSyncIcon:e,setAttributes:t})=>{const{siteIconId:o,mediaUpload:r}=(0,g.useSelect)((e=>{const{canUser:t,getEditedEntityRecord:o}=e(a.store),r=t("update","settings")?o("root","site"):void 0,s=r?.site_icon;return{siteIconId:s,mediaUpload:e(ke.store).getSettings().mediaUpload}}),[]),{editEntityRecord:s}=(0,g.useDispatch)(a.store),n=e=>s("root","site",void 0,{site_icon:null!=e?e:null}),i=(t,o=!1)=>{(e||o)&&n(t),s("root","site",void 0,{site_logo:t})},c=(e,o=!1)=>{e&&(e.id||!e.url?(i(e.id,o),t({width:dr.$k})):i(void 0))},l=r=>{if(void 0===e){const e=!o;return t({shouldSyncIcon:e}),void c(r,e)}c(r)},{createErrorNotice:m}=(0,g.useDispatch)(sr.store),u=e=>{m(e,{type:"snackbar"})};return{onFilesDrop:e=>{r({allowedTypes:["image"],filesList:e,onFileChange([e]){(0,ur.isBlobURL)(e?.url)||l(e)},onError:u})},onInitialSelectLogo:l,setIcon:n,siteIconId:o,onRemoveLogo:()=>{i(null),t({width:void 0})}}},pr=({attributes:{width:e,isLink:t,shouldSyncIcon:o,align:r=""},canUserEdit:s,naturalWidth:n,naturalHeight:a,setAttributes:i,setIcon:c,logoId:l})=>{const m=(0,_.useViewportMatch)("medium"),u=!["wide","full"].includes(r)&&m,d=e||dr.$k,g=n/a,p=n<a?dr.x6:Math.ceil(dr.x6*g),y=2.5*dr.Q_;return(0,x.jsxs)("div",{className:"woocommerce-customize-store__sidebar-group",children:[(0,x.jsx)("div",{className:"woocommerce-customize-store__sidebar-group-header",children:(0,k.__)("Settings","woocommerce")}),(0,x.jsx)(j.RangeControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,k.__)("Image width","woocommerce"),onChange:e=>i({width:e}),min:p,max:dr.Q_,initialPosition:Math.min(dr.$k,y),value:d,disabled:!u}),(0,x.jsx)(j.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,k.__)("Link logo to homepage","woocommerce"),onChange:()=>{i({isLink:!t})},checked:t}),s&&(0,x.jsx)(x.Fragment,{children:(0,x.jsx)(j.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,k.__)("Use as site icon","woocommerce"),onChange:e=>{i({shouldSyncIcon:e}),c(e?l:void 0)},checked:!!o,help:(0,k.__)("Site icons are what you see in browser tabs, bookmark bars, and within the WordPress mobile apps.","woocommerce")})})]})},yr=({siteLogoId:e,attributes:t,setAttributes:o,mediaItemData:r,isLoading:s,canUserEdit:n})=>{const{alt_text:a,source_url:i}=r||{},{onFilesDrop:l,onInitialSelectLogo:m,setIcon:u}=gr({shouldSyncIcon:t.shouldSyncIcon,setAttributes:o}),[{naturalWidth:d,naturalHeight:g},p]=(0,c.useState)({});if(s)return(0,x.jsx)("span",{className:"components-placeholder__preview",children:(0,x.jsx)(j.Spinner,{})});function y(e){m(e),(0,L.s)("customize_your_store_assembler_hub_logo_select")}if(!i)return(0,x.jsx)(ke.MediaUploadCheck,{children:(0,x.jsx)(ke.MediaUpload,{onSelect:y,allowedTypes:dr.eN,render:({open:e})=>(0,x.jsxs)(j.Button,{variant:"link",onClick:()=>{e(),(0,L.s)("customize_your_store_assembler_hub_logo_add_click")},className:"block-library-site-logo__inspector-upload-container",children:[(0,x.jsx)("span",{children:(0,x.jsx)(cr.A,{icon:lr.A,size:20,className:"icon-control"})}),(0,x.jsx)(j.DropZone,{onFilesDrop:l})]})})});const h=(0,x.jsx)("div",{className:"woocommerce-customize-store__sidebar-logo-container",children:(0,x.jsx)("img",{className:"woocommerce-customize-store_custom-logo",src:i,alt:a,onLoad:e=>{p({naturalWidth:e.target.naturalWidth,naturalHeight:e.target.naturalHeight})}})});return g&&d?(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(ke.MediaUploadCheck,{children:(0,x.jsx)(ke.MediaUpload,{onSelect:y,allowedTypes:dr.eN,render:({open:e})=>(0,c.cloneElement)(h,{onClick(){e(),(0,L.s)("customize_your_store_assembler_hub_logo_edit_click")}})})}),!!i&&(0,x.jsx)(pr,{attributes:t,setAttributes:o,naturalWidth:d,naturalHeight:g,canUserEdit:n,setIcon:u,logoId:r?.id||e})]}):h},hr=({onNavigateBackClick:e})=>{const{logoBlockIds:t}=(0,c.useContext)(be),{attributes:o,isAttributesLoading:r}=fe(),{siteLogoId:s,canUserEdit:n,mediaItemData:i,isRequestingMediaItem:l}=(0,g.useSelect)((e=>{const{canUser:t,getEntityRecord:o,getEditedEntityRecord:r}=e(a.store),s=t("update","settings"),n=s?r("root","site"):void 0,i=o("root","__unstableBase"),c=s?n?.site_logo:i?.site_logo;return{siteLogoId:null!=c?c:"",canUserEdit:null!=s&&s,mediaItemData:c&&e(a.store).getMedia(c,{context:"view"}),isRequestingMediaItem:c&&!e(a.store).hasFinishedResolution("getMedia",[c,{context:"view"}])}}),[]),{updateBlockAttributes:m}=(0,g.useDispatch)(ke.store),u=e=>{t.length&&t.forEach((t=>m(t,e)))},{onInitialSelectLogo:d,onRemoveLogo:y}=gr({shouldSyncIcon:o.shouldSyncIcon,setAttributes:u}),h=void 0===s||l||r;return(0,x.jsx)(co,{title:(0,k.__)("Add your logo","woocommerce"),description:(0,k.__)("Ensure your store is on-brand by adding your logo. For best results, upload a SVG or PNG that's a minimum of 300px wide.","woocommerce"),onNavigateBackClick:e,content:(0,x.jsxs)("div",{className:"woocommerce-customize-store__sidebar-logo-content",children:[(0,x.jsxs)("div",{className:"woocommerce-customize-store__sidebar-group-header woocommerce-customize-store__logo-header-container",children:[(0,x.jsx)("span",{children:(0,k.__)("Logo","woocommerce")}),Boolean(s)&&(0,x.jsx)(j.DropdownMenu,{icon:mr.A,label:(0,k.__)("Options","woocommerce"),className:"woocommerce-customize-store__logo-dropdown-menu",popoverProps:{className:"woocommerce-customize-store__logo-dropdown-popover",placement:"bottom-end"},children:({onClose:e})=>(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(j.MenuGroup,{className:"woocommerce-customize-store__logo-menu-group",children:(0,x.jsx)(ke.MediaUploadCheck,{children:(0,x.jsx)(ke.MediaUpload,{onSelect:t=>{d(t),e(),(0,L.s)("customize_your_store_assembler_hub_logo_select")},allowedTypes:dr.eN,render:({open:e})=>(0,x.jsx)(j.MenuItem,{onClick:()=>{e(),(0,L.s)("customize_your_store_assembler_hub_logo_replace_click")},children:(0,k.__)("Replace","woocommerce")})})})}),(0,x.jsx)(j.MenuGroup,{className:"woocommerce-customize-store__logo-menu-group",children:(0,x.jsx)(j.MenuItem,{className:"woocommerce-customize-store__logo-menu-item-delete",onClick:()=>{e(),y(),(0,L.s)("customize_your_store_assembler_hub_logo_remove_click")},children:(0,k.__)("Delete","woocommerce")})})]})})]}),(0,x.jsx)(yr,{siteLogoId:s,attributes:o,setAttributes:u,canUserEdit:n,mediaItemData:i,isLoading:h}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__fiverr-cta-group",children:[(0,x.jsx)("strong",{children:(0,k.__)("DON'T HAVE A LOGO YET?","woocommerce")}),(0,x.jsx)("p",{children:(0,f.A)({mixedString:(0,k.__)("Build your brand by creating a memorable logo using {{link}}Fiverr{{/link}}.","woocommerce"),components:{link:(0,x.jsx)(p.Link,{href:"https://go.fiverr.com/visit/?bta=917527&brand=logomaker&landingPage=https%253A%252F%252Fwww.fiverr.com%252Flogo-maker%252Fwoo",target:"_blank",type:"external",rel:"noreferrer",onClick:()=>{(0,L.s)("customize_your_store_fiverr_logo_maker_cta_click")}})}})})]})]})})},wr=(e,t)=>{if(e?.includes(t))return!0},Mr=({onNavigateBackClick:e})=>{const{context:t}=(0,c.useContext)(Jr),o=z(),r=t.isPTKPatternsAPIAvailable,s=(0,g.useSelect)((e=>e(a.store).getDefaultTemplateId({slug:"home"})),[]),[n]=Dt("wp_template",s||""),i=(0,c.useMemo)((()=>{const e=Object.keys(Qt),t=e.reduce(((e,t)=>({...e,[t]:0})),{});return n.reduce(((t,o)=>{var r;const s=(null!==(r=o.attributes?.metadata?.categories)&&void 0!==r?r:[]).find((t=>e.includes(t)));return s?{...t,[s]:t[s]+1}:t}),t)}),[n]),{blockPatterns:l,isLoading:m,invalidateCache:d}=Ne(),y=l.filter((e=>!e.name.includes(je)&&!e.name.includes("woocommerce")&&"core"!==e.source&&"pattern-directory/featured"!==e.source&&"pattern-directory/theme"!==e.source&&"pattern-directory/core"!==e.source)),h=(0,c.useMemo)((()=>{let e;return o?e=(0,k.__)("Looks like we can't detect your network. Please double-check your internet connection and refresh the page.","woocommerce"):r?window.wcTracks?.isEnabled?m||0!==y.length||(e=(0,k.__)("Unfortunately, a technical issue is preventing more patterns from being displayed. Please <FetchPatterns>try again</FetchPatterns> later.","woocommerce")):e=(0,k.__)("Opt in to <OptInModal>usage tracking</OptInModal> to get access to more patterns.","woocommerce"):e=(0,k.__)("Unfortunately, we're experiencing some technical issues — please come back later to access more patterns.","woocommerce"),e}),[o,r,m,y.length]),[w,M]=(0,c.useState)(!1),b=()=>M(!1),[N,I]=(0,c.useState)(!0),[_,D]=(0,c.useState)(!1),T=(0,k.__)("Design your homepage","woocommerce"),v=(0,k.__)("Create an engaging homepage by adding and combining different patterns and layouts. You can continue customizing this page, including the content, later via the Editor.","woocommerce"),E=(0,u.useQuery)().path;return(0,x.jsx)(co,{title:T,onNavigateBackClick:e,description:v,content:(0,x.jsx)("div",{className:"woocommerce-customize-store__sidebar-homepage-content",children:(0,x.jsxs)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen-patterns__group-homepage",children:[Object.entries(Qt).map((([e,{label:t}],o)=>(0,x.jsx)(j.__experimentalItemGroup,{children:(0,x.jsx)(j.__experimentalNavigatorButton,{className:(0,ot.A)({"woocommerce-edit-site-sidebar-navigation-screen-patterns__group-homepage-item--active":wr(E,e)}),path:`/customize-store/assembler-hub/homepage/${e}`,onClick:()=>{const t=(0,u.getNewPath)({customizing:!0},`/customize-store/assembler-hub/homepage/${e}`,{});(0,u.navigateTo)({url:t}),(0,L.s)("customize_your_store_assembler_pattern_category_click",{category:e})},as:qo.A,withChevron:!0,children:(0,x.jsxs)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen-patterns__group-homepage-label-container",children:[(0,x.jsx)("span",{children:(0,St.capitalize)(t)}),n.length>0&&i[e]>0&&(0,x.jsx)("span",{className:"woocommerce-edit-site-sidebar-navigation-screen-patterns__group-homepage-number-pattern",children:i[e]})]})})},o))),h&&(0,x.jsxs)("div",{className:"woocommerce-customize-store_sidebar-patterns-upgrade-notice",children:[(0,x.jsx)("h4",{children:(0,k.__)("Want more patterns?","woocommerce")}),(0,x.jsx)("p",{children:(0,c.createInterpolateElement)(h,{OptInModal:(0,x.jsx)(j.Button,{onClick:()=>{M(!0)},variant:"link"}),FetchPatterns:(0,x.jsx)(j.Button,{onClick:async()=>{await K()({path:"/wc/private/patterns",method:"POST"}),d()},variant:"link"})})}),w&&(0,x.jsxs)(j.Modal,{className:"woocommerce-customize-store__opt-in-usage-tracking-modal",title:(0,k.__)("Access more patterns","woocommerce"),onRequestClose:b,shouldCloseOnClickOutside:!1,children:[(0,x.jsx)(j.CheckboxControl,{className:"core-profiler__checkbox",label:(0,f.A)({mixedString:(0,k.__)("More patterns from the WooCommerce.com library are available! Opt in to connect your store and access the full library, plus get more relevant content and a tailored store setup experience. Opting in will enable {{link}}usage tracking{{/link}}, which you can opt out of at any time via WooCommerce settings.","woocommerce"),components:{link:(0,x.jsx)(p.Link,{href:"https://woocommerce.com/usage-tracking?utm_medium=product",target:"_blank",type:"external"})}}),checked:N,onChange:I}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__design-change-warning-modal-footer",children:[(0,x.jsx)(j.Button,{onClick:()=>{(0,L.s)("customize_your_store_assembler_hub_skip_opt_in_usage_tracking"),b()},variant:"link",children:(0,k.__)("Cancel","woocommerce")}),(0,x.jsx)(j.Button,{onClick:async()=>{(0,L.s)("customize_your_store_assembler_hub_opt_in_usage_tracking"),await Pe(),D(!0),b(),D(!1)},variant:"primary",disabled:!N,children:_?(0,x.jsx)(j.Spinner,{}):(0,k.__)("Opt in","woocommerce")})]})]})]})]})})})},kr=(e,t)=>"/customize-store/assembler-hub"===e?(0,x.jsx)(tr,{}):"/customize-store/assembler-hub/color-palette"===e?(0,x.jsx)(Oo,{onNavigateBackClick:t}):"/customize-store/assembler-hub/logo"===e?(0,x.jsx)(hr,{onNavigateBackClick:t}):"/customize-store/assembler-hub/typography"===e?(0,x.jsx)(rr,{onNavigateBackClick:t}):"/customize-store/assembler-hub/header"===e?(0,x.jsx)(Ro,{onNavigateBackClick:t}):Le()&&e?.includes("/customize-store/assembler-hub/homepage")?(0,x.jsx)(Mr,{onNavigateBackClick:t}):Le()||"/customize-store/assembler-hub/homepage"!==e?"/customize-store/assembler-hub/footer"===e?(0,x.jsx)(Qo,{onNavigateBackClick:t}):(0,x.jsx)(tr,{}):(0,x.jsx)(Wo,{onNavigateBackClick:t});function br(){const e=(0,u.useQuery)().path,{navigate:t}=(0,c.useContext)($o),o=(0,c.useCallback)((()=>{const e=(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub",{});(0,u.navigateTo)({url:e}),t(Xo.Back)}),[t]);return(0,x.jsx)(x.Fragment,{children:kr(e,o)})}const fr=(0,c.memo)((function(){return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(er,{children:(0,x.jsx)(br,{})}),(0,x.jsx)(ir,{})]})})),jr="woocommerce_customize_store_onboarding_tour_hidden",Nr=({onClose:e,skipTour:t,takeTour:o,showWelcomeTour:r,setIsResizeHandleVisible:s})=>{const[n,a]=(0,c.useState)("left"),{heading:i,descriptions:l}={heading:(0,k.__)("Discover what's possible with the store designer","woocommerce"),descriptions:{desktop:(0,k.__)("Start designing your store, including adding your logo, changing color schemes, and building your own layouts. Take a quick tour to discover what's possible.","woocommerce")}};return r?(0,x.jsx)(p.TourKit,{config:{options:{effects:{arrowIndicator:!1,overlay:!1,liveResize:{rootElementSelector:"#adminmenuback",resize:!0}},portalParentElement:document.getElementById("wpbody"),popperModifiers:[{name:"bottom-left",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:({state:e})=>{e.styles.popper.top="auto",e.styles.popper.left="auto",e.styles.popper.bottom="16px",e.styles.popper.transform="translate3d(16px, 0px, 0px)"}}],classNames:["woocommerce-customize-store-tour-kit","woocommerce-customize-store-welcome-tourkit"]},steps:[{meta:{name:"welcome-tour",primaryButton:{text:(0,k.__)("Take a tour","woocommerce")},descriptions:l,heading:i,skipButton:{isVisible:!0}},referenceElements:{desktop:"#adminmenuback"}}],closeHandler:(e,r,s)=>{"done-btn"===s?o():t()}}}):(0,x.jsx)(p.TourKit,{config:{placement:n,options:{effects:{spotlight:{interactivity:{enabled:!0,rootElementSelector:"#wpwrap"}},arrowIndicator:!0,autoScroll:{behavior:"auto",block:"center"},liveResize:{mutation:!0,resize:!0,rootElementSelector:"#wpwrap"}},callbacks:{onPreviousStep:()=>{a("left"),s(!0)},onNextStep:()=>{a("right-start"),s(!1)}},popperModifiers:[{name:"right-start",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:({state:e})=>{e.styles.arrow.transform="translate3d(0px, 96px, 0)"}},{name:"offset",options:{offset:({placement:e})=>"left"===e?[0,20]:[52,16]}}],classNames:"woocommerce-customize-store-tour-kit"},steps:[{referenceElements:{desktop:".woocommerce-edit-site-layout__canvas-container"},meta:{name:"view-changes-real-time",heading:(0,k.__)("View your changes in real time","woocommerce"),descriptions:{desktop:(0,k.__)("Any changes you make to the layout and style will appear here in real time — perfect for testing different looks before you make it live. You can also resize this area to check how your store looks on mobile.","woocommerce")}}},{referenceElements:{desktop:".woocommerce-edit-site-layout__sidebar-region"},meta:{name:"make-your-store-your-own",heading:(0,k.__)("Make your store your own","woocommerce"),descriptions:{desktop:(0,k.__)("Customize the style and layout of your store to fit your brand! Add your logo, change the font and colors, and try out different page layouts. You'll be able to edit the text and images later via the Editor.","woocommerce")},secondaryButton:{text:(0,k.__)("Previous","woocommerce")}}}],closeHandler:(t,o,r)=>{"done-btn"===r?(0,L.s)("customize_your_store_assembler_hub_tour_complete"):(0,L.s)("customize_your_store_assembler_hub_tour_close"),e()}}})},Ir="customize_your_store_transitional_page_secondary_button",_r=({children:e,order:t=1})=>(0,x.jsx)(j.Fill,{name:Ir,children:o=>(0,p.createOrderedChildren)(e,t,o)});_r.Slot=({fillProps:e})=>(0,x.jsx)(j.Slot,{name:Ir,fillProps:e,children:p.sortFillsByOrder});const xr=()=>{const e=(0,N.useSlot)(Ir);return Boolean(e?.fills?.length)?(0,x.jsx)(_r.Slot,{}):null};var Dr=o(5573);const Tr=(0,x.jsxs)(Dr.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",viewBox:"0 0 24 24",children:[(0,x.jsx)(Dr.Path,{fill:"#1E1E1E",d:"m8.85 4.821-1.203-.895-2.083 2.802-1.114-.83L3.553 7.1 5.87 8.829l2.98-4.008ZM20 7.75h-8.889v-1.5H20v1.5Zm0 5h-8.889v-1.5H20v1.5Z"}),(0,x.jsx)(Dr.Path,{fill:"#1E1E1E",fillRule:"evenodd",d:"M6 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm0-1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm2 4a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm-1 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z",clipRule:"evenodd"}),(0,x.jsx)(Dr.Path,{fill:"#1E1E1E",d:"M11.111 17.75H20v-1.5h-8.889v1.5Z"})]});var vr=o(33484),zr=o(44412);const Er=async()=>(0,g.resolveSelect)(d.optionsStore).getOption("woocommerce_admin_customize_store_survey_completed"),Sr=()=>{const e=(0,y.getSetting)("homeUrl",""),t=(0,u.getNewPath)((0,u.getPersistedQuery)(),"/",{});return(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional",children:[(0,x.jsx)(I.b,{isTransparent:!1,className:"woocommerce-edit-site-layout__hub"}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional-content",children:[(0,x.jsx)("h1",{className:"woocommerce-customize-store__transitional-heading",children:(0,k.__)("Your store looks great!","woocommerce")}),(0,x.jsx)("h2",{className:"woocommerce-customize-store__transitional-subheading",children:(0,ao.ex)()?(0,k.__)("Congratulations! You've successfully designed your store. Now you can go back to the Home screen to complete your store setup and start selling.","woocommerce"):(0,k.__)("Congratulations! You've successfully designed your store. Take a look at your hard work before continuing to set up your store.","woocommerce")}),(0,x.jsx)(xr,{}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional-buttons",children:[(0,x.jsx)(j.Button,{href:e,className:"woocommerce-customize-store__transitional-preview-button",variant:(0,ao.ex)()?"secondary":"primary",onClick:()=>{(0,L.s)("customize_your_store_transitional_preview_store_click")},children:(0,k.__)("View store","woocommerce")}),(0,ao.ex)()&&(0,x.jsx)(j.Button,{variant:"primary",href:t,onClick:()=>{(0,L.s)("customize_your_store_entrepreneur_home_click")},children:(0,k.__)("Back to Home","woocommerce")})]}),!(0,ao.ex)()&&(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("h2",{className:"woocommerce-customize-store__transitional-main-actions-title",children:(0,k.__)("What's next?","woocommerce")}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional-main-actions",children:[(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional-action",children:[(0,x.jsx)(cr.A,{className:"woocommerce-customize-store__transitional-action__icon",icon:vr.A}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional-action__content",children:[(0,x.jsx)("h3",{children:(0,k.__)("Add your products","woocommerce")}),(0,x.jsx)("p",{children:(0,k.__)("Start stocking your virtual shelves by adding or importing your products, or edit the sample products.","woocommerce")}),(0,x.jsx)(j.Button,{variant:"link",href:`${or.kY}edit.php?post_type=product`,onClick:()=>{(0,L.s)("customize_your_store_transitional_product_list_click")},children:(0,k.__)("Go to Products","woocommerce")})]})]}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional-action",children:[(0,x.jsx)(cr.A,{className:"woocommerce-customize-store__transitional-action__icon",icon:zr.A}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional-action__content",children:[(0,x.jsx)("h3",{children:(0,k.__)("Fine-tune your design","woocommerce")}),(0,x.jsx)("p",{children:(0,k.__)("Head to the Editor to change your images and text, add more pages, and make any further customizations.","woocommerce")}),(0,x.jsx)(j.Button,{variant:"link",href:`${or.kY}site-editor.php`,onClick:()=>{(0,L.s)("customize_your_store_transitional_editor_click")},children:(0,k.__)("Go to the Editor","woocommerce")})]})]}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional-action",children:[(0,x.jsx)(cr.A,{className:"woocommerce-customize-store__transitional-action__icon",icon:Tr}),(0,x.jsxs)("div",{className:"woocommerce-customize-store__transitional-action__content",children:[(0,x.jsx)("h3",{children:(0,k.__)("Continue setting up your store","woocommerce")}),(0,x.jsx)("p",{children:(0,k.__)("Go back to the Home screen to complete your store setup and start selling","woocommerce")}),(0,x.jsx)(j.Button,{variant:"link",href:t,onClick:()=>{(0,L.s)("customize_your_store_transitional_home_click")},children:(0,k.__)("Back to Home","woocommerce")})]})]})]})]})]})]})};var Ar=o(47804);const Lr=(e,t)=>{e.querySelectorAll(".is-added").forEach((e=>{e.classList.remove("is-added")})),t.forEach((t=>{const o=t.attributes.metadata?.patternName;if(!o)return;const r=e.querySelector(`[id="${o}"]`);r&&r.classList.add("is-added")}))},Cr={intro:["Intro: Two column with content and image","Heading with image and two columns below","Fullwidth content with background image","Two column with image and content","Centered heading with two column text","Content with button and fullwidth image","Center-aligned content overlaid on an image","Left-aligned content overlaid on an image","Centered Content","Large left-aligned heading","Fullwidth image with content and button","Pull right with wide image below"],about:["Content right with image left","Content left with image right","Heading left and content right","Four image grid, content on the left","Content with grid of images on right","Heading with two media columns","Heading with content and large image below","Centered heading and button","Content left, image right","Tall content with image left","Fullwidth image, content pull right","Right-aligned Content","Large heading with content on right","Tall content with image right","Spread right, heavy text","Heading with button and text","Left-aligned content","Pull left, fullwidth image"],services:["Three columns with images and content","Heading with four text sections","Two columns with images","Heading with six text sections","Headings left, content right"]},Or=(e,t,o)=>{const r=Cr[o]?.indexOf(e),s=Cr[o]?.indexOf(t);return-1===r&&-1===s?null:r>-1&&s>-1?r-s:-1===r&&s>-1?1:-1===s&&r>-1?-1:void 0},Fr=({category:e})=>{const{patterns:t,isLoading:o}=Ie(e),r=Lt(),s=(0,c.useMemo)((()=>{const o=t.filter((e=>!e.name.includes(je)&&"pattern-directory/theme"!==e.source&&"pattern-directory/core"!==e.source)).map((e=>{if("woocommerce-blocks/just-arrived-full-hero"!==e.name&&"woocommerce-blocks/featured-category-cover-image"!==e.name)return e;if(!r){const t=zt(e.blocks,(e=>{e.forEach((e=>e.attributes.style={}))}));return{...e,blocks:t}}const t=zt(e.blocks,(e=>{e.forEach((e=>e.attributes.style=Et))}));return{...e,blocks:t}}));return((e,t)=>{const o="woocommerce-blocks";return"intro"===t||"about"===t?e.sort(((e,r)=>{if("woocommerce-blocks/centered-content-with-image-below"===e.name)return-1;if("woocommerce-blocks/centered-content-with-image-below"===r.name)return 1;const s=Or(e.title,r.title,t);return"number"==typeof s?s:e.name.includes(o)&&!r.name.includes(o)?1:!e.name.includes(o)&&r.name.includes(o)?-1:0})):e.sort(((e,r)=>{const s=Or(e.title,r.title,t);return"number"==typeof s?s:e.name.includes(o)&&!r.name.includes(o)?-1:!e.name.includes(o)&&r.name.includes(o)?1:0}))})(o,e)}),[e,r,t]),n=(0,_.useAsyncList)(s),[i,l]=(0,c.useState)(10),m=(0,c.useRef)(null),d=(0,g.useSelect)((e=>e(a.store).getDefaultTemplateId({slug:"home"})),[]),[p]=Dt("wp_template",d||""),y=(0,st.O)(),h=o||y;(0,c.useEffect)((()=>{if(h||null===m.current)return;Lr(m.current,p);const e=new MutationObserver((()=>{Lr(m.current,p)})),t=document.querySelector(".woocommerce-customize-store-edit-site-layout__sidebar-extra__pattern .block-editor-block-patterns-list");return t&&e.observe(t,{childList:!0}),()=>{e.disconnect()}}),[o,p,h]);const{insertPattern:w,insertedPattern:M}=Ct();return(0,c.useEffect)((()=>{if(y)return;const e=window.document.querySelector('.woocommerce-customize-store-assembler > iframe[name="editor-canvas"]'),t=e?.contentWindow?.document.body.querySelector(".block-editor-block-list__layout"),o=new MutationObserver((()=>{if(M.current){const e=t?.querySelector(`[id="block-${M.current}"]`);e&&(e.scrollIntoView({behavior:"smooth",block:"end"}),M.current=null)}}));return t&&o.observe(t,{childList:!0}),()=>{o.disconnect()}}),[M,y]),(0,x.jsxs)("div",{className:"woocommerce-customize-store-edit-site-layout__sidebar-extra__pattern",onScroll:e=>{const t=e.target;t.scrollTop/(t.scrollHeight-t.clientHeight)>.5&&l((e=>e+10))},children:[(0,x.jsxs)("div",{className:"woocommerce-customize-store-edit-site-layout__sidebar-extra__pattern__header",children:[(0,x.jsx)("h1",{children:(0,St.capitalize)(Qt[e].label)}),(0,x.jsx)(j.Button,{onClick:()=>{const e=(0,u.getNewPath)({customizing:!0},"/customize-store/assembler-hub/homepage",{});(0,u.navigateTo)({url:e}),(0,L.s)("customize_your_store_assembler_pattern_sidebar_close")},iconSize:18,icon:Ar.A,label:(0,k.__)("Close","woocommerce")})]}),(0,x.jsx)("div",{className:"woocommerce-customize-store-edit-site-layout__sidebar-extra__pattern__description",children:(0,x.jsx)("span",{children:Qt[e].description})}),h&&(0,x.jsx)("span",{className:"components-placeholder__preview",children:(0,x.jsx)(j.Spinner,{})}),!h&&(0,x.jsx)(ke.__experimentalBlockPatternsList,{shownPatterns:n.slice(0,i),blockPatterns:n.slice(0,i),onClickPattern:w,label:"Homepage",orientation:"vertical",category:e,isDraggable:!1,showTitlesAsTooltip:!0,ref:m})]})},Br=()=>{const{path:e}=(0,u.useQuery)(),t=(0,c.useMemo)((()=>e?(e=>{const t=e.match(/\/homepage\/(.*)/),o=t?.[1];return o&&Object.keys(Qt).includes(o)?o:null})(e):null),[e]);return t?(0,x.jsx)("div",{className:"woocommerce-customize-store-edit-site-layout__sidebar-extra",children:(0,x.jsx)(Fr,{category:t})}):null},{useGlobalStyle:Pr}=(0,Je.T)(ke.privateApis),Ur=()=>{const[e,t]=(0,c.useState)([]),{currentState:o}=(0,c.useContext)(Jr);(0,rt.A)();const{shouldTourBeShown:r,isResizeHandleVisible:s,setShowWelcomeTour:n,onClose:i,...l}=(()=>{const[e,t]=(0,ut.useState)(!0),[o,r]=(0,ut.useState)(!0),{updateOptions:s}=(0,g.useDispatch)(d.optionsStore),{shouldTourBeShown:n}=(0,g.useSelect)((e=>{const{getOption:t,hasFinishedResolution:o}=e(d.optionsStore);return{shouldTourBeShown:!("yes"===t(jr)||!o("getOption",[jr]))}}),[]);return{onClose:()=>{s({[jr]:"yes"})},shouldTourBeShown:n,showWelcomeTour:e,setShowWelcomeTour:t,setIsResizeHandleVisible:r,isResizeHandleVisible:o}})(),m=(0,_.useViewportMatch)("medium","<"),u=(0,_.useReducedMotion)(),[p,y]=(0,_.useResizeObserver)(),h=(0,st.O)(),[w,M]=(0,c.useState)(!1),[b]=Pr("color.background"),[f]=Pr("color.gradient"),{record:N}=(0,it.A)(),{id:D,type:T}=N,v=(0,x.jsx)(oo,{isLoading:h});return"object"==typeof o&&"transitional"===o.transitionalScreen?(0,x.jsx)(a.EntityProvider,{kind:"root",type:"site",children:(0,x.jsx)(a.EntityProvider,{kind:"postType",type:T,id:D,children:(0,x.jsx)(Sr,{})})}):(0,x.jsx)(be.Provider,{value:{logoBlockIds:e,setLogoBlockIds:t},children:(0,x.jsx)(eo,{children:(0,x.jsx)(a.EntityProvider,{kind:"root",type:"site",children:(0,x.jsxs)(a.EntityProvider,{kind:"postType",type:T,id:D,children:[(0,x.jsxs)("div",{className:(0,ot.A)("woocommerce-edit-site-layout"),children:[(0,x.jsx)(j.__unstableMotion.div,{className:"woocommerce-edit-site-layout__header-container",animate:"view",children:(0,x.jsx)(I.b,{isTransparent:!1,className:"woocommerce-edit-site-layout__hub"})}),(0,x.jsxs)("div",{className:"woocommerce-edit-site-layout__content",children:[(0,x.jsxs)("div",{className:"woocommerce-edit-site-layout__sidebar",children:[(0,x.jsx)(at.s2,{ariaLabel:(0,k.__)("Navigation","woocommerce"),className:"woocommerce-edit-site-layout__sidebar-region",children:(0,x.jsx)(j.__unstableMotion.div,{animate:{opacity:1},transition:{type:"tween",duration:u||m?0:.5,ease:"easeOut"},className:"woocommerce-edit-site-layout__sidebar",children:(0,x.jsx)(fr,{})})}),(0,x.jsx)(Br,{})]}),!m&&(0,x.jsxs)("div",{className:"woocommerce-edit-site-layout__canvas-container",children:[p,!!y.width&&(0,x.jsx)(j.__unstableMotion.div,{initial:!1,layout:"position",className:(0,ot.A)("woocommerce-edit-site-layout__canvas"),children:(0,x.jsx)(nt.A,{children:(0,x.jsx)(It.A,{isReady:!h,isHandleVisibleByDefault:!l.showWelcomeTour&&s,isFullWidth:!1,defaultSize:{width:y.width-24,height:y.height},isOversized:w,setIsOversized:M,innerContentStyle:{background:null!=f?f:b},children:v})})})]})]})]}),!h&&r&&(0,x.jsx)(Nr,{skipTour:()=>{(0,L.s)("customize_your_store_assembler_hub_tour_skip"),i()},takeTour:()=>{(0,L.s)("customize_your_store_assembler_hub_tour_start"),n(!1)},onClose:i,...l})]})})})})},Qr={},Yr=e=>{(0,c.useEffect)((()=>{if("undefined"!=typeof document)return Qr[e]=(Qr[e]||0)+1,1===Qr[e]&&document.body.classList.add(e),()=>{Qr[e]--,0===Qr[e]&&document.body.classList.remove(e)}}),[e])},{useGlobalSetting:Rr}=(0,Je.T)(ke.privateApis),Wr=()=>{const{setOptInFlowStatus:e}=(0,c.useContext)(Eo),[t,o]=Rr("typography.fontFamilies"),r=(0,g.useSelect)((e=>"yes"===e(d.optionsStore).getOption("woocommerce_allow_tracking")),[]);return(0,c.useEffect)((function(){r&&(e(zo.LOADING),async function(){await async function(){await K()({path:"/wc/private/patterns",method:"POST"}),await(0,g.dispatch)(a.store).invalidateResolutionForStoreSelector("getBlockPatterns")}();const e=await async function(e){var t;await Fe();const o=(0,g.select)(a.store).__experimentalGetCurrentGlobalStylesId(),r=(await(0,g.resolveSelect)(a.store).getEntityRecords("postType","wp_font_family",{_embed:!0,per_page:-1})||[]).map((e=>({id:e.id,...e.font_family_settings,fontFace:e?._embedded?.font_faces.map((e=>e.font_face_settings))||[]}))),{custom:s}=e,n=[...s?s.map((e=>e.slug)):[]],i=r.reduce(((e,t)=>n.includes(t.slug)||void 0===pe[t.slug]?e:[...e,{...t}]),[]),{__experimentalSaveSpecifiedEntityEdits:c}=(0,g.dispatch)(a.store);return c("root","globalStyles",o,["settings.typography.fontFamilies"],void 0),{...e,custom:[...null!==(t=e.custom)&&void 0!==t?t:[],...null!=i?i:[]]}}(t);o(e)}().finally((()=>{e(zo.DONE)})))}),[r]),null},Gr={},Hr={},Zr={"core/block-editor":{moveBlocksUp:()=>(0,L.s)("customize_your_store_assembler_pattern_move_up_click"),moveBlocksDown:()=>(0,L.s)("customize_your_store_assembler_pattern_move_down_click")}};(0,g.use)((e=>({dispatch:t=>{const o="object"==typeof t?t.name:t,r={...e.dispatch(o)},s=Zr[o];return Gr[o]||(Gr[o]={}),Hr[o]||(Hr[o]={}),s&&Object.keys(s).forEach((e=>{const t=r[e],n=s[e];Hr[o][e]&&Hr[o][e]===t||(Hr[o][e]=t,Gr[o][e]=(...e)=>{try{"string"==typeof n?(0,L.s)(n):"function"==typeof n&&n(...e)}catch(e){console.error(e)}return t(...e)}),r[e]=Gr[o][e]})),r}})),{});const{RouterProvider:Vr}=(0,Je.T)(Ve.privateApis);(0,tt.addFilter)("editor.MediaUpload","woo/customize-store/assembler-hub",(()=>et.MediaUpload));const Jr=(0,c.createContext)({sendEvent:()=>{},context:{},currentState:"assemblerHub"}),Kr=e=>{const t=(0,c.useRef)(!1);Yr("woocommerce-assembler"),t.current||((()=>{if(!window.wcBlockSettings)return void console.warn("window.blockSettings not found. Skipping initialization.");const e=window.wcBlockSettings;e.__experimentalFetchLinkSuggestions=(t,o)=>(0,a.__experimentalFetchLinkSuggestions)(t,o,e),e.__experimentalFetchRichUrlData=a.__experimentalFetchUrlData,(0,(0,g.dispatch)(we.store).reapplyBlockTypeFilters)();const t=(0,Me.__experimentalGetCoreBlocks)().filter((({name:e})=>"core/freeform"!==e&&!(0,we.getBlockType)(e)));(0,Me.registerCoreBlocks)(t),(0,g.dispatch)(we.store).setFreeformFallbackBlockName("core/html"),(0,g.dispatch)(qe.store).setDefaults("core/edit-site",{editorMode:"visual",fixedToolbar:!1,focusMode:!1,distractionFree:!1,keepCaretInsideBlock:!1,welcomeGuide:!1,welcomeGuideStyles:!1,welcomeGuidePage:!1,welcomeGuideTemplate:!1,showListViewByDefault:!1,showBlockBreadcrumbs:!0}),(0,g.dispatch)($e.M).updateSettings(e),(0,g.dispatch)(Xe.store).updateEditorSettings({defaultTemplateTypes:e.defaultTemplateTypes,defaultTemplatePartAreas:e.defaultTemplatePartAreas}),window.addEventListener("dragover",(e=>e.preventDefault()),!1),window.addEventListener("drop",(e=>e.preventDefault()),!1),(0,Je.T)((0,g.dispatch)($e.M)).setCanvasMode("view")})(),t.current=!0);const[o,r]=(0,c.useState)(!1);return(0,c.useEffect)((()=>{(0,A.q2)((()=>r(!0)))}),[]),e.context.aiOnline=window.parent?.window.cys_aiOnline,(0,x.jsxs)(x.Fragment,{children:[o&&(0,x.jsx)(no,{setOpenWarningModal:r,onExitClicked:()=>{window.location.href=(0,u.getNewPath)({},"/",{})}}),(0,x.jsx)(Jr.Provider,{value:e,children:(0,x.jsx)(Ke.ShortcutProvider,{style:{height:"100%"},children:(0,x.jsx)(So,{children:(0,x.jsxs)(ue.T,{children:[(0,x.jsx)(Vr,{routes:[],children:(0,x.jsx)(Ur,{})}),(0,x.jsx)(Wr,{})]})})})})]})};var qr=o(50348);const Xr=()=>{const e=(0,u.getNewPath)((0,u.getPersistedQuery)(),"/",{});(0,A.$g)(window,e)},$r=()=>(0,x.jsx)("div",{className:"woocommerce-customize-store__loading",children:(0,x.jsx)(p.Spinner,{})}),es={updateQueryStep:(e,t,{action:o})=>{const{path:r}=(0,u.getQuery)(),s=o.step,n=r.split("/");if("customize-store"===n[1]&&n[2]!==s){const e=`/customize-store/${s}`;if((0,A.d4)(window)&&window.top)return void window.top.history.pushState({},"",(0,u.getNewPath)({},e));(0,u.updateQueryString)({},e)}},redirectToWooHome:Xr,redirectToThemes:e=>{var t;(0,_e.E)()?window.location.href=null!==(t=e?.intro?.themeData?._links?.browse_all?.href)&&void 0!==t?t:(0,y.getAdminLink)("themes.php"):(0,qr.isFeatureEnabled)("marketplace")?window.location.href=(0,y.getAdminLink)("admin.php?page=wc-admin&tab=themes&path=%2Fextensions"):window.location.href="https://woocommerce.com/product-category/themes/"},redirectToReferrer:()=>{const{getWithExpiry:e,remove:t}=(0,w.accessTaskReferralStorage)({taskId:"customize-store"}),o=e();o&&(t(),window.location.href=o.returnUrl)},goBack:()=>{const e=(0,u.getHistory)();e.__experimentalLocationStack.length>=2&&!e.__experimentalLocationStack[e.__experimentalLocationStack.length-2].search.includes("customize-store")?e.back():Xr()}},ts={...r,...es},os={...s,...n,browserPopstateHandler:()=>e=>{const t=()=>{e({type:"EXTERNAL_URL_UPDATE"})};return window.addEventListener("popstate",t),()=>{window.removeEventListener("popstate",t)}},markTaskComplete:async()=>{const e=await(0,g.resolveSelect)(a.store).getDefaultTemplateId({slug:"home"});return(0,g.dispatch)(d.optionsStore).updateOptions({woocommerce_admin_customize_store_completed:"yes",woocommerce_admin_customize_store_completed_theme_id:e})}},rs=(0,i.O)({id:"customizeStore",initial:"setFlags",predictableActionArguments:!0,preserveActionOrder:!0,schema:{context:{},events:{},services:{}},context:{intro:{hasErrors:!1,themeData:{themes:[],_links:{browse_all:{href:(0,y.getAdminLink)("themes.php")}}},activeTheme:"",customizeStoreTaskCompleted:!1},isFontLibraryAvailable:null,isPTKPatternsAPIAvailable:null,activeThemeHasMods:void 0},invoke:{src:"browserPopstateHandler"},on:{GO_BACK_TO_DESIGN_WITHOUT_AI:{target:"intro",actions:[{type:"updateQueryStep",step:"intro"}]},EXTERNAL_URL_UPDATE:{target:"navigate"},NO_AI_FLOW_ERROR:{target:"intro",actions:[{type:"assignNoAIFlowError"},{type:"updateQueryStep",step:"intro"}]},INSTALL_FONTS:{target:"designWithoutAi.installFonts"}},states:{setFlags:{invoke:{src:"setFlags",onDone:{actions:"assignFlags",target:"navigate"}}},navigate:{always:[{target:"intro",cond:{type:"hasStepInUrl",step:"intro"}},{target:"designWithoutAi",cond:{type:"hasStepInUrl",step:"design"}},{target:"assemblerHub",cond:{type:"hasStepInUrl",step:"assembler-hub"}},{target:"transitionalScreen",cond:{type:"hasStepInUrl",step:"transitional"}},{target:"intro"}]},intro:{id:"intro",initial:"fetchIntroData",states:{fetchIntroData:{initial:"pending",states:{pending:{invoke:{src:"fetchIntroData",onError:{actions:"assignFetchIntroDataError",target:"success"},onDone:{target:"success",actions:["assignThemeData","assignActiveTheme","assignCustomizeStoreCompleted","assignCurrentThemeIsAiGenerated"]}}},success:{type:"final"}},onDone:"intro"},intro:{meta:{component:({sendEvent:e,context:t})=>{const{intro:{activeTheme:o,themeData:r,customizeStoreTaskCompleted:s}}=t,n=z(),[a,i]=(0,c.useState)(t.intro.hasErrors),l=403===t.intro.errorStatus?(0,k.__)("Sorry, you don't have permission to update the theme.","woocommerce"):(0,k.__)("Oops! We encountered a problem while setting up the foundations. {{anchor}}Please try again{{/anchor}} or start with a theme.","woocommerce");let m="no-ai";const u="twentytwentyfour"===o,d=(0,g.useSelect)((e=>e("core").getCurrentTheme()),[]),p=d?.is_block_theme;switch(!0){case n:m="network-offline";break;case!p:m="classic-theme";break;case p&&!u:m="non-default-block-theme";break;case!s:m="no-ai";break;case s:m="existing-no-ai-theme"}const y=oe[m],h=(0,k.__)("Design a store that reflects your brand and business. Customize your active theme, select a professionally designed theme, or create a new look using our store designer.","woocommerce");return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("div",{className:"woocommerce-customize-store-header",children:(0,x.jsx)(I.b,{isTransparent:!1,className:"woocommerce-customize-store__content"})}),(0,x.jsxs)("div",{className:"woocommerce-customize-store-container",children:[(0,x.jsxs)("div",{className:"woocommerce-customize-store-sidebar",children:[(0,x.jsxs)("div",{className:"woocommerce-customize-store-sidebar__title",children:[(0,x.jsx)("button",{onClick:()=>{e("CLICKED_ON_BREADCRUMB")},children:b.A}),(0,k.__)("Customize your store","woocommerce")]}),(0,x.jsx)("p",{children:h})]}),(0,x.jsxs)("div",{className:"woocommerce-customize-store-main",children:[a&&(0,x.jsx)(j.Notice,{onRemove:()=>i(!1),className:"woocommerce-cys-design-with-ai__error-notice",status:"error",children:(0,f.A)({mixedString:l,components:{anchor:(0,x.jsx)("a",{className:"woocommerce-customize-store-error-link",onClick:()=>e("DESIGN_WITHOUT_AI")})}})}),(0,x.jsx)(y,{redirectToCYSFlow:()=>e("DESIGN_WITHOUT_AI"),sendEvent:e}),u&&!s?(0,x.jsx)(re,{sendEvent:e,themeData:r}):(0,x.jsx)(se,{isBlockTheme:p,isDefaultTheme:u,sendEvent:e})]})]})]})}}}},on:{CLICKED_ON_BREADCRUMB:{actions:"goBack"},DESIGN_WITHOUT_AI:{actions:["recordTracksDesignWithoutAIClicked"],target:"designWithoutAi"},SELECTED_NEW_THEME:{actions:["recordTracksThemeSelected"],target:"appearanceTask"},SELECTED_ACTIVE_THEME:{actions:["recordTracksThemeSelected"],target:"appearanceTask"},SELECTED_BROWSE_ALL_THEMES:{actions:["recordTracksBrowseAllThemesClicked","redirectToThemes"]}}},designWithoutAi:{initial:"preDesignWithoutAi",states:{preDesignWithoutAi:{always:{target:"designWithoutAi"}},designWithoutAi:{entry:[{type:"updateQueryStep",step:"design"}],meta:{component:Ze}},installFonts:{entry:[{type:"updateQueryStep",step:"design/install-fonts"}],meta:{component:Ze}},installPatterns:{entry:[{type:"updateQueryStep",step:"design/install-patterns"}],meta:{component:Ze}}}},assemblerHub:{initial:"fetchCustomizeStoreCompleted",states:{fetchCustomizeStoreCompleted:{invoke:{src:"fetchCustomizeStoreCompleted",onDone:{actions:"assignCustomizeStoreCompleted",target:"checkCustomizeStoreCompleted"}}},checkCustomizeStoreCompleted:{always:[{cond:"customizeTaskIsNotCompleted",actions:[{type:"updateQueryStep",step:"intro"}],target:"#customizeStore.intro"},{cond:"customizeTaskIsCompleted",target:"assemblerHub"}]},assemblerHub:{entry:[{type:"updateQueryStep",step:"assembler-hub"}],meta:{component:Kr}},postAssemblerHub:{invoke:[{src:"markTaskComplete",onDone:{target:"#customizeStore.transitionalScreen"}},{src:"fetchSurveyCompletedOption"}]}},on:{FINISH_CUSTOMIZATION:{target:".postAssemblerHub"}}},transitionalScreen:{initial:"fetchCustomizeStoreCompleted",states:{fetchCustomizeStoreCompleted:{invoke:{src:"fetchCustomizeStoreCompleted",onDone:{actions:"assignCustomizeStoreCompleted",target:"checkCustomizeStoreCompleted"}}},checkCustomizeStoreCompleted:{always:[{cond:"customizeTaskIsNotCompleted",actions:[{type:"updateQueryStep",step:"intro"}],target:"#customizeStore.intro"},{cond:"hasTaskReferral",target:"skipTransitional"},{cond:"customizeTaskIsCompleted",target:"preTransitional"}]},preTransitional:{meta:{component:$r},invoke:{src:"fetchSurveyCompletedOption",onError:{target:"transitional"},onDone:{target:"transitional"}}},skipTransitional:{entry:["redirectToReferrer"]},transitional:{entry:[{type:"updateQueryStep",step:"transitional"}],meta:{component:Kr}}}},appearanceTask:{}}}),ss=({actionOverrides:e,servicesOverrides:t})=>{(0,M.xG)(["woocommerce-customize-store"]);const o=(0,c.useMemo)((()=>rs.withConfig({services:{...os,...t},actions:{...ts,...e},guards:{hasStepInUrl:(e,t,{cond:o})=>{const{path:r=""}=(0,u.getQuery)();return r.split("/")[2]===o.step},activeThemeHasMods:e=>!!e.activeThemeHasMods,activeThemeHasNoMods:e=>!e.activeThemeHasMods,customizeTaskIsCompleted:e=>e.intro.customizeStoreTaskCompleted,customizeTaskIsNotCompleted:e=>!e.intro.customizeStoreTaskCompleted,hasTaskReferral:()=>{const{getWithExpiry:e}=(0,w.accessTaskReferralStorage)({taskId:"customize-store"});return null!==e()}}})),[e,t]),{versionEnabled:r}=(0,Ge.D)(),[s,n,a]=(0,l.z)(o,{devTools:"V4"===r});(0,c.useEffect)((()=>{(0,A.d4)(window)||(window.__wcCustomizeStore={...window.__wcCustomizeStore,sendEventToIntroMachine:e=>n(e)})}),[n]),window.__wcCustomizeStore={...window.__wcCustomizeStore};const i=(0,m.d)(a,(e=>{var t;return(0,We.Q)(null!==(t=e?.meta)&&void 0!==t?t:void 0)})),[d,g]=(0,c.useState)(null);(0,c.useEffect)((()=>{i?.component&&g((()=>i?.component))}),[d,i?.component]),(0,c.useEffect)((()=>(0,A.Vu)()),[]),Yr("is-fullscreen-mode");const p=s.value instanceof Object?Object.keys(s.value)[0]:s.value;return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("div",{className:`woocommerce-customize-store__container woocommerce-customize-store__step-${p}`,children:d?(0,x.jsx)(d,{parentMachine:a,sendEvent:n,context:s.context,currentState:s.value}):(0,x.jsx)($r,{})}),(0,x.jsx)(h.PluginArea,{scope:"woocommerce-customize-store"})]})},ns=ss},30737:(e,t,o)=>{o.d(t,{G5:()=>k,QL:()=>M,ow:()=>h,wY:()=>y,k8:()=>w,bp:()=>b});var r=o(27723),s=o(4921),n=o(56427),a=o(96476),i=o(36849),c=o(98846),l=o(39793);const m=({siteUrl:e})=>(0,l.jsx)("iframe",{className:"preview-iframe",src:e,title:"Preview",tabIndex:-1});var u=o(56109),d=o(42859),g=o(47884);const p=({bannerTitle:e,bannerText:t,bannerClass:o,showAIDisclaimer:a,buttonIsLink:m,bannerButtonOnClick:u,bannerButtonText:d,secondaryButton:g,previewBanner:p,children:y})=>(0,l.jsxs)("div",{className:(0,s.A)("woocommerce-customize-store-banner",o),children:[(0,l.jsxs)("div",{className:"woocommerce-customize-store-banner-content",children:[(0,l.jsxs)("div",{className:"banner-actions",children:[(0,l.jsx)("h1",{children:e}),(0,l.jsx)("p",{children:t}),d&&(0,l.jsx)(n.Button,{onClick:()=>u&&u(),variant:m?"link":"primary",children:d}),g,a&&(0,l.jsx)("p",{className:"ai-disclaimer",children:(0,i.A)({mixedString:(0,r.__)("Powered by experimental AI. {{link}}Learn more{{/link}}","woocommerce"),components:{link:(0,l.jsx)(c.Link,{href:"https://automattic.com/ai-guidelines",target:"_blank",type:"external"})}})})]}),y]}),p]}),y=()=>(0,l.jsx)(p,{bannerTitle:(0,r.__)("Looking to design your store using AI?","woocommerce"),bannerText:(0,r.__)("Unfortunately, the [AI Store designer] isn't available right now as we can't detect your network. Please check your internet connection.","woocommerce"),bannerClass:"offline-banner",bannerButtonOnClick:()=>{},showAIDisclaimer:!0}),h=({sendEvent:e})=>(0,l.jsx)(p,{bannerTitle:(0,r.__)("Looking to design your store using AI?","woocommerce"),bannerText:(0,r.__)("It looks like you're using Jetpack's offline mode — switch to online mode to start designing with AI.","woocommerce"),bannerClass:"offline-banner",buttonIsLink:!1,bannerButtonOnClick:()=>{e({type:"JETPACK_OFFLINE_HOWTO"})},bannerButtonText:(0,r.__)("Find out how","woocommerce"),showAIDisclaimer:!0}),w=({redirectToCYSFlow:e})=>(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(p,{bannerTitle:(0,r.__)("Design your own","woocommerce"),bannerText:(0,r.__)("Quickly create a beautiful store using our built-in store designer. Choose your layout, select a style, and much more.","woocommerce"),bannerClass:"no-ai-banner",bannerButtonText:(0,r.__)("Start designing","woocommerce"),bannerButtonOnClick:()=>{e()},showAIDisclaimer:!1})}),M=()=>{const e=(0,u.Qk)("siteUrl")+"?cys-hide-admin-bar=1";return(0,l.jsx)(p,{bannerTitle:(0,r.__)("Customize your theme","woocommerce"),bannerText:(0,r.__)("Customize everything from the color palette and the fonts to the page layouts, making sure every detail aligns with your brand.","woocommerce"),bannerClass:"existing-no-ai-theme-banner",buttonIsLink:!1,bannerButtonOnClick:()=>{(0,g.s)("customize_your_store_intro_customize_click",{theme_type:"block"}),(0,d.$g)(window,(0,a.getNewPath)({customizing:!0},"/customize-store/assembler-hub",{}))},bannerButtonText:(0,r.__)("Customize your store","woocommerce"),showAIDisclaimer:!1,previewBanner:(0,l.jsx)(m,{siteUrl:e})})},k=()=>{const e=(0,u.Qk)("siteUrl")+"?cys-hide-admin-bar=1";return(0,l.jsx)(p,{bannerTitle:(0,r.__)("Customize your theme","woocommerce"),bannerText:(0,r.__)("Customize everything from the color palette and the fonts to the page layouts, making sure every detail aligns with your brand.","woocommerce"),bannerClass:"existing-no-ai-theme-banner",buttonIsLink:!1,bannerButtonOnClick:()=>{(0,g.s)("customize_your_store_intro_customize_click",{theme_type:"classic"}),(0,d.$g)(window,"customize.php?return=/wp-admin/themes.php")},bannerButtonText:(0,r.__)("Go to the Customizer","woocommerce"),showAIDisclaimer:!1,previewBanner:(0,l.jsx)(m,{siteUrl:e})})},b=()=>{const e=(0,u.Qk)("siteUrl")+"?cys-hide-admin-bar=1";return(0,l.jsx)(p,{bannerTitle:(0,r.__)("Customize your theme","woocommerce"),bannerText:(0,r.__)("Customize everything from the color palette and the fonts to the page layouts, making sure every detail aligns with your brand.","woocommerce"),bannerClass:"existing-no-ai-theme-banner",buttonIsLink:!1,bannerButtonOnClick:()=>{(0,g.s)("customize_your_store_intro_customize_click",{theme_type:"block"}),(0,d.$g)(window,`${u.kY}site-editor.php`)},bannerButtonText:(0,r.__)("Go to the Editor","woocommerce"),showAIDisclaimer:!1,previewBanner:(0,l.jsx)(m,{siteUrl:e})})}},81392:(e,t,o)=>{o.d(t,{z:()=>m});var r=o(56427),s=o(27723),n=o(98846),a=o(86087),i=o(56109),c=o(47884),l=o(39793);const m=({isNoAiFlow:e=!0,setIsModalOpen:t,redirectToCYSFlow:o})=>(0,l.jsxs)(r.Modal,{className:"woocommerce-customize-store__theme-switch-warning-modal",title:(0,s.__)("Are you sure you want to design a new theme?","woocommerce"),onRequestClose:()=>t(!1),shouldCloseOnClickOutside:!1,children:[(0,l.jsx)("p",{children:e?(0,s.__)("Your active theme will be changed and you could lose any changes you’ve made to it.","woocommerce"):(0,a.createInterpolateElement)((0,s.__)("The Store Designer will create a new store design for you, and you'll lose any changes you've made to your active theme. If you'd prefer to continue editing your theme, you can do so via the <EditorLink>Editor</EditorLink>.","woocommerce"),{EditorLink:(0,l.jsx)(n.Link,{onClick:()=>(window.open(`${i.kY}site-editor.php`,"_blank"),!1),href:""})})}),(0,l.jsxs)("div",{className:"woocommerce-customize-store__theme-switch-warning-modal-footer",children:[(0,l.jsx)(r.Button,{onClick:()=>{t(!1)},variant:"link",children:(0,s.__)("Cancel","woocommerce")}),(0,l.jsx)(r.Button,{onClick:()=>{t(!1),(0,c.s)("customize_your_store_agree_to_theme_switch_click"),o()},variant:"primary",children:(0,s.__)("Design a new theme","woocommerce")})]})]})},47884:(e,t,o)=>{o.d(t,{s:()=>a});var r=o(83306),s=o(38966),n=o(97687);const a=(e,t)=>{(0,n.E)()&&(0,s.ex)()?(0,r.recordEvent)(e,{...t,ref:s.ot}):(0,r.recordEvent)(e,t)}},42859:(e,t,o)=>{o.d(t,{$g:()=>d,AV:()=>l,Gy:()=>p,Hr:()=>g,Vu:()=>u,am:()=>y,d4:()=>i,j1:()=>c,q2:()=>m});var r=o(96476),s=o(86817),n=o(56109),a=o(79745);function i(e){return e.document!==e.parent.document&&null!==e.parent.document.body.querySelector(".woocommerce-customize-store__container")}function c(){window.parent.postMessage({type:"iframe-loaded"},(0,n.Qk)("homeUrl"))}function l(e){window.addEventListener("message",(t=>{"iframe-loaded"===t.data.type&&e()}))}function m(e){window.addEventListener("message",(t=>{"assemberBackButtonClicked"===t.data.type&&e()}))}function u(){const e=[(0,n.Qk)("homeUrl")];function t(t){if(e.includes(t.origin)&&t.data&&"string"==typeof t.data.type&&"string"==typeof t.data.url&&"navigate"===t.data.type)try{const o=(0,r.parseAdminUrl)(t.data.url);if(!e.some((e=>o.origin===e)))throw new Error(`Blocked navigation to untrusted URL: ${o.href}`);window.location.href=o.href}catch(e){(0,s.captureException)(e)}}return window.addEventListener("message",t,!1),function(){window.removeEventListener("message",t,!1)}}function d(e,t){try{if(i(e))e.parent.postMessage({type:"navigate",url:t},(0,n.Qk)("homeUrl"));else{const o=(0,r.parseAdminUrl)(t);e.location.href=o.href}}catch(e){(0,s.captureException)(e)}}function g(e){const t=e.contentDocument||e.contentWindow?.document;t?.addEventListener("click",(function(e){if(e.target){const t=e.target?.closest("a");t&&"_blank"===t.target?(e.preventDefault(),window.open(t.href,"_blank")):t&&(e.preventDefault(),window.location.href=t.href)}}))}const p=(e,t=a.$k)=>e.replaceAll(/<!-- wp:site-logo\s*(\{.*?\})?\s*\/-->/g,((e,o)=>{if(o){const e=JSON.parse(o);return e.width=t,`\x3c!-- wp:site-logo ${JSON.stringify(e)} /--\x3e`}return`\x3c!-- wp:site-logo {"width":${t}} /--\x3e`})),y=(e,t)=>e.map(((e,o,r)=>{const s=r[o+1];if(!s)return[e];const n=[e],a=(s.progress-e.progress)/t;for(let o=0;o<t;o++)n.push({...e,progress:e.progress+(o+1)*a});return n})).flat()}}]);