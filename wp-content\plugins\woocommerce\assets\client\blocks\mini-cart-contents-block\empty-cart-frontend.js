"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[149],{1186:(e,c,s)=>{s.r(c),s.d(c,{default:()=>r});var a=s(5460),t=s(6087),n=s(790);const r=({children:e,className:c})=>{const{cartItems:s,cartIsLoading:r}=(0,a.V)(),l=(0,t.useRef)(null);return(0,t.useEffect)((()=>{0!==s.length||r||l.current?.focus()}),[s,r]),r||s.length>0?null:(0,n.jsx)("div",{tabIndex:-1,ref:l,className:c,children:(0,n.jsx)("div",{className:"wc-block-mini-cart__empty-cart-wrapper",children:e})})}}}]);