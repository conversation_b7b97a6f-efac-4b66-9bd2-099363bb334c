(()=>{"use strict";var e={d:(t,r)=>{for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{CustomDataProvider:()=>a,InnerBlockLayoutContextProvider:()=>u,ProductDataContextProvider:()=>_,useCustomDataContext:()=>s,useInnerBlockLayoutContext:()=>d,useProductDataContext:()=>m});const r=window.wp.element,n=window.ReactJSXRuntime,i=new Map;function o(e){return i.get(e)}function a({children:e,id:t,...a}){const s=function(e){let t=o(e);return t||(t=(0,r.createContext)({isLoading:!1}),i.set(e,t)),t}(t);return(0,n.jsx)(s.Provider,{value:a,children:a.isLoading?(0,n.jsx)("div",{className:"is-loading",children:e}):e})}function s(e){const t=o(e);return(0,r.useContext)(t)}const c=(0,r.createContext)({parentName:"",parentClassName:"",isLoading:!1}),d=()=>(0,r.useContext)(c),u=({parentName:e="",parentClassName:t="",isLoading:r=!1,children:i})=>{const o={parentName:e,parentClassName:t,isLoading:r};return(0,n.jsx)(c.Provider,{value:o,children:i})},l={id:0,name:"",parent:0,type:"simple",variation:"",permalink:"",sku:"",slug:"",short_description:"",description:"",on_sale:!1,prices:{currency_code:"USD",currency_symbol:"$",currency_minor_unit:2,currency_decimal_separator:".",currency_thousand_separator:",",currency_prefix:"$",currency_suffix:"",price:"0",regular_price:"0",sale_price:"0",price_range:null},price_html:"",average_rating:"0",review_count:0,images:[],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!1,is_in_stock:!1,is_on_backorder:!1,low_stock_remaining:null,stock_availability:{text:"",class:""},sold_individually:!1,add_to_cart:{text:"Add to cart",description:"Add to cart",url:"",minimum:1,maximum:99,multiple_of:1},grouped_products:[]},p=(0,r.createContext)({product:l,hasContext:!1,isLoading:!1}),m=(e={isAdmin:!1})=>{const t=(0,r.useContext)(p),{isAdmin:n,product:i,isResolving:o}=e;return n?{product:i,isLoading:o}:t},_=({product:e=null,children:t,isLoading:r})=>{const i={product:e||l,isLoading:r,hasContext:!0};return(0,n.jsx)(p.Provider,{value:i,children:r?(0,n.jsx)("div",{className:"is-loading",children:t}):t})};(this.wc=this.wc||{}).wcBlocksSharedContext=t})();