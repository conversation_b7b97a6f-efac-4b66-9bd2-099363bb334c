import*as e from"@wordpress/interactivity";var t={d:(e,o)=>{for(var r in o)t.o(o,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:o[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const o=(s={getContext:()=>e.getContext,getElement:()=>e.getElement,store:()=>e.store,withScope:()=>e.withScope},l={},t.d(l,s),l),r=e=>{if(!e)return{top:!1,bottom:!1,left:!1,right:!1};const{scrollTop:t,scrollHeight:o,clientHeight:r,scrollLeft:n,scrollWidth:c,clientWidth:i}=e;return{top:t>3,bottom:t+r<o-3,left:n>3,right:n+i<c-3}},n=e=>(0,o.getContext)(e),c={state:{get imageIndex(){const{imageData:e,selectedImageId:t}=n();return e.indexOf(t)}},actions:{selectImage:e=>{const t=n(),{imageData:r}=t,c=r[e],{isDisabledPrevious:i,isDisabledNext:s}={isDisabledPrevious:0===(l=e),isDisabledNext:l===r.length-1};var l;t.isDisabledPrevious=i,t.isDisabledNext=s,t.selectedImageId=c,-1!==c&&((e=>{if(!e)return;const t=(0,o.getElement)()?.ref;if(!t)return;const r=t.closest(".wp-block-woocommerce-product-gallery");if(!r)return;const n=r.querySelector(".wc-block-product-gallery-large-image__container");if(!n)return;const c=n.querySelector(`img[data-image-id="${e}"]`);if(c){const e=n.getBoundingClientRect(),t=c.getBoundingClientRect(),o=n.scrollLeft+(t.left-e.left)-(e.width-t.width)/2;n.scrollTo({left:o,behavior:"smooth"})}})(c),(e=>{if(!e)return;const t=(0,o.getElement)()?.ref;if(!t)return;const r=t.closest(".wp-block-woocommerce-product-gallery");if(!r)return;const n=r.querySelector(`.wc-block-product-gallery-thumbnails__thumbnail img[data-image-id="${e}"]`);if(!n)return;const c=n.closest(".wc-block-product-gallery-thumbnails__scrollable");if(!c)return;const i=n.closest(".wc-block-product-gallery-thumbnails__thumbnail");if(!i)return;const s=c.getBoundingClientRect(),l=i.getBoundingClientRect(),a=c.scrollTop+(l.top-s.top)-(s.height-l.height)/2,g=c.scrollLeft+(l.left-s.left)-(s.width-l.width)/2;c.scrollTo({top:a,left:g,behavior:"smooth"})})(c))},selectCurrentImage:e=>{e&&e.stopPropagation();const t=(0,o.getElement)()?.ref;if(!t)return;const r=t.getAttribute("data-image-id");if(!r)return;const c=parseInt(r,10),{imageData:s}=n(),l=s.indexOf(c);i.selectImage(l)},selectNextImage:e=>{e&&e.stopPropagation();const{imageData:t,selectedImageId:o}=n(),r=t.indexOf(o),c=Math.min(t.length-1,r+1);i.selectImage(c)},selectPreviousImage:e=>{e&&e.stopPropagation();const{imageData:t,selectedImageId:o}=n(),r=t.indexOf(o),c=Math.max(0,r-1);i.selectImage(c)},onSelectedLargeImageKeyDown:e=>{"Enter"!==e.code&&"Space"!==e.code&&"NumpadEnter"!==e.code||("Space"===e.code&&e.preventDefault(),i.openDialog()),"ArrowRight"===e.code&&i.selectNextImage(),"ArrowLeft"===e.code&&i.selectPreviousImage()},onDialogKeyDown:e=>{if("Escape"===e.code&&i.closeDialog(),"Tab"===e.code){const t='a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex]:not([tabindex="-1"])',r=(0,o.getElement)()?.ref,n=r.querySelectorAll(t);if(!n.length)return;const c=n[0],i=n[n.length-1];if(!e.shiftKey&&e.target===i)return e.preventDefault(),void c.focus();if(e.shiftKey&&e.target===c)return e.preventDefault(),void i.focus();e.target===r&&(e.preventDefault(),c.focus())}},openDialog:()=>{n().isDialogOpen=!0,document.body.classList.add("wc-block-product-gallery-dialog-open")},closeDialog:()=>{n().isDialogOpen=!1,document.body.classList.remove("wc-block-product-gallery-dialog-open")},onTouchStart:e=>{const t=n(),{clientX:o}=e.touches[0];t.touchStartX=o,t.touchCurrentX=o,t.isDragging=!0},onTouchMove:e=>{const t=n();if(!t.isDragging)return;const{clientX:o}=e.touches[0];t.touchCurrentX=o;const r=o-t.touchStartX;Math.abs(r)>10&&e.preventDefault()},onTouchEnd:()=>{const e=n();if(!e.isDragging)return;const t=e.touchCurrentX-e.touchStartX,r=(0,o.getElement)()?.ref,c=r?.offsetWidth||0;Math.abs(t)>.2*c&&(t>0&&!e.isDisabledPrevious?i.selectPreviousImage():t<0&&!e.isDisabledNext&&i.selectNextImage()),e.isDragging=!1,e.touchStartX=0,e.touchCurrentX=0},onScroll:()=>{const e=(0,o.getElement)()?.ref;if(!e)return;const t=n(),c=r(e);t.thumbnailsOverflow=c},onArrowsKeyDown:e=>{"ArrowRight"===e.code&&(e.preventDefault(),i.selectNextImage()),"ArrowLeft"===e.code&&(e.preventDefault(),i.selectPreviousImage())},onThumbnailsArrowsKeyDown:e=>{i.onArrowsKeyDown(e);const t=(0,o.getElement)()?.ref,{selectedImageId:r}=n();if(t){const e=t.closest(".wp-block-woocommerce-product-gallery");if(e){const t=e.querySelector(`img[data-image-id="${r}"]`);t&&t.focus({preventScroll:!0})}}},onClickPrevious:e=>{i.selectPreviousImage(e)},onClickNext:e=>{i.selectNextImage(e)},onKeyDownPrevious:e=>{i.onArrowsKeyDown(e)},onKeyDownNext:e=>{i.onArrowsKeyDown(e)}},callbacks:{watchForChangesOnAddToCartForm:()=>{const e=n(),t=document.querySelector(`form[data-product_id="${e.productId}"]`);if(!t)return;const r=()=>(0,o.withScope)((()=>i.selectImage(0))),c=new MutationObserver((0,o.withScope)((function(e){for(const t of e){const{imageData:e}=n(),o=t.target.getAttribute("current-image"),r=o?parseInt(o,10):null;if("attributes"===t.type&&r&&e.includes(r)){const t=e.indexOf(r);i.selectImage(t)}else i.selectImage(0)}})));c.observe(t,{attributes:!0});const s=document.querySelector(".wp-block-add-to-cart-form .reset_variations");return s&&s.addEventListener("click",r),()=>{c.disconnect(),document.removeEventListener("click",r)}},dialogStateChange:()=>{const{selectedImageId:e,isDialogOpen:t}=n(),{ref:r}=(0,o.getElement)()||{};if(t&&r instanceof HTMLElement){r.focus();const t=r.querySelector(`[data-image-id="${e}"]`);t instanceof HTMLElement&&t.scrollIntoView({behavior:"auto",block:"center"})}},toggleActiveThumbnailAttributes:()=>{const e=(0,o.getElement)()?.ref;if(!e)return!1;const t=e.getAttribute("data-image-id");if(!t)return!1;const{selectedImageId:r}=n();r===Number(t)?(e.classList.add("wc-block-product-gallery-thumbnails__thumbnail__image--is-active"),e.setAttribute("tabIndex","0")):(e.classList.remove("wc-block-product-gallery-thumbnails__thumbnail__image--is-active"),e.setAttribute("tabIndex","-1"))},initResizeObserver:()=>{const e=(0,o.getElement)()?.ref;if(!e)return;const t=n(),c=new ResizeObserver((()=>{const o=r(e);t.thumbnailsOverflow=o}));return c.observe(e),e.parentElement&&c.observe(e.parentElement),()=>{c.disconnect()}},hideGhostOverflow:()=>{const e=(0,o.getElement)()?.ref;if(!e)return;const{clientWidth:t,scrollWidth:r}=e;t>=r&&(e.style.scrollbarWidth="none")}}},{actions:i}=(0,o.store)("woocommerce/product-gallery",c,{lock:!0});var s,l;