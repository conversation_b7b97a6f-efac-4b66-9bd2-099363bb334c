var wc;(()=>{var e,t,r,s={3010:(e,t,r)=>{"use strict";var s=r(6087),o=r(7723);const i=window.wc.wcSettings,n=(0,i.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),a=n.pluginUrl+"assets/images/",c=(n.pluginUrl,i.STORE_PAGES.shop,i.STORE_PAGES.checkout,i.STORE_PAGES.checkout,i.STORE_PAGES.privacy,i.STORE_PAGES.privacy,i.STORE_PAGES.terms,i.STORE_PAGES.terms,i.STORE_PAGES.cart,i.STORE_PAGES.cart,i.STORE_PAGES.myaccount?.permalink?i.STORE_PAGES.myaccount.permalink:(0,i.getSetting)("wpLoginUrl","/wp-login.php"),(0,i.getSetting)("localPickupEnabled",!1),(0,i.getSetting)("shippingMethodsExist",!1),(0,i.getSetting)("shippingEnabled",!0),(0,i.getSetting)("countries",{})),l=(0,i.getSetting)("countryData",{}),d={...Object.fromEntries(Object.keys(l).filter((e=>!0===l[e].allowBilling)).map((e=>[e,c[e]||""]))),...Object.fromEntries(Object.keys(l).filter((e=>!0===l[e].allowShipping)).map((e=>[e,c[e]||""])))},p=(Object.fromEntries(Object.keys(d).map((e=>[e,l[e].states||{}]))),Object.fromEntries(Object.keys(d).map((e=>[e,l[e].locale||{}]))),{address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]});(0,i.getSetting)("addressFieldsLocations",p).address,(0,i.getSetting)("addressFieldsLocations",p).contact,(0,i.getSetting)("addressFieldsLocations",p).order,(0,i.getSetting)("additionalOrderFields",{}),(0,i.getSetting)("additionalContactFields",{}),(0,i.getSetting)("additionalAddressFields",{});var w=r(790);const m=({imageUrl:e=`${a}/block-error.svg`,header:t=(0,o.__)("Oops!","woocommerce"),text:r=(0,o.__)("There was an error loading the content.","woocommerce"),errorMessage:s,errorMessagePrefix:i=(0,o.__)("Error:","woocommerce"),button:n,showErrorBlock:c=!0})=>c?(0,w.jsxs)("div",{className:"wc-block-error wc-block-components-error",children:[e&&(0,w.jsx)("img",{className:"wc-block-error__image wc-block-components-error__image",src:e,alt:""}),(0,w.jsxs)("div",{className:"wc-block-error__content wc-block-components-error__content",children:[t&&(0,w.jsx)("p",{className:"wc-block-error__header wc-block-components-error__header",children:t}),r&&(0,w.jsx)("p",{className:"wc-block-error__text wc-block-components-error__text",children:r}),s&&(0,w.jsxs)("p",{className:"wc-block-error__message wc-block-components-error__message",children:[i?i+" ":"",s]}),n&&(0,w.jsx)("p",{className:"wc-block-error__button wc-block-components-error__button",children:n})]})]}):null;r(5893);class h extends s.Component{state={errorMessage:"",hasError:!1};static getDerivedStateFromError(e){return void 0!==e.statusText&&void 0!==e.status?{errorMessage:(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("strong",{children:e.status}),": ",e.statusText]}),hasError:!0}:{errorMessage:e.message,hasError:!0}}render(){const{header:e,imageUrl:t,showErrorMessage:r=!0,showErrorBlock:s=!0,text:o,errorMessagePrefix:i,renderError:n,button:a}=this.props,{errorMessage:c,hasError:l}=this.state;return l?"function"==typeof n?n({errorMessage:c}):(0,w.jsx)(m,{showErrorBlock:s,errorMessage:r?c:null,header:e,imageUrl:t,text:o,errorMessagePrefix:i,button:a}):this.props.children}}const u=h,v=[".wp-block-woocommerce-cart"],g=({Block:e,container:t,attributes:r={},props:o={},errorBoundaryProps:i={}})=>{const n=()=>{(0,s.useEffect)((()=>{t.classList&&t.classList.remove("is-loading")}),[]);const n=t.classList.contains("wp-block-woocommerce-checkout"),a=t.classList.contains("wp-block-woocommerce-cart");return n||a?(0,w.jsx)(u,{...i,children:(0,w.jsx)(e,{...o,attributes:r})}):(0,w.jsx)(u,{...i,children:(0,w.jsx)(s.Suspense,{fallback:(0,w.jsx)("div",{className:"wc-block-placeholder",children:"Loading..."}),children:e&&(0,w.jsx)(e,{...o,attributes:r})})})},a=(0,s.createRoot)(t);return a.render((0,w.jsx)(n,{})),a},b=({Block:e,containers:t,getProps:r=()=>({}),getErrorBoundaryProps:s=()=>({})})=>{if(0===t.length)return[];const o=[];return t.forEach(((t,i)=>{const n=r(t,i),a=s(t,i),c={...t.dataset,...n.attributes||{}};o.push({container:t,root:g({Block:e,container:t,props:n,attributes:c,errorBoundaryProps:a})})})),o};var _=r(195);const y=window.wp.apiFetch;var f=r.n(y);r(6919);var k=r(3240),x=r.n(k);const R=["a","b","em","i","strong","p","br"],j=["target","href","rel","name","download"],S=(e,t)=>{const r=t?.tags||R,s=t?.attr||j;return x().sanitize(e,{ALLOWED_TAGS:r,ALLOWED_ATTR:s})};var E=r(4921);const O=({label:e,screenReaderLabel:t,wrapperElement:r,wrapperProps:o={},allowHTML:i=!1})=>{let n;const a=null!=e,c=null!=t;return!a&&c?(n=r||"span",o={...o,className:(0,E.A)(o.className,"screen-reader-text")},(0,w.jsx)(n,{...o,children:t})):(n=r||s.Fragment,a&&c&&e!==t?(0,w.jsxs)(n,{...o,children:[i?(0,w.jsx)(s.RawHTML,{children:S(e,{tags:["b","em","i","strong","p","br","span"],attr:["style"]})}):(0,w.jsx)("span",{"aria-hidden":"true",children:e}),(0,w.jsx)("span",{className:"screen-reader-text",children:t})]}):(0,w.jsx)(n,{...o,children:e}))},L=({onClick:e,label:t=(0,o.__)("Load more","woocommerce"),screenReaderLabel:r=(0,o.__)("Load more","woocommerce")})=>(0,w.jsx)("div",{className:"wp-block-button wc-block-load-more wc-block-components-load-more",children:(0,w.jsx)("button",{className:"wp-block-button__link",onClick:e,children:(0,w.jsx)(O,{label:t,screenReaderLabel:r})})}),T=window.wc.blocksComponents;r(6878);const P=({onChange:e,readOnly:t,value:r})=>(0,w.jsx)(T.SortSelect,{className:"wc-block-review-sort-select wc-block-components-review-sort-select",label:(0,o.__)("Order by","woocommerce"),onChange:e,options:[{key:"most-recent",label:(0,o.__)("Most recent","woocommerce")},{key:"highest-rating",label:(0,o.__)("Highest rating","woocommerce")},{key:"lowest-rating",label:(0,o.__)("Lowest rating","woocommerce")}],readOnly:t,screenReaderLabel:(0,o.__)("Order reviews by","woocommerce"),value:r});function A(e){let t,r,s,o=[];for(let i=0;i<e.length;i++)t=e.substring(i),r=t.match(/^&[a-z0-9#]+;/),r?(s=r[0],o.push(s),i+=s.length-1):o.push(e[i]);return o}const N=(e,t,r="...")=>{const s=function(e,t){const r=(t=t||{}).limit||100,s=void 0===t.preserveTags||t.preserveTags,o=void 0!==t.wordBreak&&t.wordBreak,i=t.suffix||"...",n=t.moreLink||"",a=t.moreText||"»",c=t.preserveWhiteSpace||!1,l=e.replace(/</g,"\n<").replace(/>/g,">\n").replace(/\n\n/g,"\n").replace(/^\n/g,"").replace(/\n$/g,"").split("\n");let d,p,w,m,h,u,v=0,g=[],b=!1;for(let e=0;e<l.length;e++){if(d=l[e],m=c?d:d.replace(/[ ]+/g," "),!d.length)continue;const t=A(m);if("<"!==d[0])if(v>=r)d="";else if(v+t.length>=r){if(p=r-v," "===t[p-1])for(;p&&(p-=1," "===t[p-1]););else w=t.slice(p).indexOf(" "),o||(-1!==w?p+=w:p=d.length);if(d=t.slice(0,p).join("")+i,n){const e=document.createElement("a");e.href=n,e.style.display="inline",e.textContent=a,d+=e.outerHTML}v=r,b=!0}else v+=t.length;else if(s){if(v>=r)if(h=d.match(/[a-zA-Z]+/),u=h?h[0]:"",u)if("</"!==d.substring(0,2))g.push(u),d="";else{for(;g[g.length-1]!==u&&g.length;)g.pop();g.length&&(d=""),g.pop()}else d=""}else d="";l[e]=d}return{html:l.join("\n").replace(/\n/g,""),more:b}}(e,{suffix:r,limit:t});return s.html},M=(e,t,r)=>(t<=r?e.start=e.middle+1:e.end=e.middle-1,e),C=(e,t,r,s)=>{const o=((e,t,r)=>{let s={start:0,middle:0,end:e.length};for(;s.start<=s.end;)s.middle=Math.floor((s.start+s.end)/2),t.innerHTML=N(e,s.middle),s=M(s,t.clientHeight,r);return s.middle})(e,t,r);return N(e,o-s.length,s)},B={className:"read-more-content",ellipsis:"&hellip;",lessText:(0,o.__)("Read less","woocommerce"),maxLines:3,moreText:(0,o.__)("Read more","woocommerce")};class D extends s.Component{static defaultProps=B;constructor(e){super(e),this.state={isExpanded:!1,clampEnabled:null,content:e.children,summary:"."},this.reviewContent=(0,s.createRef)(),this.reviewSummary=(0,s.createRef)(),this.getButton=this.getButton.bind(this),this.onClick=this.onClick.bind(this)}componentDidMount(){this.setSummary()}componentDidUpdate(e){e.maxLines===this.props.maxLines&&e.children===this.props.children||this.setState({clampEnabled:null,summary:"."},this.setSummary)}setSummary(){if(this.props.children){const{maxLines:e,ellipsis:t}=this.props;if(!this.reviewSummary.current||!this.reviewContent.current)return;const r=(this.reviewSummary.current.clientHeight+1)*e+1,s=this.reviewContent.current.clientHeight+1>r;this.setState({clampEnabled:s}),s&&this.setState({summary:C(this.reviewContent.current.innerHTML,this.reviewSummary.current,r,t)})}}getButton(){const{isExpanded:e}=this.state,{className:t,lessText:r,moreText:s}=this.props,o=e?r:s;if(o)return(0,w.jsx)("a",{href:"#more",className:t+"__read_more",onClick:this.onClick,"aria-expanded":!e,role:"button",children:o})}onClick(e){e.preventDefault();const{isExpanded:t}=this.state;this.setState({isExpanded:!t})}render(){const{className:e}=this.props,{content:t,summary:r,clampEnabled:s,isExpanded:o}=this.state;return t?!1===s?(0,w.jsx)("div",{className:e,children:(0,w.jsx)("div",{ref:this.reviewContent,children:t})}):(0,w.jsxs)("div",{className:e,children:[(!o||null===s)&&(0,w.jsx)("div",{ref:this.reviewSummary,"aria-hidden":o,dangerouslySetInnerHTML:{__html:r}}),(o||null===s)&&(0,w.jsx)("div",{ref:this.reviewContent,"aria-hidden":!o,children:t}),this.getButton()]}):null}}const I=D,F=window.wp.htmlEntities;function H(e,t,r){return r||!e?(0,w.jsx)("div",{className:"wc-block-review-list-item__image wc-block-components-review-list-item__image"}):(0,w.jsxs)("div",{className:"wc-block-review-list-item__image wc-block-components-review-list-item__image",children:["product"===t?(0,w.jsx)("img",{"aria-hidden":"true",alt:e.product_image?.alt||"",src:e.product_image?.thumbnail||""}):(0,w.jsx)("img",{"aria-hidden":"true",alt:"",src:e.reviewer_avatar_urls[96]||""}),e.verified&&(0,w.jsx)("div",{className:"wc-block-review-list-item__verified wc-block-components-review-list-item__verified",title:(0,o.__)("Verified buyer","woocommerce"),children:(0,o.__)("Verified buyer","woocommerce")})]})}function G(e){return(0,w.jsx)(I,{maxLines:10,moreText:(0,o.__)("Read full review","woocommerce"),lessText:(0,o.__)("Hide full review","woocommerce"),className:"wc-block-review-list-item__text wc-block-components-review-list-item__text",children:(0,w.jsx)("div",{dangerouslySetInnerHTML:{__html:e.review||""}})})}function U(e,t){return(0,w.jsx)("div",{className:"wc-block-review-list-item__product wc-block-components-review-list-item__product",children:(0,w.jsx)("a",{href:e.product_permalink,"aria-labelledby":t,children:(0,F.decodeEntities)(e.product_name)})})}function W(e){const{reviewer:t=""}=e;return(0,w.jsx)("div",{className:"wc-block-review-list-item__author wc-block-components-review-list-item__author",children:t})}function $(e){const{date_created:t,formatted_date_created:r}=e;return(0,w.jsx)("time",{className:"wc-block-review-list-item__published-date wc-block-components-review-list-item__published-date",dateTime:t,children:r})}function q(e,t){const{rating:r}=e,s={width:r/5*100+"%"},i=(0,o.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,o.__)("Rated %f out of 5","woocommerce"),r),n={__html:(0,o.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,o.__)("Rated %s out of 5","woocommerce"),(0,o.sprintf)('<strong class="rating">%f</strong>',r))};return(0,w.jsx)("div",{id:t,"aria-label":`${(0,F.decodeEntities)(e.product_name)} ${i}`,className:"wc-block-review-list-item__rating wc-block-components-review-list-item__rating",children:(0,w.jsx)("div",{"aria-hidden":"true",className:`wc-block-review-list-item__rating__stars wc-block-components-review-list-item__rating__stars wc-block-review-list-item__rating__stars--${r}`,role:"img",children:(0,w.jsx)("span",{style:s,dangerouslySetInnerHTML:n})})})}r(7313);const J=({attributes:e,review:t={}})=>{const{imageType:r,showReviewDate:o,showReviewerName:i,showReviewImage:n,showReviewRating:a,showReviewContent:c,showProductName:l}=e,{rating:d}=t,p=!(Object.keys(t).length>0),m=Number.isFinite(d)&&a,h=(0,s.useId)();return(0,w.jsxs)("li",{className:(0,E.A)("wc-block-review-list-item__item","wc-block-components-review-list-item__item",{"is-loading":p,"wc-block-components-review-list-item__item--has-image":n}),"aria-hidden":p,children:[(l||o||i||n||m)&&(0,w.jsxs)("div",{className:"wc-block-review-list-item__info wc-block-components-review-list-item__info",children:[n&&H(t,r,p),(l||i||m||o)&&(0,w.jsxs)("div",{className:"wc-block-review-list-item__meta wc-block-components-review-list-item__meta",children:[m&&q(t,h),l&&U(t,h),i&&W(t),o&&$(t)]})]}),c&&G(t)]})};r(5183);const V=({attributes:e,reviews:t})=>{const r=(0,i.getSetting)("showAvatars",!0),s=(0,i.getSetting)("reviewRatingsEnabled",!0),o=(r||"product"===e.imageType)&&e.showReviewImage,n=s&&e.showReviewRating,a={...e,showReviewImage:o,showReviewRating:n};return(0,w.jsx)("ul",{className:"wc-block-review-list wc-block-components-review-list",children:0===t.length?(0,w.jsx)(J,{attributes:a}):t.map(((e,t)=>(0,w.jsx)(J,{attributes:a,review:e},e.id||t)))})};var z=r(923),X=r.n(z);const Z=(e=>{var t;class r extends s.Component{isPreview=!!this.props.attributes.previewReviews;delayedAppendReviews=(null!==(t=this.props.delayFunction)&&void 0!==t?t:e=>e)(this.appendReviews);isMounted=!1;state={error:null,loading:!0,reviews:this.isPreview&&this.props.attributes?.previewReviews?this.props.attributes.previewReviews:[],totalReviews:this.isPreview&&this.props.attributes?.previewReviews?this.props.attributes.previewReviews.length:0};componentDidMount(){this.isMounted=!0,this.replaceReviews()}componentDidUpdate(e){e.reviewsToDisplay<this.props.reviewsToDisplay?this.delayedAppendReviews():this.shouldReplaceReviews(e,this.props)&&this.replaceReviews()}shouldReplaceReviews(e,t){return e.orderby!==t.orderby||e.order!==t.order||e.productId!==t.productId||!X()(e.categoryIds,t.categoryIds)}componentWillUnmount(){this.isMounted=!1,"cancel"in this.delayedAppendReviews&&"function"==typeof this.delayedAppendReviews.cancel&&this.delayedAppendReviews.cancel()}getArgs(e){const{categoryIds:t,order:r,orderby:s,productId:o,reviewsToDisplay:i}=this.props,n={order:r,orderby:s,per_page:i-e,offset:e};if(t){const e=Array.isArray(t)?t:JSON.parse(t);n.category_id=Array.isArray(e)?e.join(","):e}return o&&(n.product_id=o),n}replaceReviews(){var e;if(this.isPreview)return;const t=null!==(e=this.props.onReviewsReplaced)&&void 0!==e?e:()=>{};this.updateListOfReviews().then(t)}appendReviews(){var e;if(this.isPreview)return;const t=null!==(e=this.props.onReviewsAppended)&&void 0!==e?e:()=>{},{reviewsToDisplay:r}=this.props,{reviews:s}=this.state;r<=s.length||this.updateListOfReviews(s).then(t)}updateListOfReviews(e=[]){const{reviewsToDisplay:t}=this.props,{totalReviews:r}=this.state,s=Math.min(r,t)-e.length;return this.setState({loading:!0,reviews:e.concat(Array(s).fill({}))}),(o=this.getArgs(e.length),f()({path:"/wc/store/v1/products/reviews?"+Object.entries(o).map((e=>e.join("="))).join("&"),parse:!1}).then((e=>e.json().then((t=>({reviews:t,totalReviews:parseInt(e.headers.get("x-wp-total"),10)})))))).then((({reviews:t,totalReviews:r})=>(this.isMounted&&this.setState({reviews:e.filter((e=>Object.keys(e).length)).concat(t),totalReviews:r,loading:!1,error:null}),{newReviews:t}))).catch(this.setError);var o}setError=async e=>{var t;if(!this.isMounted)return;const r=null!==(t=this.props.onReviewsLoadError)&&void 0!==t?t:()=>{},s=await(async e=>{if(!("json"in e))return{code:e.code||"",message:e.message,type:e.type||"general"};try{const t=await e.json();return{code:t.code||"",message:t.message,type:t.type||"api"}}catch(e){return{message:e.message,type:"general"}}})(e);this.setState({reviews:[],loading:!1,error:s}),r(s)};render(){const{reviewsToDisplay:t}=this.props,{error:r,loading:s,reviews:o,totalReviews:i}=this.state;return(0,w.jsx)(e,{...this.props,error:r,isLoading:s,reviews:o.slice(0,t),totalReviews:i})}}const{displayName:o=e.name||"Component"}=e;return r.displayName=`WithReviews(${o})`,r})((({attributes:e,onAppendReviews:t,onChangeOrderby:r,reviews:s,sortSelectValue:n,totalReviews:a})=>{if(0===s.length)return null;const c=(0,i.getSetting)("reviewRatingsEnabled",!0);return(0,w.jsxs)(w.Fragment,{children:[e.showOrderby&&c&&(0,w.jsx)(P,{value:n,onChange:r}),(0,w.jsx)(V,{attributes:e,reviews:s}),e.showLoadMore&&a>s.length&&(0,w.jsx)(L,{onClick:t,screenReaderLabel:(0,o.__)("Load more reviews","woocommerce")})]})}));class K extends s.Component{constructor(e){super(e);const{attributes:t}=this.props;this.state={orderby:t?.orderby,reviewsToDisplay:this.getReviewsOnPageLoad()},this.onAppendReviews=this.onAppendReviews.bind(this),this.onChangeOrderby=this.onChangeOrderby.bind(this)}getReviewsOnPageLoad(){const{attributes:e}=this.props;return"number"==typeof e.reviewsOnPageLoad?e.reviewsOnPageLoad:parseInt(e.reviewsOnPageLoad,10)}getReviewsOnLoadMore(){const{attributes:e}=this.props;return"number"==typeof e.reviewsOnLoadMore?e.reviewsOnLoadMore:parseInt(e.reviewsOnLoadMore,10)}onAppendReviews(){const{reviewsToDisplay:e}=this.state;this.setState({reviewsToDisplay:e+this.getReviewsOnLoadMore()})}onChangeOrderby(e){this.setState({orderby:e.target.value,reviewsToDisplay:this.getReviewsOnPageLoad()})}onReviewsAppended({newReviews:e}){(0,_.speak)((0,o.sprintf)(/* translators: %d is the count of reviews loaded. */ /* translators: %d is the count of reviews loaded. */
(0,o._n)("%d review loaded.","%d reviews loaded.",e.length,"woocommerce"),e.length))}onReviewsReplaced(){(0,_.speak)((0,o.__)("Reviews list updated.","woocommerce"))}onReviewsLoadError(){(0,_.speak)((0,o.__)("There was an error loading the reviews.","woocommerce"))}render(){const{attributes:e}=this.props,{categoryIds:t,productId:r}=e,{reviewsToDisplay:s}=this.state,{order:o,orderby:n}=(e=>{if((0,i.getSetting)("reviewRatingsEnabled",!0)){if("lowest-rating"===e)return{order:"asc",orderby:"rating"};if("highest-rating"===e)return{order:"desc",orderby:"rating"}}return{order:"desc",orderby:"date_gmt"}})(this.state.orderby);return(0,w.jsx)(Z,{attributes:e,categoryIds:t,onAppendReviews:this.onAppendReviews,onChangeOrderby:this.onChangeOrderby,onReviewsAppended:this.onReviewsAppended,onReviewsLoadError:this.onReviewsLoadError,onReviewsReplaced:this.onReviewsReplaced,order:o,orderby:n,productId:r,reviewsToDisplay:s,sortSelectValue:this.state.orderby})}}(e=>{const t=Array.from(document.body.querySelectorAll(v.join(","))),{Block:r,getProps:s,getErrorBoundaryProps:o,selector:i,options:n={multiple:!0}}=e,a=(({Block:e,getProps:t,getErrorBoundaryProps:r,selector:s,wrappers:o,options:i})=>{let n=Array.from(document.body.querySelectorAll(s));return o&&o.length>0&&(n=n.filter((e=>!((e,t)=>t.some((t=>t.contains(e)&&!t.isSameNode(e))))(e,o)))),!1===i?.multiple&&(n=n.slice(0,1)),b({Block:e,containers:n,getProps:t,getErrorBoundaryProps:r})})({Block:r,getProps:s,getErrorBoundaryProps:o,selector:i,options:n,wrappers:t});t.forEach((t=>{t.addEventListener("wc-blocks_render_blocks_frontend",(()=>{(({Block:e,getProps:t,getErrorBoundaryProps:r,selector:s,wrapper:o,options:i})=>{let n=Array.from(o.querySelectorAll(s));!1===i?.multiple&&(n=n.slice(0,1)),b({Block:e,containers:n,getProps:t,getErrorBoundaryProps:r})})({...e,wrapper:t})}))}))})({selector:"\n\t.wp-block-woocommerce-all-reviews,\n\t.wp-block-woocommerce-reviews-by-product,\n\t.wp-block-woocommerce-reviews-by-category\n",Block:K,getProps:e=>({attributes:{showOrderby:"true"===e.dataset.showOrderby,showLoadMore:"true"===e.dataset.showLoadMore,showReviewDate:e.classList.contains("has-date"),showReviewerName:e.classList.contains("has-name"),showReviewImage:e.classList.contains("has-image"),showReviewRating:e.classList.contains("has-rating"),showReviewContent:e.classList.contains("has-content"),showProductName:e.classList.contains("has-product-name")}})})},5893:()=>{},6919:()=>{},7313:()=>{},5183:()=>{},6878:()=>{},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},195:e=>{"use strict";e.exports=window.wp.a11y},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual}},o={};function i(e){var t=o[e];if(void 0!==t)return t.exports;var r=o[e]={exports:{}};return s[e].call(r.exports,r,r.exports,i),r.exports}i.m=s,e=[],i.O=(t,r,s,o)=>{if(!r){var n=1/0;for(d=0;d<e.length;d++){for(var[r,s,o]=e[d],a=!0,c=0;c<r.length;c++)(!1&o||n>=o)&&Object.keys(i.O).every((e=>i.O[e](r[c])))?r.splice(c--,1):(a=!1,o<n&&(n=o));if(a){e.splice(d--,1);var l=s();void 0!==l&&(t=l)}}return t}o=o||0;for(var d=e.length;d>0&&e[d-1][2]>o;d--)e[d]=e[d-1];e[d]=[r,s,o]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,s){if(1&s&&(e=this(e)),8&s)return e;if("object"==typeof e&&e){if(4&s&&e.__esModule)return e;if(16&s&&"function"==typeof e.then)return e}var o=Object.create(null);i.r(o);var n={};t=t||[null,r({}),r([]),r(r)];for(var a=2&s&&e;"object"==typeof a&&!~t.indexOf(a);a=r(a))Object.getOwnPropertyNames(a).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,i.d(o,n),o},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=258,(()=>{var e={258:0};i.O.j=t=>0===e[t];var t=(t,r)=>{var s,o,[n,a,c]=r,l=0;if(n.some((t=>0!==e[t]))){for(s in a)i.o(a,s)&&(i.m[s]=a[s]);if(c)var d=c(i)}for(t&&t(r);l<n.length;l++)o=n[l],i.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return i.O(d)},r=globalThis.webpackChunkwebpackWcBlocksFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksFrontendJsonp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var n=i.O(void 0,[763],(()=>i(3010)));n=i.O(n),(wc=void 0===wc?{}:wc).reviews=n})();