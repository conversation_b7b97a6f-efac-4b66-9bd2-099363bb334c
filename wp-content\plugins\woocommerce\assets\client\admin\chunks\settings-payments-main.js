"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[1226],{47804:(e,t,n)=>{n.d(t,{A:()=>i});var s=n(5573),o=n(39793);const i=(0,o.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,o.jsx)(s.<PERSON>,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"})})},75854:(e,t,n)=>{n.d(t,{CX:()=>m,Ci:()=>_}),n(51609);var s=n(27723),o=n(10432),i=n(98404),a=n(3511),c=n(33623),r=n(59530),l=n(8148),d=n(39793);const m="test_account",_=[{id:"payment_methods",order:1,type:"backend",label:(0,s.__)("Choose your payment methods","woocommerce"),content:(0,d.jsx)(a.A,{})},{id:"wpcom_connection",order:2,type:"backend",label:(0,s.sprintf)((0,s.__)("Connect with %s","woocommerce"),"WordPress.com"),content:(0,d.jsx)(o.A,{}),dependencies:["payment_methods"]},{id:"activate_payments",order:3,type:"frontend",label:(0,s.__)("Activate payments","woocommerce"),subSteps:[{id:"test_or_live_account",order:1,type:"frontend",label:(0,s.__)("Test or live account","woocommerce"),dependencies:["wpcom_connection"],content:(0,d.jsx)(r.A,{})},{id:m,order:2,type:"backend",label:(0,s.__)("Ready to test payments","woocommerce"),dependencies:["test_or_live_account"],content:(0,d.jsx)(c.A,{})},{id:"business_verification",order:3,type:"backend",label:(0,s.__)("Activate payments","woocommerce"),dependencies:["test_or_live_account"],content:(0,d.jsx)(i.A,{})}]},{id:"finish",order:4,type:"frontend",label:(0,s.__)("Submit for verification","woocommerce"),dependencies:["business_verification"],content:(0,d.jsx)(l.A,{})}];(0,s.__)("Choose your payment methods","woocommerce"),a.A,(0,s.sprintf)((0,s.__)("Connect with %s","woocommerce"),"WordPress.com"),o.A,(0,s.__)("Activate payments","woocommerce"),i.A},47580:(e,t,n)=>{n.r(t),n.d(t,{SettingsPaymentsMain:()=>ge,default:()=>pe});var s=n(51609),o=n.n(s),i=n(27723),a=n(40314),c=n(47143),r=n(86087),l=n(1455),d=n.n(l),m=n(96476),_=n(56427);function u(e){const{createNotice:t}=(0,c.dispatch)("core/notices");e.error_data&&e.errors&&Object.keys(e.errors).length?Object.keys(e.errors).forEach((n=>{t("error",e.errors[n].join(" "))})):e.message&&t(e.code?"error":"success",e.message)}var g=n(72553),p=n(18537),h=n(39793);const y=()=>(0,h.jsxs)("div",{className:"other-payment-gateways__content__grid-item",children:[(0,h.jsx)("div",{className:"grid-item-placeholder__img"}),(0,h.jsxs)("div",{className:"other-payment-gateways__content__grid-item__content grid-item-placeholder__content",children:[(0,h.jsx)("span",{className:"grid-item-placeholder__title"}),(0,h.jsx)("span",{className:"grid-item-placeholder__description"}),(0,h.jsx)("div",{className:"grid-item-placeholder__actions"})]})]});var v=n(98846),w=n(56109),x=n(1069);const b=({variant:e,suggestionId:t})=>{const[n,s]=(0,r.useState)(!1),o=(0,r.useRef)(null),a=e=>{const n=e.target.closest(".woocommerce-official-extension-badge__container");o.current&&n!==o.current||(s((e=>!e)),(0,x.TH)("official_badge_click",{suggestion_id:t}))};return(0,h.jsx)(v.Pill,{className:"woocommerce-official-extension-badge",children:(0,h.jsxs)("span",{className:"woocommerce-official-extension-badge__container",tabIndex:0,role:"button",ref:o,onClick:a,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||a(e)},children:[(0,h.jsx)("img",{src:w.GZ+"images/icons/official-extension.svg",alt:(0,i.__)("Official WooCommerce extension badge","woocommerce")}),"expanded"===e&&(0,h.jsx)("span",{children:(0,i.__)("Official","woocommerce")}),n&&(0,h.jsx)(_.Popover,{className:"woocommerce-official-extension-badge-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{s(!1)},children:(0,h.jsx)("div",{className:"components-popover__content-container",children:(0,h.jsx)("p",{children:(0,r.createInterpolateElement)((0,i.__)("This is an Official WooCommerce payment extension. <learnMoreLink />","woocommerce"),{learnMoreLink:(0,h.jsx)(v.Link,{href:"https://woocommerce.com/learn-more-about-official-partner-badging/",target:"_blank",rel:"noreferrer",type:"external",onClick:()=>{(0,x.TH)("official_badge_learn_more_click",{suggestion_id:t})},children:(0,i.__)("Learn more","woocommerce")})})})})})]})})};var f=n(24148),j=n(73290);const k=({status:e,message:t,popoverContent:n})=>{const[s,o]=(0,r.useState)(!1),a=(0,r.useRef)(null),c=e=>{const t=e.target.closest(".woocommerce-status-badge__icon-container");a.current&&t!==a.current||o((e=>!e))};return(0,h.jsxs)(v.Pill,{className:`woocommerce-status-badge ${(()=>{switch(e){case"active":case"has_incentive":return"woocommerce-status-badge--success";case"needs_setup":case"test_mode":case"test_account":return"woocommerce-status-badge--warning";case"recommended":case"inactive":return"woocommerce-status-badge--info";default:return""}})()}`,children:[t||(()=>{switch(e){case"active":return(0,i.__)("Active","woocommerce");case"inactive":return(0,i.__)("Inactive","woocommerce");case"needs_setup":return(0,i.__)("Action needed","woocommerce");case"test_mode":return(0,i.__)("Test mode","woocommerce");case"test_account":return(0,i.__)("Test account","woocommerce");case"recommended":return(0,i.__)("Recommended","woocommerce");default:return""}})(),n&&(0,h.jsxs)("span",{className:"woocommerce-status-badge__icon-container",tabIndex:0,role:"button",ref:a,onClick:c,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||c(e)},children:[(0,h.jsx)(f.A,{className:"woocommerce-status-badge-icon",size:16,icon:j.A}),s&&(0,h.jsx)(_.Popover,{className:"woocommerce-status-badge-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{o(!1)},children:(0,h.jsx)("div",{className:"components-popover__content-container",children:n})})]})]})},N=({incentive:e})=>(0,h.jsx)(k,{status:"has_incentive",message:e.badge,popoverContent:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("p",{className:"woocommerce-incentive-popover__title",children:e.title}),(0,h.jsx)("p",{className:"woocommerce-incentive-popover__terms",children:(0,r.createInterpolateElement)((0,i.__)("See <termsLink /> for details.","woocommerce"),{termsLink:(0,h.jsx)(v.Link,{href:e.tc_url,target:"_blank",rel:"noreferrer",type:"external",children:(0,i.__)("Terms and Conditions","woocommerce")})})})]})}),P=({suggestions:e,suggestionCategories:t,installingPlugin:n,setUpPlugin:s,isFetching:o,morePaymentOptionsLink:a})=>{const c=new URLSearchParams(window.location.search),l="expanded"===c.get("other_pes_section"),[d,m]=(0,r.useState)(l),[u,v]=(0,r.useState)(""),w=(0,r.useRef)({}),f=(e,t)=>{var n;const s=e.target.closest(".other-payment-gateways__content__title__icon-container"),o=null!==(n=w.current[t])&&void 0!==n?n:null;o&&s!==o||v(t===u?"":t)},j=()=>{v("")},k=()=>{const e=!d;(0,x.TH)("other_payment_options_section_click",{action:e?"expand":"collapse"}),m(e),c.set("other_pes_section",e?"expanded":"collapsed"),window.history.replaceState({},document.title,window.location.pathname+"?"+c.toString())},P=(0,r.useMemo)((()=>t.map((t=>({category:t,suggestions:e.filter((e=>e._type===t.id))})))),[e,t]),S=(0,r.useMemo)((()=>o?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("div",{className:"other-payment-gateways__header__title-image-placeholder"}),(0,h.jsx)("div",{className:"other-payment-gateways__header__title-image-placeholder"}),(0,h.jsx)("div",{className:"other-payment-gateways__header__title-image-placeholder"})]}):P.map((({suggestions:e})=>0===e.length?null:e.map((e=>(0,h.jsx)("img",{src:e.icon,alt:e.title+" small logo",width:"24",height:"24",className:"other-payment-gateways__header__title-image"},e.id)))))),[P,o]),O=(0,r.useMemo)((()=>o?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(y,{}),(0,h.jsx)(y,{}),(0,h.jsx)(y,{})]}):P.map((({category:e,suggestions:t})=>0===t.length?null:(0,h.jsxs)("div",{className:"other-payment-gateways__content__category-container",children:[(0,h.jsxs)("div",{className:"other-payment-gateways__content__title",children:[(0,h.jsx)("h3",{className:"other-payment-gateways__content__title__h3",children:(0,p.decodeEntities)(e.title)}),(0,h.jsxs)("span",{className:"other-payment-gateways__content__title__icon-container",onClick:t=>f(t,e.id),onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||f(t,e.id)},tabIndex:0,role:"button",ref:t=>{w.current[e.id]=t},children:[(0,h.jsx)(g.A,{icon:"info-outline",className:"other-payment-gateways__content__title__icon"}),e.id===u&&(0,h.jsx)(_.Popover,{className:"other-payment-gateways__content__title-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:j,children:(0,h.jsx)("div",{className:"components-popover__content-container",children:(0,h.jsx)("p",{children:(0,p.decodeEntities)(e.description)})})})]})]}),(0,h.jsx)("div",{className:"other-payment-gateways__content__grid",children:t.map((e=>(0,h.jsxs)("div",{className:"other-payment-gateways__content__grid-item",children:[(0,h.jsx)("img",{className:"other-payment-gateways__content__grid-item-image",src:e.icon,alt:(0,p.decodeEntities)(e.title)+" logo"}),(0,h.jsxs)("div",{className:"other-payment-gateways__content__grid-item__content",children:[(0,h.jsxs)("span",{className:"other-payment-gateways__content__grid-item__content__title",children:[e.title,e?._incentive&&(0,h.jsx)(N,{incentive:e._incentive}),(0,h.jsx)(b,{variant:"expanded",suggestionId:e.id})]}),(0,h.jsx)("span",{className:"other-payment-gateways__content__grid-item__content__description",children:(0,p.decodeEntities)(e.description)}),(0,h.jsx)("div",{className:"other-payment-gateways__content__grid-item__content__actions",children:(0,h.jsx)(_.Button,{variant:"link",onClick:()=>{var t;return s(e,null,"not_installed"===e.plugin.status&&null!==(t=e._links?.attach?.href)&&void 0!==t?t:null,"wc_settings_payments__other_payment_options")},isBusy:n===e.id,disabled:!!n,children:n===e.id?(0,i.__)("Installing","woocommerce"):(0,i.__)("Install","woocommerce")})})]})]},e.id)))})]},e.id)))),[P,n,s,o,u]);return(0,h.jsxs)("div",{className:"other-payment-gateways"+(d?" is-expanded":""),children:[(0,h.jsxs)("div",{className:"other-payment-gateways__header",onClick:k,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||k()},role:"button",tabIndex:0,"aria-expanded":d,children:[(0,h.jsxs)("div",{className:"other-payment-gateways__header__title",children:[(0,h.jsx)("span",{children:(0,i.__)("More payment options","woocommerce")}),!d&&(0,h.jsx)(h.Fragment,{children:S})]}),(0,h.jsx)(g.A,{className:"other-payment-gateways__header__arrow",icon:d?"chevron-up":"chevron-down"})]}),d&&(0,h.jsxs)("div",{className:"other-payment-gateways__content",children:[O,(0,h.jsx)("div",{className:"other-payment-gateways__content__external-icon",children:a})]})]})};var S=n(4921),O=n(15703),C=n(46608),I=n(36849),T=n(21913),A=n(29491),E=n(90700),M=n(72744);const D=(e,t)=>{const n=t,{changes:s,type:o,props:i}=n,{items:a}=i,{selectedItem:c}=e;switch(o){case T.WM.stateChangeTypes.ItemClick:return{...s,isOpen:!0,highlightedIndex:e.highlightedIndex};case T.WM.stateChangeTypes.ToggleButtonKeyDownArrowDown:return{selectedItem:a[c?Math.min(a.indexOf(c)+1,a.length-1):0],isOpen:!0};case T.WM.stateChangeTypes.ToggleButtonKeyDownArrowUp:return{selectedItem:a[c?Math.max(a.indexOf(c)-1,0):a.length-1],isOpen:!0};default:return s}},H=e=>e.normalize("NFD").replace(/[\u0300-\u036f]/g,""),B=({name:e,className:t,label:n,describedBy:o,options:a,onChange:c,value:l,placeholder:d,children:m})=>{var u;const[g,p]=(0,s.useState)(""),y=(0,A.useThrottle)((0,r.useCallback)(((e,t)=>new Set(t.filter((t=>{var n;return`${H(null!==(n=t.name)&&void 0!==n?n:"")}`.toLowerCase().includes(H(e.toLowerCase()))})))),[]),200),v=""!==g?null!==(u=y(g,a))&&void 0!==u?u:new Set:new Set(a),{getToggleButtonProps:x,getMenuProps:b,getItemProps:j,isOpen:k,highlightedIndex:N,selectedItem:P,closeMenu:O}=(0,T.WM)({initialSelectedItem:l,items:[...v],stateReducer:D}),C=((e,t)=>{const n=t.find((t=>t.key===e));return n?.name?n.name:""})(l.key,a),I=P?P.key:"",B=(0,s.useRef)(null),R=(0,s.useRef)(null),L=(0,r.useCallback)((e=>{const t=B.current,n=t?.querySelector(`[data-index="${e}"]`);n&&n.scrollIntoView({block:"nearest"})}),[B]),F=""!==g,U=b({className:"components-country-select-control__menu","aria-hidden":!k,ref:B}),W=(0,r.useCallback)((e=>{e.stopPropagation(),c(I),O()}),[c,I,O]),G=(0,r.useCallback)((e=>{e.stopPropagation(),"Enter"===e.key&&c(I)}),[c,I]),$=(0,r.useCallback)((e=>{e.preventDefault(),""!==g&&p(""),null!==P&&setTimeout((()=>{L(a.indexOf(P))}),10)}),[g,P]);return(0,r.useEffect)((()=>{if(k&&null!==P){const e=Array.from(v).indexOf(P);L(e)}}),[k]),(0,h.jsxs)("div",{className:(0,S.A)("woopayments components-country-select-control",t),children:[(0,h.jsxs)(_.Button,{...x({"aria-label":n,"aria-labelledby":void 0,"aria-describedby":o||(C?(0,i.sprintf)((0,i.__)("Currently selected: %s","woocommerce"),C):(0,i.__)("No selection","woocommerce")),className:(0,S.A)("components-country-select-control__button",{placeholder:!C}),name:e,onKeyDown:G}),children:[(0,h.jsxs)("span",{className:"components-country-select-control__button-value",children:[(0,h.jsx)("span",{className:"components-country-select-control__label",children:n}),C||d]}),(0,h.jsx)(f.A,{icon:E.A,className:"components-custom-select-control__button-icon"})]}),(0,h.jsx)("div",{...U,children:k&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("div",{className:"components-country-select-control__search wc-settings-prevent-change-event",children:[(0,h.jsx)("input",{className:"components-country-select-control__search--input",ref:R,type:"text",value:g,onChange:({target:e})=>p(e.value),tabIndex:-1,placeholder:(0,i.__)("Search","woocommerce")}),(0,h.jsx)("button",{className:"components-country-select-control__search--input-suffix",onClick:$,children:(z=F,z?(0,h.jsx)("img",{src:w.GZ+"images/icons/clear.svg",alt:(0,i.__)("Clear search","woocommerce")}):(0,h.jsx)("img",{src:w.GZ+"images/icons/search.svg",alt:(0,i.__)("Search","woocommerce")}))})]}),(0,h.jsx)("div",{className:"components-country-select-control__list",children:[...v].map(((e,t)=>(0,s.createElement)("div",{...j({item:e,index:t,key:e.key,className:(0,S.A)(e.className,"components-country-select-control__item",{"is-highlighted":t===N}),"data-index":t,style:e.style}),key:e.key},e.key===I&&(0,h.jsx)(f.A,{icon:M.A,className:"components-country-select-control__item-icon"}),m?m(e):e.name)))}),(0,h.jsx)("div",{className:"components-country-select-control__apply",children:(0,h.jsx)("button",{className:"components-button is-primary",onClick:W,children:(0,i.__)("Apply","woocommerce")})})]})})]});var z};var R=n(51881),L=n(33068),F=n(15698),U=n(85816),W=n(12974);const G=({provider:e,pluginFile:t,isSuggestion:n,onToggle:s,links:o=[],canResetAccount:l=!1,setResetAccountModalVisible:d=()=>{},isEnabled:m=!1,canResetOnboarding:u=!1})=>{const{deactivatePlugin:g}=(0,c.useDispatch)(a.pluginsStore),[p,y]=(0,r.useState)(!1),[v,w]=(0,r.useState)(!1),[b,f]=(0,r.useState)(!1),{invalidateResolutionForStoreSelector:j,togglePaymentGateway:k,hidePaymentExtensionSuggestion:N}=(0,c.useDispatch)(a.paymentSettingsStore),{createErrorNotice:P,createSuccessNotice:S}=(0,c.useDispatch)("core/notices"),O={pricing:(0,i.__)("See pricing & fees","woocommerce"),about:(0,i.__)("Learn more","woocommerce"),terms:(0,i.__)("See Terms of Service","woocommerce"),support:(0,i.__)("Get support","woocommerce"),documentation:(0,i.__)("View documentation","woocommerce")},C=o.filter((e=>{switch(e._type){case"pricing":return!0;case"terms":case"about":return!m;case"documentation":case"support":return m;default:return!1}}));return(0,h.jsxs)(h.Fragment,{children:[C.map((t=>{const n=O[t._type];return n?(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{target:"_blank",href:t.url,onClick:()=>{(0,x.g2)("context_link_click",e,{link_type:t._type,link_url:t.url})},children:n})},t._type):null})),!!C.length&&(0,h.jsx)(_.CardDivider,{}),n&&(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{onClick:()=>{(0,x.g2)("context_link_click",e,{link_type:"hide_suggestion"}),(()=>{const t=e._links?.hide?.href;t?(f(!0),N(t).then((()=>{j("getPaymentProviders"),f(!1),s()})).catch((()=>{P((0,i.__)("Failed to hide the payments extension suggestion.","woocommerce")),f(!1),s()}))):P((0,i.__)("Failed to hide the payments extension suggestion.","woocommerce"))})()},isBusy:b,disabled:b,children:(0,i.__)("Hide suggestion","woocommerce")})},"hide-suggestion"),(l||u)&&(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{onClick:()=>{(0,x.g2)("context_link_click",e,{link_type:"reset_onboarding",with_account:l}),d(!0),s()},className:"components-button__danger",children:l?(0,i.__)("Reset account","woocommerce"):(0,i.__)("Reset onboarding","woocommerce")})},"reset-account"),!n&&!m&&(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{className:"components-button__danger",onClick:()=>{(0,x.g2)("context_link_click",e,{link_type:"deactivate_extension"}),y(!0),g(t).then((()=>{S((0,i.__)("The provider plugin was successfully deactivated.","woocommerce")),j("getPaymentProviders"),y(!1),s()})).catch((()=>{(0,x.g2)("extension_deactivation_failed",e,{reason:"error"}),P((0,i.__)("Failed to deactivate the provider plugin.","woocommerce")),y(!1),s()}))},isBusy:p,disabled:!t||p,children:(0,i.__)("Deactivate","woocommerce")})},"deactivate"),!n&&m&&(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{className:"components-button__danger",onClick:()=>{(0,x.g2)("context_link_click",e,{link_type:"disable"}),(()=>{const t=window.woocommerce_admin.nonces?.gateway_toggle||"";if(!t)return(0,x.g2)("disable_failed",e,{reason:"missing_nonce"}),void P((0,i.__)("Failed to disable the payments provider.","woocommerce"));w(!0),k(e.id,window.woocommerce_admin.ajax_url,t).then((()=>{j("getPaymentProviders"),w(!1),s()})).catch((()=>{(0,x.g2)("disable_failed",e,{reason:"error"}),P((0,i.__)("Failed to disable the payments provider.","woocommerce")),w(!1),s()}))})()},isBusy:v,disabled:v,children:(0,i.__)("Disable","woocommerce")})},"disable")]})};var $=n(28239);const z=({provider:e,label:t})=>{const[n,s]=(0,r.useState)(!1),o=(0,x.j4)(e.id)&&"gateway"===e._type&&e.state?.account_connected&&(e.onboarding?.state?.test_mode||!e.onboarding?.state?.completed)&&!!e.onboarding?._links?.reset?.href,i=!o&&(0,x.j4)(e.id)&&"gateway"===e._type&&!e.state?.account_connected&&e.onboarding?.state?.started&&!!e.onboarding?._links?.reset?.href;return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(v.EllipsisMenu,{label:t,renderContent:({onToggle:t})=>(0,h.jsx)(G,{provider:e,pluginFile:e.plugin.file,isSuggestion:"suggestion"===e._type,links:e.links,onToggle:t,isEnabled:e.state?.enabled,canResetAccount:o,setResetAccountModalVisible:s,canResetOnboarding:i}),focusOnMount:!0}),(0,h.jsx)($.Mk,{isOpen:n,onClose:()=>s(!1),hasAccount:e.state?.account_connected,isTestMode:e.onboarding?.state?.test_mode,resetUrl:e.onboarding?._links?.reset?.href})]})},K=({suggestion:e,installingPlugin:t,setUpPlugin:n,pluginInstalled:s,acceptIncentive:o,shouldHighlightIncentive:a=!1,...c})=>{const r=(0,x.O2)(e)?e._incentive:null;let l=(0,i.__)("Install","woocommerce");return s?l=(0,i.__)("Enable","woocommerce"):t===e.id&&(l=(0,i.__)("Installing","woocommerce")),(0,h.jsx)("div",{id:e.id,className:"transitions-disabled woocommerce-list__item woocommerce-list__item-enter-done "+((0,x.O2)(e)&&a?"has-incentive":""),...c,children:(0,h.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,h.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,h.jsx)(F.Gh,{}),e.icon&&(0,h.jsx)("img",{className:"woocommerce-list__item-image",src:e.icon,alt:e.title+" logo"})]}),(0,h.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,h.jsxs)("span",{className:"woocommerce-list__item-title",children:[e.title," ",!(0,x.O2)(e)&&(0,x.j4)(e.id)&&(0,h.jsx)(k,{status:"recommended"}),r&&(0,h.jsx)(N,{incentive:r}),(0,h.jsx)(b,{variant:"expanded",suggestionId:e.id})]}),(0,h.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,W.Ay)((0,p.decodeEntities)(e.description))}),(0,x.j4)(e.id)&&(0,h.jsx)(U.WooPaymentsMethodsLogos,{maxElements:10,tabletWidthBreakpoint:1080,mobileWidthBreakpoint:768,isWooPayEligible:(0,x.bU)(e)})]}),(0,h.jsx)("div",{className:"woocommerce-list__item-buttons",children:(0,h.jsx)("div",{className:"woocommerce-list__item-buttons__actions",children:(0,h.jsx)(_.Button,{variant:"primary",onClick:()=>{var t,i;s&&(0,x.g2)("enable_click",e,{incentive_id:r?r.promo_id:"none"}),r&&o(r.promo_id),n(e,null!==(t=e.onboarding?._links?.onboard?.href)&&void 0!==t?t:null,s?null:null!==(i=e._links?.attach?.href)&&void 0!==i?i:null,"wc_settings_payments__main_suggestion")},isBusy:t===e.id,disabled:!!t,children:l})})}),(0,h.jsx)("div",{className:"woocommerce-list__item-after",children:(0,h.jsx)("div",{className:"woocommerce-list__item-after__actions",children:(0,h.jsx)(z,{label:(0,i.__)("Payment provider actions","woocommerce"),provider:e})})})]})})};var q=n(75753),Z=n(22861);const V=({buttonText:e=(0,i.__)("Reactivate payments","woocommerce"),settingsHref:t})=>{const[n,s]=(0,r.useState)(!1),{createSuccessNotice:o,createErrorNotice:l}=(0,c.dispatch)("core/notices"),{invalidateResolutionForStoreSelector:m}=(0,c.useDispatch)(a.paymentSettingsStore);return(0,h.jsx)(_.Button,{variant:"primary",isBusy:n,disabled:n,onClick:e=>{e.preventDefault(),s(!0),(0,x.TH)("reactivate_payments_button_click",{provider_id:Z.$8,provider_extension_slug:Z.bw,suggestion_id:Z.eD}),d()({path:"/wc/v3/payments/settings",method:"POST",data:{is_test_mode_enabled:!1}}).then((()=>{o((0,i.sprintf)((0,i.__)("%s is now processing live payments (real payment methods and charges).","woocommerce"),"WooPayments"),{type:"snackbar",explicitDismiss:!1}),m("getPaymentProviders"),s(!1)})).catch((()=>{s(!1),(0,x.TH)("reactivate_payments_error",{provider_id:Z.$8,provider_extension_slug:Z.bw,suggestion_id:Z.eD}),l((0,i.sprintf)((0,i.__)("An error occurred. You will be redirected to the %s settings page to manage payments processing mode from there.","woocommerce"),"WooPayments"),{type:"snackbar",explicitDismiss:!0}),window.location.href=t}))},href:t,children:e})},Q=({gateway:e,installingPlugin:t,acceptIncentive:n,shouldHighlightIncentive:s,setIsOnboardingModalOpen:o,...a})=>{var c;const r=(0,x.j4)(e.id),l=(0,x.O2)(e)?e._incentive:null,d=(null!==(c=e.onboarding.recommended_payment_methods)&&void 0!==c?c:[]).length>0,m=!e.state.account_connected||e.state.account_connected&&!e.onboarding.state.started||e.state.account_connected&&e.onboarding.state.started&&!e.onboarding.state.completed;return(0,h.jsx)("div",{id:e.id,className:`transitions-disabled woocommerce-list__item woocommerce-list__item-enter-done woocommerce-item__payment-gateway ${r?"woocommerce-item__woocommerce-payments":""} ${(0,x.O2)(e)&&s?"has-incentive":""}`,...a,children:(0,h.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,h.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,h.jsx)(F.Gh,{}),e.icon&&(0,h.jsx)("img",{className:"woocommerce-list__item-image",src:e.icon,alt:e.title+" logo"})]}),(0,h.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,h.jsxs)("span",{className:"woocommerce-list__item-title",children:[e.title,l?(0,h.jsx)(N,{incentive:l}):(0,h.jsx)(k,{status:(()=>{if(m||!e.state.enabled&&e.state.needs_setup)return"needs_setup";if(e.state.enabled){if(e.state.account_connected){if(e.onboarding.state.test_mode)return"test_account";if(e.state.test_mode)return"test_mode"}return"active"}return"inactive"})()}),e._suggestion_id&&(0,h.jsx)(b,{variant:"expanded",suggestionId:e._suggestion_id}),e.supports?.includes("subscriptions")&&(0,h.jsx)(_.Tooltip,{placement:"top",text:(0,i.__)("Supports recurring payments","woocommerce"),children:(0,h.jsx)("img",{className:"woocommerce-list__item-recurring-payments-icon",src:w.GZ+"images/icons/recurring-payments.svg",alt:(0,i.__)("Icon to indicate support for recurring payments","woocommerce")})})]}),(0,h.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,W.Ay)((0,p.decodeEntities)(e.description))}),r&&(0,h.jsx)(U.WooPaymentsMethodsLogos,{maxElements:10,tabletWidthBreakpoint:1080,mobileWidthBreakpoint:768,isWooPayEligible:(0,x.bU)(e)})]}),(0,h.jsx)("div",{className:"woocommerce-list__item-buttons",children:(0,h.jsxs)("div",{className:"woocommerce-list__item-buttons__actions",children:[!e.state.enabled&&!m&&(0,h.jsx)(q.S,{gatewayProvider:e,settingsHref:e.management._links.settings.href,onboardingHref:e.onboarding._links.onboard.href,isOffline:!1,gatewayHasRecommendedPaymentMethods:d,installingPlugin:t,incentive:l,acceptIncentive:n,setOnboardingModalOpen:o,onboardingType:e.onboarding.type}),!m&&(0,h.jsx)(q.CS,{gatewayProvider:e,settingsHref:e.management._links.settings.href,isInstallingPlugin:!!t}),m&&(0,h.jsx)(q.PE,{gatewayProvider:e,settingsHref:e.management._links.settings.href,onboardingHref:e.onboarding._links.onboard.href,gatewayHasRecommendedPaymentMethods:d,installingPlugin:t,setOnboardingModalOpen:o,onboardingType:e.onboarding.type,incentive:l,acceptIncentive:n}),(0,x.j4)(e.id)&&!e.state.dev_mode&&e.state.account_connected&&e.onboarding.state.completed&&e.onboarding.state.test_mode&&(0,h.jsx)(q.LO,{acceptIncentive:n,installingPlugin:t,incentive:l,setOnboardingModalOpen:o,onboardingType:e.onboarding.type,disableTestAccountUrl:e.onboarding._links.disable_test_account?.href}),(0,x.j4)(e.id)&&!e.state.dev_mode&&e.state.account_connected&&e.onboarding.state.completed&&!e.onboarding.state.test_mode&&e.state.test_mode&&(0,h.jsx)(V,{settingsHref:e.management._links.settings.href})]})}),(0,h.jsx)("div",{className:"woocommerce-list__item-after",children:(0,h.jsx)("div",{className:"woocommerce-list__item-after__actions",children:(0,h.jsx)(z,{label:(0,i.__)("Payment Provider Options","woocommerce"),provider:e})})})]})})},Y=({providers:e,installedPluginSlugs:t,installingPlugin:n,setUpPlugin:s,acceptIncentive:o,shouldHighlightIncentive:c,updateOrdering:r,setIsOnboardingModalOpen:l})=>{const d=(0,L.Zp)();return(0,h.jsx)(F.q6,{items:e,className:"settings-payment-gateways__list",setItems:r,children:e.map((e=>{switch(e._type){case a.PaymentsProviderType.Suggestion:const r=e,m=t.includes(e.plugin.slug);return(0,h.jsx)(F.Uq,{id:r.id,children:K({suggestion:r,installingPlugin:n,setUpPlugin:s,pluginInstalled:m,acceptIncentive:o,shouldHighlightIncentive:c})},r.id);case a.PaymentsProviderType.Gateway:const _=e;return(0,h.jsx)(F.Uq,{id:e.id,children:Q({gateway:_,installingPlugin:n,acceptIncentive:o,shouldHighlightIncentive:c,setIsOnboardingModalOpen:l})},e.id);case a.PaymentsProviderType.OfflinePmsGroup:const u=e;return(0,h.jsx)(F.Uq,{id:u.id,children:(0,h.jsx)("div",{id:u.id,className:"transitions-disabled woocommerce-list__item clickable-list-item enter-done",onClick:()=>{d((0,x.Wg)(u.management._links.settings.href))},children:(0,h.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,h.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,h.jsx)(F.Gh,{}),(0,h.jsx)("img",{src:u.icon,alt:u.title+" logo"})]}),(0,h.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,h.jsx)("span",{className:"woocommerce-list__item-title",children:u.title}),(0,h.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:{__html:u.description}})]}),(0,h.jsx)("div",{className:"woocommerce-list__item-after centered no-buttons",children:(0,h.jsx)("div",{className:"woocommerce-list__item-after__actions",children:(0,h.jsx)("a",{className:"woocommerce-list__item-after__actions__arrow",href:u.management._links.settings.href,"aria-label":u.title,children:(0,h.jsx)(g.A,{icon:(0,i.isRTL)()?"chevron-left":"chevron-right"})})})})]})})},u.id);default:return null}}))})},J=({providers:e,installedPluginSlugs:t,installingPlugin:n,setUpPlugin:s,acceptIncentive:o,shouldHighlightIncentive:l,updateOrdering:m,isFetching:u,businessRegistrationCountry:g,setBusinessRegistrationCountry:y,setIsOnboardingModalOpen:w})=>{var b;const{invalidateResolution:f}=(0,c.useDispatch)(a.paymentSettingsStore),{invalidateResolution:j}=(0,c.useDispatch)(a.woopaymentsOnboardingStore),[k,N]=(0,r.useState)(!1),P=(0,r.useRef)(null),T=(window.wcSettings?.admin?.preloadSettings?.general?.woocommerce_default_country||"US").split(":")[0],A=(0,r.useMemo)((()=>Object.entries(window.wcSettings.countries||[]).map((([e,t])=>({key:e,name:(0,p.decodeEntities)(t),types:[]}))).sort(((e,t)=>e.name.localeCompare(t.name)))),[]),E=T!==g,M=(0,S.A)("settings-payment-gateways__header-select-container",{"has-alert":E}),D=e=>{const t=e.target.closest(".settings-payment-gateways__header-select-container--indicator");P.current&&t!==P.current||((0,x.TH)("business_location_indicator_click",{store_country:T,business_country:g||"unknown"}),N((e=>!e)))};return(0,h.jsxs)("div",{className:"settings-payment-gateways",children:[(0,h.jsxs)("div",{className:"settings-payment-gateways__header",children:[(0,h.jsx)("div",{className:"settings-payment-gateways__header-title",children:(0,i.__)("Payment providers","woocommerce")}),(0,h.jsxs)("div",{className:M,children:[(0,h.jsx)(B,{className:"woocommerce-select-control__country",label:(0,i.__)("Business location:","woocommerce"),placeholder:"",value:null!==(b=A.find((e=>e.key===g)))&&void 0!==b?b:{key:"US",name:"United States (US)"},options:A,onChange:e=>{d()({path:a.WC_ADMIN_NAMESPACE+"/settings/payments/country",method:"POST",data:{location:e}}).then((()=>{y(e),window.wcSettings.admin.woocommerce_payments_nox_profile&&(window.wcSettings.admin.woocommerce_payments_nox_profile.business_country_code=e),f("getPaymentProviders",[e]),j("getOnboardingData",[])}))}}),E&&(0,h.jsxs)("div",{className:"settings-payment-gateways__header-select-container--indicator",tabIndex:0,role:"button",ref:P,onClick:D,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||D(e)},children:[(0,h.jsx)("div",{className:"settings-payment-gateways__header-select-container--indicator-icon",children:(0,h.jsx)(C.A,{})}),k&&(0,h.jsx)(_.Popover,{className:"settings-payment-gateways__header-select-container--indicator-popover",placement:"top-end",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{N(!1)},children:(0,h.jsx)("div",{className:"components-popover__content-container",children:(0,h.jsx)("p",{children:(0,I.A)({mixedString:(0,i.__)("Your business location does not match your store location. {{link}}Edit store location.{{/link}}","woocommerce"),components:{link:(0,h.jsx)(v.Link,{href:(0,O.getAdminLink)("admin.php?page=wc-settings&tab=general"),target:"_blank",type:"external",onClick:()=>{(0,x.TH)("business_location_popover_edit_store_location_click",{store_country:T,business_country:g||"unknown"})}})}})})})})]})]})]}),u?(0,h.jsx)(R.i,{rows:5}):(0,h.jsx)(Y,{providers:e,installedPluginSlugs:t,installingPlugin:n,setUpPlugin:s,acceptIncentive:o,shouldHighlightIncentive:l,updateOrdering:m,setIsOnboardingModalOpen:w})]})},X=({incentive:e,provider:t,onboardingUrl:n,onDismiss:o,onAccept:a,setUpPlugin:c})=>{const[l,d]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!1),[g,p]=(0,r.useState)(!1),y="wc_settings_payments__banner";return(0,s.useEffect)((()=>{var n;(0,x.TH)("incentive_show",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(n=t._suggestion_id)&&void 0!==n?n:"unknown",display_context:y})}),[e,t]),l||(0,x.$8)(e,y)||m?null:(0,h.jsx)(_.Card,{className:"woocommerce-incentive-banner",isRounded:!0,children:(0,h.jsxs)("div",{className:"woocommerce-incentive-banner__content",children:[(0,h.jsx)("div",{className:"woocommerce-incentive-banner__image",children:(0,h.jsx)("img",{src:w.GZ+"images/settings-payments/incentives-illustration.svg",alt:(0,i.__)("Incentive illustration","woocommerce")})}),(0,h.jsxs)(_.CardBody,{className:"woocommerce-incentive-banner__body",children:[(0,h.jsx)(k,{status:"has_incentive",message:(0,i.__)("Limited time offer","woocommerce")}),(0,h.jsxs)("div",{className:"woocommerce-incentive-banner__copy",children:[(0,h.jsx)("h2",{children:e.title}),(0,h.jsx)("p",{children:e.description})]}),(0,h.jsx)("div",{className:"woocommerce-incentive-banner__terms",children:(0,r.createInterpolateElement)((0,i.__)("See <termsLink /> for details.","woocommerce"),{termsLink:(0,h.jsx)(v.Link,{href:e.tc_url,target:"_blank",rel:"noreferrer",type:"external",children:(0,i.__)("Terms and Conditions","woocommerce")})})}),(0,h.jsxs)("div",{className:"woocommerce-incentive-banner__actions",children:[(0,h.jsx)(_.Button,{variant:"primary",isBusy:l,disabled:l,onClick:()=>{var s,i;(0,x.TH)("incentive_accept",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(s=t._suggestion_id)&&void 0!==s?s:"unknown",display_context:y}),p(!0),a(e.promo_id),o(e._links.dismiss.href,y,!0),d(!0),c(t,n,"not_installed"===t.plugin.status&&null!==(i=t._links?.attach?.href)&&void 0!==i?i:null,"wc_settings_payments__incentive_banner"),p(!1)},children:e.cta_label}),(0,h.jsx)(_.Button,{variant:"tertiary",isBusy:g,disabled:g,onClick:()=>{p(!0),o(e._links.dismiss.href,y),p(!1),u(!0)},children:(0,i.__)("Dismiss","woocommerce")})]})]})]})})},ee=({incentive:e,provider:t,onboardingUrl:n,onAccept:o,onDismiss:a,setUpPlugin:c})=>{const[l,d]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!0),g="wc_settings_payments__modal",p=(0,x.$8)(e,g);(0,s.useEffect)((()=>{var n;(0,x.TH)("incentive_show",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(n=t._suggestion_id)&&void 0!==n?n:"unknown",display_context:g})}),[e,t]);const y=()=>{u(!1)};return p?null:(0,h.jsx)(h.Fragment,{children:m&&(0,h.jsx)(_.Modal,{title:"",className:"woocommerce-incentive-modal",onRequestClose:()=>{a(e._links.dismiss.href,g),y()},children:(0,h.jsx)(_.Card,{className:"woocommerce-incentive-modal__card",children:(0,h.jsxs)("div",{className:"woocommerce-incentive-modal__content",children:[(0,h.jsx)(_.CardMedia,{className:"woocommerce-incentive-modal__media",children:(0,h.jsx)("img",{src:w.GZ+"images/settings-payments/incentives-illustration.svg",alt:(0,i.__)("Incentive illustration","woocommerce")})}),(0,h.jsxs)(_.CardBody,{className:"woocommerce-incentive-modal__body",children:[(0,h.jsx)("div",{children:(0,h.jsx)(k,{status:"has_incentive",message:(0,i.__)("Limited time offer","woocommerce")})}),(0,h.jsx)("h2",{children:e.title}),(0,h.jsx)("p",{children:e.description}),(0,h.jsx)("p",{className:"woocommerce-incentive-modal__terms",children:(0,r.createInterpolateElement)((0,i.__)("See <termsLink /> for details.","woocommerce"),{termsLink:(0,h.jsx)(v.Link,{href:e.tc_url,target:"_blank",rel:"noreferrer",type:"external",children:(0,i.__)("Terms and Conditions","woocommerce")})})}),(0,h.jsx)("div",{className:"woocommerce-incentive-model__actions",children:(0,h.jsx)(_.Button,{variant:"primary",isBusy:l,disabled:l,onClick:()=>{var s,i;(0,x.TH)("incentive_accept",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(s=t._suggestion_id)&&void 0!==s?s:"unknown",display_context:g}),d(!0),o(e.promo_id),a(e._links.dismiss.href,g,!0),y(),c(t,n,"not_installed"===t.plugin.status&&null!==(i=t._links?.attach?.href)&&void 0!==i?i:null,"wc_settings_payments__incentive_modal"),d(!1)},children:e.cta_label})})]})]})})})})};var te=n(93832);function ne({setIsOpen:e,children:t}){return(0,h.jsx)(_.Modal,{className:"settings-payments-onboarding-modal",isFullScreen:!0,__experimentalHideHeader:!0,onRequestClose:()=>e(!1),shouldCloseOnClickOutside:!1,children:t})}var se=n(58016),oe=n(99096),ie=n(75854),ae=n(66087),ce=n(20195),re=n(10979),le=n.n(re);const de=(0,r.forwardRef)((function({className:e,children:t,spokenMessage:n=t,politeness:s="polite",actions:o=[],onRemove:a=ae.noop,icon:c=null,explicitDismiss:l=!1,onDismiss:d=null,__unstableHTML:m=!1},u){function g(e){e&&e.preventDefault&&e.preventDefault(),d(),a()}d=d||ae.noop,function(e,t){const n="string"==typeof e?e:(0,r.renderToString)(e);(0,r.useEffect)((()=>{n&&(0,ce.speak)(n,t)}),[n,t])}(n,s),(0,r.useEffect)((()=>{const e=setTimeout((()=>{l||(d(),a())}),1e4);return()=>clearTimeout(e)}),[l,d,a]);const p=(0,S.A)(e,"components-snackbar",{"components-snackbar-explicit-dismiss":!!l});o&&o.length>1&&(!0===globalThis.SCRIPT_DEBUG&&le()("Snackbar can only have 1 action, use Notice if your message require many messages"),o=[o[0]]);const y=(0,S.A)("components-snackbar__content",{"components-snackbar__content-with-icon":!!c});return!0===m&&(t=(0,h.jsx)(r.RawHTML,{children:t})),(0,h.jsx)("div",{ref:u,className:p,onClick:l?ae.noop:g,tabIndex:"0",role:l?"":"button",onKeyPress:l?ae.noop:g,"aria-label":l?"":(0,i.__)("Dismiss this notice","woocommerce"),children:(0,h.jsxs)("div",{className:y,children:[c&&(0,h.jsx)("div",{className:"components-snackbar__icon",children:c}),t,o.map((({label:e,onClick:t,url:n},s)=>(0,h.jsx)(_.Button,{href:n,isTertiary:!0,onClick:e=>function(e,t){e.stopPropagation(),a(),t&&t(e)}(e,t),className:"components-snackbar__action",children:e},s))),l&&(0,h.jsx)("span",{role:"button","aria-label":"Dismiss this notice",tabIndex:"0",className:"components-snackbar__dismiss-button",onClick:g,onKeyPress:g,children:"✕"})]})})})),me=({children:e,duration:t=4e3,className:n})=>{const[s,o]=(0,r.useState)(!1),[i,a]=(0,r.useState)(!1);(0,r.useEffect)((()=>{const e=setTimeout((()=>{o(!0);const e=setTimeout((()=>{a(!0)}),t);return()=>clearTimeout(e)}),100);return()=>{clearTimeout(e)}}),[]);const c=["woopayments_onboarding_modal_snackbar_wrapper",n,s?"is-visible":"",i?"is-exiting":""].filter(Boolean).join(" ");return(0,h.jsx)("div",{className:c,children:(0,h.jsx)(de,{className:n+"__snackbar",children:e})})},_e=()=>{const{snackbar:e}=(0,oe.w)();return e.show?(0,h.jsx)(me,{className:e.className||"",duration:e.duration,children:e.message}):null};function ue({isOpen:e,setIsOpen:t,providerData:n}){const s=(0,L.zy)(),a=(0,m.getHistory)(),r="/woopayments/onboarding",{createErrorNotice:l}=(0,c.dispatch)("core/notices"),d=(0,te.getQueryArg)(window.location.href,"wpcom_connection_return")||!1,_=n?.onboarding?.state?.wpcom_has_working_connection||!1,{sessionEntryPoint:u}=(0,oe.w)();o().useEffect((()=>{const n=(0,m.getQuery)(),s=n.path&&n.path.includes(r);if(!s||e||!_&&d||((0,x.W7)("woopayments_onboarding_modal_opened",{source:u}),t(!0)),e&&!s){const e=(0,m.getNewPath)({path:r},r,{page:"wc-settings",tab:"checkout"});a.push(e)}!_&&d&&((0,x.W7)("woopayments_onboarding_wpcom_connection_cancelled",{source:u}),l((0,i.__)("Setup was cancelled!","woocommerce"),{type:"snackbar",explicitDismiss:!1}))}),[s,e,t,d,_,l,a]);const g=()=>{(0,x.W7)("woopayments_onboarding_modal_closed",{source:u});const e=(0,m.getNewPath)({},"/wp-admin/admin.php",{page:"wc-settings",tab:"checkout"});a.push(e),t(!1)};return e?(0,h.jsx)(ne,{setIsOpen:g,children:(0,h.jsxs)(oe.X,{closeModal:g,onboardingSteps:ie.Ci,children:[(0,h.jsx)(se.A,{}),(0,h.jsx)(_e,{})]})}):null}const ge=()=>{var e,t;const[n,o]=(0,r.useState)(null),[l,g]=(0,r.useState)(null),{installAndActivatePlugins:p}=(0,c.useDispatch)(a.pluginsStore),{updateProviderOrdering:y,attachPaymentExtensionSuggestion:v}=(0,c.useDispatch)(a.paymentSettingsStore),[b,f]=(0,r.useState)(null),[j,k]=(0,r.useState)(!1),[N,S]=(0,r.useState)(window.wcSettings?.admin?.woocommerce_payments_nox_profile?.business_country_code||null),[O,C]=(0,r.useState)(!1),I=(0,w.Qk)("wcAdminAssetUrl");(0,r.useEffect)((()=>{(0,x.TH)("pageview");const e=new URLSearchParams(window.location.search);"true"===e.get("test_drive_error")&&f((0,i.sprintf)((0,i.__)("%s: An error occurred while setting up your sandbox account — please try again.","woocommerce"),"WooPayments")),"1"===e.get("wcpay-connect-jetpack-error")&&f((0,i.sprintf)((0,i.__)("%s: There was a problem connecting your WordPress.com account — please try again.","woocommerce"),"WooPayments")),"true"===e.get("wcpay-sandbox-success")&&k(!0)}),[]);const T=(0,c.useSelect)((e=>e(a.pluginsStore).getInstalledPlugins()),[]),{invalidateResolutionForStoreSelector:A}=(0,c.useDispatch)(a.paymentSettingsStore),{providers:E,offlinePaymentGateways:M,suggestions:D,suggestionCategories:H,isFetching:B}=(0,c.useSelect)((e=>{const t=e(a.paymentSettingsStore);return{providers:t.getPaymentProviders(N),offlinePaymentGateways:t.getOfflinePaymentGateways(N),suggestions:t.getSuggestions(N),suggestionCategories:t.getSuggestionCategories(N),isFetching:t.isFetching()}}),[N]),R=(0,s.useCallback)(((e,t,n=!1)=>{d()({url:e,method:"POST",data:{context:t,do_not_track:n}})}),[]),L=(0,s.useCallback)((e=>{d()({path:`/wc-analytics/admin/notes/experimental-activate-promo/${e}`,method:"POST"})}),[]);(0,r.useEffect)((()=>{g(null)}),[E]);const F=E.find((e=>"_incentive"in e)),U=F?F._incentive:null;let W=!1,G=!1,z=!1;if(F&&U)if((0,x.sq)(U))if((0,x.$8)(U,"wc_settings_payments__modal")){if(!(0,x.$8)(U,"wc_settings_payments__banner")){const e=new Date;e.setDate(e.getDate()-30),(0,x.uH)(U,"wc_settings_payments__modal",e.getTime())?G=!0:z=!0}}else W=!0;else(0,x.uU)(U)&&((0,x.$8)(U,"wc_settings_payments__banner")?z=!0:G=!0);const K=(0,s.useRef)(!1);(0,r.useEffect)((()=>{if(B||!E.length||!D.length||K.current)return;K.current=!0;const e={woocommerce_payments_displayed:E.some((e=>(0,x.j4)(e.id)))};D.forEach((t=>{e[t.id.replace(/-/g,"_")+"_displayed"]=!0})),E.filter((e=>"suggestion"===e._type)).forEach((t=>{t._suggestion_id?e[t._suggestion_id.replace(/-/g,"_")+"_displayed"]=!0:t.plugin&&t.plugin.slug&&(e[t.plugin.slug.replace(/-/g,"_")+"_displayed"]=!0)})),(0,x.TH)("recommendations_pageview",e)}),[D,E,B]);const q=(0,s.useCallback)(((e,t,s,i="wc_settings_payments__main")=>{var r;n||(e?.onboarding?._links?.preload?.href&&d()({url:e?.onboarding?._links?.preload.href,method:"POST",data:{location:N}}),!t&&(0,x.j4)(e.id)&&(t=(0,x.ge)()),o(e.id),(0,x.TH)("recommendations_setup",{extension_selected:e.plugin.slug,extension_action:"not_installed"===e.plugin.status?"install":"activate",provider_id:e.id,suggestion_id:null!==(r=e?._suggestion_id)&&void 0!==r?r:"unknown",provider_extension_slug:e.plugin.slug,from:i,source:i}),p([e.plugin.slug]).then((async n=>{var r;s&&v(s),u(n),A("getPaymentProviders"),"not_installed"===e.plugin.status&&(0,x.TH)("provider_installed",{provider_id:e.id,suggestion_id:null!==(r=e?._suggestion_id)&&void 0!==r?r:"unknown",provider_extension_slug:e.plugin.slug,from:i}),o(null);const l=(await(0,c.resolveSelect)(a.paymentSettingsStore).getPaymentProviders(N)).find((t=>t.id===e.id||t?._suggestion_id===e.id||t.plugin.slug===e.plugin.slug));if("native_in_context"===l?.onboarding?.type)(0,x.W7)("woopayments_onboarding_modal_opened",{from:i,source:Z.Fx}),C(!0);else{var d;if((null!==(d=l?.onboarding?.recommended_payment_methods)&&void 0!==d?d:[]).length>0)return void(0,m.getHistory)().push((0,m.getNewPath)({},"/payment-methods"));t&&(window.location.href=t)}})).catch((t=>{var n;let s="provider_extension_installation_failed";"not_installed"!==e.plugin.status&&(s="provider_extension_activation_failed"),(0,x.TH)(s,{provider_id:e.id,suggestion_id:null!==(n=e?._suggestion_id)&&void 0!==n?n:"unknown",provider_extension_slug:e.plugin.slug,from:i,source:Z.Fx,reason:"error"}),u(t),o(null)})))}),[n,p,A,N]),V=(0,h.jsxs)(_.Button,{variant:"link",target:"_blank",rel:"noopener noreferrer",href:"https://woocommerce.com/product-category/woocommerce-extensions/payment-gateways/?utm_source=payments_recommendations",className:"more-payment-options-link",onClick:()=>{const e=E.map((e=>e.plugin&&e.plugin.slug?e.plugin.slug.replace(/-/g,"_"):e._suggestion_id?e._suggestion_id.replace(/-/g,"_"):e.id));M.forEach((t=>{e.push(t.id)})),D.forEach((t=>{t.plugin&&t.plugin.slug?e.push(t.plugin.slug.replace(/-/g,"_")):e.push(t.id.replace(/-/g,"_"))}));const t=[...new Set(e)];(0,x.TH)("recommendations_other_options",{available_payment_methods:t.join(", ")})},children:[(0,h.jsx)("img",{src:I+"/icons/external-link.svg",alt:""}),(0,i.__)("More payment options","woocommerce")]});return(0,h.jsxs)(h.Fragment,{children:[W&&F&&U&&(0,h.jsx)(ee,{incentive:U,provider:F,onboardingUrl:null!==(e=F.onboarding?._links?.onboard?.href)&&void 0!==e?e:null,onDismiss:R,onAccept:L,setUpPlugin:q}),b&&(0,h.jsxs)("div",{className:"notice notice-error is-dismissible wcpay-settings-notice",children:[(0,h.jsx)("p",{children:b}),(0,h.jsx)("button",{type:"button",className:"notice-dismiss",onClick:()=>{f(null)}})]}),G&&F&&U&&(0,h.jsx)(X,{incentive:U,provider:F,onboardingUrl:null!==(t=F.onboarding?._links?.onboard?.href)&&void 0!==t?t:null,onDismiss:R,onAccept:L,setUpPlugin:q}),(0,h.jsxs)("div",{className:"settings-payments-main__container",children:[(0,h.jsx)(J,{providers:l||E,installedPluginSlugs:T,installingPlugin:n,setUpPlugin:q,acceptIncentive:L,shouldHighlightIncentive:z,updateOrdering:function(e){const t=e.map((e=>e._order)).sort(((e,t)=>e-t)),n={};e.forEach(((e,s)=>{n[e.id]=t[s]})),y(n),g(e)},isFetching:B,businessRegistrationCountry:N,setBusinessRegistrationCountry:S,setIsOnboardingModalOpen:C}),!B&&0===D.length&&(0,h.jsx)("div",{className:"more-payment-options",children:V}),(B||D.length>0)&&(0,h.jsx)(P,{suggestions:D,suggestionCategories:H,installingPlugin:n,setUpPlugin:q,isFetching:B,morePaymentOptionsLink:V})]}),((0,x.ZT)(E)||(0,x.Pt)(E))&&(0,h.jsx)(ue,{isOpen:O,setIsOpen:C,providerData:(0,x.RY)(E)||{}}),(0,h.jsx)($.lQ,{isOpen:j&&(0,x.Pt)(E),devMode:(0,x.MQ)(E),onClose:()=>k(!1)})]})},pe=ge},8181:(e,t,n)=>{t.A=function(e){var t=e.size,n=void 0===t?24:t,s=e.onClick,c=(e.icon,e.className),r=function(e,t){if(null==e)return{};var n,s,o=function(e,t){if(null==e)return{};var n,s,o={},i=Object.keys(e);for(s=0;s<i.length;s++)n=i[s],0<=t.indexOf(n)||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)n=i[s],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,i),l=["gridicon","gridicons-cross-small",c,!1,!1,!1].filter(Boolean).join(" ");return o.default.createElement("svg",a({className:l,height:n,width:n,onClick:s},r,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),o.default.createElement("g",null,o.default.createElement("path",{d:"M17.705 7.705l-1.41-1.41L12 10.59 7.705 6.295l-1.41 1.41L10.59 12l-4.295 4.295 1.41 1.41L12 13.41l4.295 4.295 1.41-1.41L13.41 12l4.295-4.295z"})))};var s,o=(s=n(51609))&&s.__esModule?s:{default:s},i=["size","onClick","icon","className"];function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},a.apply(this,arguments)}},46608:(e,t,n)=>{t.A=function(e){var t=e.size,n=void 0===t?24:t,s=e.onClick,c=(e.icon,e.className),r=function(e,t){if(null==e)return{};var n,s,o=function(e,t){if(null==e)return{};var n,s,o={},i=Object.keys(e);for(s=0;s<i.length;s++)n=i[s],0<=t.indexOf(n)||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)n=i[s],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,i),l=["gridicon","gridicons-info-outline",c,!!function(e){return 0==e%18}(n)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return o.default.createElement("svg",a({className:l,height:n,width:n,onClick:s},r,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),o.default.createElement("g",null,o.default.createElement("path",{d:"M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.411 0-8 3.589-8 8s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"})))};var s,o=(s=n(51609))&&s.__esModule?s:{default:s},i=["size","onClick","icon","className"];function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},a.apply(this,arguments)}}}]);