(()=>{var t={364:()=>{},8878:()=>{},7575:()=>{}},e={};function o(s){var i=e[s];if(void 0!==i)return i.exports;var r=e[s]={exports:{}};return t[s](r,r.exports,o),r.exports}(()=>{"use strict";o(364),o(8878);const t=window.wp.element,e=window.wp.compose,s=window.wp.blocks,i=window.wp.hooks,r=window.ReactJSXRuntime,c=(0,e.createHigherOrderComponent)((e=>{class o extends t.Component{mounted=!1;componentDidMount(){const{block:t,setAttributes:e}=this.props;t.name.startsWith("woocommerce/")&&e(this.getAttributesWithDefaults())}componentDidUpdate(){this.props.block.name.startsWith("woocommerce/")&&!this.mounted&&(this.mounted=!0)}getAttributesWithDefaults(){const t=(0,s.getBlockType)(this.props.block.name);let e=this.props.attributes;return!this.mounted&&this.props.block.name.startsWith("woocommerce/")&&void 0!==t.attributes&&void 0!==t.defaults&&(e=Object.assign({},this.props.attributes||{}),Object.keys(t.attributes).map((o=>(void 0===e[o]&&void 0!==t.defaults[o]&&(e[o]=t.defaults[o]),o)))),e}render(){return(0,r.jsx)(e,{...this.props,attributes:this.getAttributesWithDefaults()})}}return o}),"withDefaultAttributes");(0,i.addFilter)("editor.BlockListBlock","woocommerce-blocks/block-list-block",c),(0,i.addFilter)("blocks.getBlockAttributes","woocommerce-blocks/get-block-attributes",((t,e)=>(e.name.startsWith("woocommerce/")&&Object.keys(e.attributes).map((o=>(void 0===t[o]&&void 0!==e.defaults&&void 0!==e.defaults[o]&&(t[o]=e.defaults[o]),o))),t))),o(7575);const n=window.wp.coreData,a=window.wp.data,d=window.wp.i18n,u={name:"product",kind:"root",baseURL:"/wc/v3/products",label:(0,d.__)("Product","woocommerce"),plural:(0,d.__)("Products","woocommerce"),key:"id",supportsPagination:!0,getTitle:t=>t.name},p=[];(()=>{if(p.includes(u.name))return;const{addEntities:t}=(0,a.dispatch)(n.store);t([u]),p.push(u.name)})()})(),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["wc-blocks"]={}})();