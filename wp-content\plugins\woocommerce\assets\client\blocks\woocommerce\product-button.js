import*as t from"@woocommerce/stores/woocommerce/cart";import*as e from"@wordpress/interactivity";var o={1401:e=>{e.exports=t}},a={};function n(t){var e=a[t];if(void 0!==e)return e.exports;var r=a[t]={exports:{}};return o[t](r,r.exports,n),r.exports}n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);const r=(s={getContext:()=>e.getContext,store:()=>e.store,useLayoutEffect:()=>e.useLayoutEffect},u={},n.d(u,s),u),i="I acknowledge that using a private store means my plugin will inevitably break on the next store release.";var s,u,c=function(t){return t.IDLE="IDLE",t.SLIDE_OUT="SLIDE-OUT",t.SLIDE_IN="SLIDE-IN",t}(c||{});const{state:d}=(0,r.store)("woocommerce",{},{lock:i}),{state:m}=(0,r.store)("woocommerce/add-to-cart-with-options",{},{lock:i}),y={state:{get quantity(){const t=d.cart?.items.filter((t=>t.id===l.productId));if(0===t.length)return 0;if("variation"!==t[0]?.type)return t[0]?.quantity||0;const e=m?.selectedAttributes,o=t.find((t=>((t,e)=>!(!Array.isArray(t.variation)||!Array.isArray(e))&&t.variation.length===e.length&&t.variation.every((({raw_attribute:t,value:o})=>e.some((e=>e.attribute===t&&(e.value.toLowerCase()===o.toLowerCase()||e.value&&""===o))))))(t,e)));return o?.quantity||0},get slideInAnimation(){const{animationStatus:t}=(0,r.getContext)();return t===c.SLIDE_IN},get slideOutAnimation(){const{animationStatus:t}=(0,r.getContext)();return t===c.SLIDE_OUT},get addToCartText(){const{animationStatus:t,tempQuantity:e,addToCartText:o,productType:a,groupedProductIds:n,hasPressedButton:i,inTheCartText:s}=(0,r.getContext)(),u=t===c.IDLE||t===c.SLIDE_OUT?e||0:l.quantity;if("grouped"===a){const t=n?.map((t=>{const e=d.cart?.items.find((e=>e.id===t));return e?.quantity||0}));return t?.some((t=>t>0))&&i?s:o}return u>0?s.replace("###",u.toString()):o},get displayViewCart(){const{displayViewCart:t}=(0,r.getContext)();return!!t&&l.quantity>0},get productId(){return m?.variationId||(0,r.getContext)().productId}},actions:{*addCartItem(){const t=(0,r.getContext)();yield Promise.resolve().then(n.bind(n,1401));const{actions:e}=(0,r.store)("woocommerce",{},{lock:i});yield e.addCartItem({id:l.productId,quantity:l.quantity+t.quantityToAdd,type:t.productType}),t.displayViewCart=!0},*refreshCartItems(){yield Promise.resolve().then(n.bind(n,1401));const{actions:t}=(0,r.store)("woocommerce",{},{lock:i});t.refreshCartItems()},handleAnimationEnd(t){const e=(0,r.getContext)();"slideOut"===t.animationName?e.animationStatus=c.SLIDE_IN:"slideIn"===t.animationName&&(e.tempQuantity=l.quantity,e.animationStatus=c.IDLE)},handlePressedState(){const t=(0,r.getContext)();(void 0===m?.isFormValid||m?.isFormValid)&&(t.hasPressedButton=!0,t.animationStatus=c.SLIDE_OUT)}},callbacks:{syncTempQuantityOnLoad(){const t=(0,r.getContext)();(0,r.useLayoutEffect)((()=>{t.tempQuantity=l.quantity}),[])},startAnimation(){const t=(0,r.getContext)();t.tempQuantity!==l.quantity&&t.animationStatus===c.IDLE&&(t.animationStatus=c.SLIDE_OUT)}}},{state:l}=(0,r.store)("woocommerce/product-button",y,{lock:!0});