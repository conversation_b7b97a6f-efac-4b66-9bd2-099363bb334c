{"name": "woocommerce/product-filter-status", "title": "Status Filter", "description": "Let shoppers filter products by choosing stock status.", "category": "woocommerce", "keywords": ["WooCommerce"], "textdomain": "woocommerce", "apiVersion": 3, "ancestor": ["woocommerce/product-filters"], "supports": {"interactivity": true, "html": false, "color": {"text": true, "background": false, "__experimentalDefaultControls": {"text": false}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontWeight": true, "__experimentalFontFamily": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": false}}, "spacing": {"margin": true, "padding": true, "blockGap": true, "__experimentalDefaultControls": {"margin": false, "padding": false, "blockGap": false}}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": false, "radius": false, "style": false, "width": false}}}, "attributes": {"showCounts": {"type": "boolean", "default": false}, "displayStyle": {"type": "string", "default": "woocommerce/product-filter-checkbox-list"}, "isPreview": {"type": "boolean", "default": false}, "hideEmpty": {"type": "boolean", "default": true}}, "usesContext": ["query", "filterParams"], "example": {"attributes": {"isPreview": true}}}