/*! For license information please see command-palette-analytics.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,o,t)=>{var r=t(51609),n=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,w={key:!0,ref:!0,__self:!0,__source:!0};o.jsx=function(e,o,t){var r,c={},l=null,s=null;for(r in void 0!==t&&(l=""+t),void 0!==o.key&&(l=""+o.key),void 0!==o.ref&&(s=o.ref),o)i.call(o,r)&&!w.hasOwnProperty(r)&&(c[r]=o[r]);if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===c[r]&&(c[r]=o[r]);return{$$typeof:n,type:e,key:l,ref:s,props:c,_owner:a.current}}},39793:(e,o,t)=>{e.exports=t(94931)},51609:e=>{e.exports=window.React}},o={};const t=window.wp.i18n,r=window.wp.primitives;var n=function t(r){var n=o[r];if(void 0!==n)return n.exports;var i=o[r]={exports:{}};return e[r](i,i.exports,t),i.exports}(39793);const i=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{fillRule:"evenodd",d:"M11.25 5h1.5v15h-1.5V5zM6 10h1.5v10H6V10zm12 4h-1.5v6H18v-6z",clipRule:"evenodd"})}),a=window.wp.element,w=window.wp.plugins,c=window.wp.url,l=window.wp.data,s=window.wp.editor,d=window.wp.commands,p=window.wc.tracks,m=window.wp.htmlEntities;(0,w.registerPlugin)("woocommerce-analytics-commands-registration",{render:()=>{const{editedPostType:e}=(0,l.useSelect)((e=>({editedPostType:e(s.store).getCurrentPostType()}))),o=e?e+"-editor":null;return(0,a.useEffect)((()=>{window.hasOwnProperty("wcCommandPaletteAnalytics")&&window.wcCommandPaletteAnalytics.hasOwnProperty("reports")&&Array.isArray(window.wcCommandPaletteAnalytics.reports)&&window.wcCommandPaletteAnalytics.reports.forEach((e=>{(({label:e,path:o,origin:r})=>{(({name:e,label:o,icon:t,callback:r,origin:n})=>{(0,l.dispatch)(d.store).registerCommand({name:e,label:(0,m.decodeEntities)(o),icon:t,callback:(...o)=>{(0,p.queueRecordEvent)("woocommerce_command_palette_submit",{name:e,origin:n}),r(...o)}})})({name:`woocommerce${o}`,label:(0,t.sprintf)((0,t.__)("WooCommerce Analytics: %s","woocommerce"),e),icon:i,callback:()=>{document.location=(0,c.addQueryArgs)("admin.php",{page:"wc-admin",path:o})},origin:r})})({label:e.title,path:e.path,origin:o})}))}),[o]),null}}),(window.wc=window.wc||{}).commandPaletteAnalytics={}})();