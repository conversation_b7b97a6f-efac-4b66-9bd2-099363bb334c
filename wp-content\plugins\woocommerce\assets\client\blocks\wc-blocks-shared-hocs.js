(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var o in r)e.o(r,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:r[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{withFilteredAttributes:()=>f,withProductDataContext:()=>h});const r=window.wc.wcBlocksData,o=window.wp.data,n=window.wp.element,s=window.wp.isShallowEqual;var a=e.n(s);function c(e){const t=(0,n.useRef)(e);return a()(e,t.current)||(t.current=e),t.current}const u=window.wc.wcTypes,i=e=>{const t={namespace:"/wc/store/v1",resourceName:"products"},{results:s,isLoading:a}=(e=>{const{namespace:t,resourceName:s,resourceValues:a=[],query:i={},shouldSelect:d=!0}=e;if(!t||!s)throw new Error("The options object must have valid values for the namespace and the resource properties.");const l=(0,n.useRef)({results:[],isLoading:!0}),p=c(i),w=c(a),h=(()=>{const[,e]=(0,n.useState)();return(0,n.useCallback)((t=>{e((()=>{throw t}))}),[])})(),f=(0,o.useSelect)((e=>{if(!d)return null;const o=e(r.COLLECTIONS_STORE_KEY),n=[t,s,p,w],a=o.getCollectionError(...n);if(a){if(!(0,u.isError)(a))throw new Error("TypeError: `error` object is not an instance of Error constructor");h(a)}return{results:o.getCollection(...n),isLoading:!o.hasFinishedResolution("getCollection",n)}}),[t,s,w,p,d,h]);return null!==f&&(l.current=f),l.current})({...t,query:e}),{value:i}=((e,t)=>{const{namespace:n,resourceName:s,resourceValues:a=[],query:u={}}=t;if(!n||!s)throw new Error("The options object must have valid values for the namespace and the resource name properties.");const i=c(u),d=c(a),{value:l,isLoading:p=!0}=(0,o.useSelect)((t=>{const o=t(r.COLLECTIONS_STORE_KEY),a=[e,n,s,i,d];return{value:o.getCollectionHeader(...a),isLoading:o.hasFinishedResolution("getCollectionHeader",a)}}),[e,n,s,d,i]);return{value:l,isLoading:p}})("x-wp-total",{...t,query:e});return{products:s,totalProducts:parseInt(i,10),productsLoading:a}},d=window.wc.wcBlocksSharedContext,l=window.ReactJSXRuntime,p=(e,t)=>e.find((e=>e.id===t)),w=e=>{const{productId:t,OriginalComponent:r,postId:o,product:n}=e,s=e?.isDescendentOfQueryLoop?o:t,{products:a,productsLoading:c}=i({include:s}),u={product:s>0&&a.length>0?p(a,s):null,isLoading:c};return n?(0,l.jsx)(d.ProductDataContextProvider,{product:n,isLoading:!1,children:(0,l.jsx)(r,{...e})}):(0,l.jsx)(d.ProductDataContextProvider,{product:u.product,isLoading:u.isLoading,children:(0,l.jsx)(r,{...e})})},h=e=>t=>{const r=(0,d.useProductDataContext)({isAdmin:t.isAdmin,product:t.product});return!t.product&&r.hasContext||t.isAdmin?(0,l.jsx)(e,{...t}):(0,l.jsx)(w,{...t,OriginalComponent:e})},f=e=>t=>r=>{const o=((e,t)=>{const r=[];return Object.keys(e).forEach((o=>{if(void 0!==t[o])switch(e[o].type){case"boolean":r[o]="false"!==t[o]&&!1!==t[o];break;case"number":r[o]=parseInt(t[o],10);break;case"array":case"object":r[o]=JSON.parse(t[o]);break;default:r[o]=t[o]}else r[o]=e[o].default})),r})(e,r);return(0,l.jsx)(t,{...r,...o})};(this.wc=this.wc||{}).wcBlocksSharedHocs=t})();