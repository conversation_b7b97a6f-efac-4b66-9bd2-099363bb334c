import*as e from"@wordpress/interactivity";var t={d:(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const r=(o={getConfig:()=>e.getConfig,getContext:()=>e.getContext,getElement:()=>e.getElement,store:()=>e.store},i={},t.d(i,o),i),n={state:{get isStarHovered(){const{starValue:e}=(0,r.getContext)();return a.hoveredStar>=parseInt(e,10)},get isStarSelected(){const{starValue:e}=(0,r.getContext)();return a.selectedStar>=parseInt(e,10)},get hasRatingError(){return a.ratingError.length>0}},actions:{hoverStar(){const{starValue:e}=(0,r.getContext)();a.hoveredStar=parseInt(e,10)},leaveStar(){a.hoveredStar=0},selectStar(){const{starValue:e}=(0,r.getContext)();a.selectedStar=parseInt(e,10),a.ratingError=""},changeRatingWithKeyboard(e){const{ref:t}=(0,r.getElement)();if(!t||!t.parentNode)return;const{starValue:n}=(0,r.getContext)(),o=parseInt(n,10);let i=o,s=!1;if("ArrowLeft"===e.key&&o>1)i=o-1,s=!0;else if("ArrowRight"===e.key&&o<5)i=o+1,s=!0;else if("Home"===e.key)i=1,s=!0;else if("End"===e.key)i=5,s=!0;else if(" "===e.key||"Enter"===e.key)return e.preventDefault(),a.selectedStar=o,void(a.ratingError="");if(s){e.preventDefault(),a.selectedStar=i,a.ratingError="";const r=t.parentNode.querySelector(`button:nth-child(${i})`);r&&r.focus()}},handleSubmit(e){const t=(0,r.getConfig)("woocommerce/product-reviews");if(!t.reviewRatingEnabled)return;const n=new FormData(e.target).get("rating");if(t.reviewRatingRequired&&(!n||0===parseInt(n,10)))return e.preventDefault(),void(a.ratingError=t.i18nRequiredRatingText);a.ratingError=""}},callbacks:{showRatingStars(){const{ref:e}=(0,r.getElement)();e&&(e.hidden=!1)},hideRatingSelector(){const{ref:e}=(0,r.getElement)();e&&(e.hidden=!0,"required"in e&&(e.required=!1))}}},{state:a}=(0,r.store)("woocommerce/product-reviews",n,{lock:"I acknowledge that using a private store means my plugin will inevitably break on the next store release."});var o,i;