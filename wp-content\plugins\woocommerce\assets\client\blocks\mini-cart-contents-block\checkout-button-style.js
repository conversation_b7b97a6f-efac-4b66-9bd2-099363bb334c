"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[6476],{80371:(t,e,o)=>{o.d(e,{p:()=>c});var n=o(4921),s=o(73993),r=o(70219),l=o(70017);const c=t=>{const e=(t=>{const e=(0,s.isObject)(t)?t:{style:{}};let o=e.style;return(0,s.isString)(o)&&(o=JSON.parse(o)||{}),(0,s.isObject)(o)||(o={}),{...e,style:o}})(t),o=(0,l.BK)(e),c=(0,l.aR)(e),a=(0,l.fo)(e),i=(0,r.x)(e);return{className:(0,n.A)(i.className,o.className,c.className,a.className),style:{...i.style,...o.style,...c.style,...a.style}}}},70219:(t,e,o)=>{o.d(e,{x:()=>s});var n=o(73993);const s=t=>{const e=(0,n.isObject)(t.style.typography)?t.style.typography:{},o=(0,n.isString)(e.fontFamily)?e.fontFamily:"";return{className:t.fontFamily?`has-${t.fontFamily}-font-family`:o,style:{fontSize:t.fontSize?`var(--wp--preset--font-size--${t.fontSize})`:e.fontSize,fontStyle:e.fontStyle,fontWeight:e.fontWeight,letterSpacing:e.letterSpacing,lineHeight:e.lineHeight,textDecoration:e.textDecoration,textTransform:e.textTransform}}}},70017:(t,e,o)=>{o.d(e,{BK:()=>i,aR:()=>u,fo:()=>y});var n=o(4921),s=o(67356),r=o(49786),l=o(73993);function c(t={}){const e={};return(0,r.getCSSRules)(t,{selector:""}).forEach((t=>{e[t.key]=t.value})),e}function a(t,e){return t&&e?`has-${(0,s.c)(e)}-${t}`:""}function i(t){const{backgroundColor:e,textColor:o,gradient:s,style:r}=t,i=a("background-color",e),u=a("color",o),y=function(t){if(t)return`has-${t}-gradient-background`}(s),f=y||r?.color?.gradient;return{className:(0,n.A)(u,y,{[i]:!f&&!!i,"has-text-color":o||r?.color?.text,"has-background":e||r?.color?.background||s||r?.color?.gradient,"has-link-color":(0,l.isObject)(r?.elements?.link)?r?.elements?.link?.color:void 0}),style:c({color:r?.color||{}})}}function u(t){const e=t.style?.border||{};return{className:function(t){const{borderColor:e,style:o}=t,s=e?a("border-color",e):"";return(0,n.A)({"has-border-color":!!e||!!o?.border?.color,[s]:!!s})}(t),style:c({border:e})}}function y(t){return{className:void 0,style:c({spacing:t.style?.spacing||{}})}}},82752:(t,e,o)=>{o.r(e),o.d(e,{default:()=>f});var n=o(78331),s=o(89874),r=o(4921),l=o(80371),c=o(30743),a=o(73993),i=o(5116),u=o(22805),y=o(10790);const f=({className:t,checkoutButtonLabel:e,style:o})=>{const f=(0,l.p)({style:o}),{dispatchOnProceedToCheckout:d}=(0,c.e)();return n.tn?(0,y.jsx)(s.A,{className:(0,r.A)(t,f.className,"wc-block-mini-cart__footer-checkout"),variant:(0,u.I)(t,"contained"),style:f.style,href:n.tn,onClick:t=>{d().then((e=>{e.some(a.isErrorResponse)&&t.preventDefault()}))},children:e||i.c}):null}}}]);