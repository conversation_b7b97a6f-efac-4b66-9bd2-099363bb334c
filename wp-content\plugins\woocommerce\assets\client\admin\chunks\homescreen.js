"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[1494],{37148:(e,t,o)=>{o.d(t,{a:()=>c});var s=o(4921),n=o(86087),r=o(23772);const c=({children:e,className:t})=>(0,n.createElement)("div",{className:(0,s.A)("woocommerce-onboarding-loader",t)},e);c.Layout=({children:e,className:t})=>(0,n.createElement)("div",{className:(0,s.A)("woocommerce-onboarding-loader-wrapper",t)},(0,n.createElement)("div",{className:(0,s.A)("woocommerce-onboarding-loader-container",t)},e)),c.Illustration=({children:e})=>(0,n.createElement)(n.Fragment,null,e),c.Title=({children:e,className:t})=>(0,n.createElement)("h1",{className:(0,s.A)("woocommerce-onboarding-loader__title",t)},e),c.ProgressBar=({progress:e,className:t})=>(0,n.createElement)(r.A,{className:(0,s.A)("progress-bar",t),percent:null!=e?e:0,color:"var(--wp-admin-theme-color)",bgcolor:"#E0E0E0"}),c.Subtext=({children:e,className:t})=>(0,n.createElement)("p",{className:(0,s.A)("woocommerce-onboarding-loader__paragraph",t)},e),c.Sequence=({interval:e,shouldLoop:t=!0,children:o,onChange:s=()=>{}})=>{const[r,c]=(0,n.useState)(0),i=n.Children.count(o);(0,n.useEffect)((()=>{const o=setInterval((()=>{c((e=>{const n=e+1;if(t){const e=n%i;return s(e),e}return n<i?(s(n),n):(clearInterval(o),e)}))}),e);return()=>clearInterval(o)}),[e,o,t,i]);const a=n.Children.toArray(o)[r];return(0,n.createElement)(n.Fragment,null,a)}},14695:(e,t,o)=>{o.d(t,{L:()=>A});var s=o(51609),n=o(86087),r=o(56427),c=o(18015),i=o(12486),a=o(27193),l=o(6457),m=o(94084),d=o(90423),u=o(31183),p=o(76147),_=o(22056),h=o(96043),w=o(70145),g=o(61727),k=o(97021),y=o(27707),v=o(30487),x=o(97723),f=o(80019),j=o(66012),b=o(17479),C=o(93342),S=o(10630);const N=[{name:"visa",component:(0,n.createElement)(c.A,{key:"visa"})},{name:"mastercard",component:(0,n.createElement)(i.A,{key:"mastercard"})},{name:"amex",component:(0,n.createElement)(a.A,{key:"amex"})},{name:"discover",component:(0,n.createElement)(l.A,{key:"discover"})},{name:"woopay",component:(0,n.createElement)(p.A,{key:"woopay"})},{name:"applepay",component:(0,n.createElement)(m.A,{key:"applepay"})},{name:"googlepay",component:(0,n.createElement)(d.A,{key:"googlepay"})},{name:"afterpay",component:(0,n.createElement)(_.A,{key:"afterpay"})},{name:"affirm",component:(0,n.createElement)(h.A,{key:"affirm"})},{name:"klarna",component:(0,n.createElement)(w.A,{key:"klarna"})},{name:"cartebancaire",component:(0,n.createElement)(g.A,{key:"cartebancaire"})},{name:"unionpay",component:(0,n.createElement)(k.A,{key:"unionpay"})},{name:"diners",component:(0,n.createElement)(y.A,{key:"diners"})},{name:"eftpos",component:(0,n.createElement)(v.A,{key:"eftpos"})},{name:"jcb",component:(0,n.createElement)(u.A,{key:"jcb"})},{name:"bancontact",component:(0,n.createElement)(f.A,{key:"bancontact"})},{name:"becs",component:(0,n.createElement)(b.A,{key:"becs"})},{name:"eps",component:(0,n.createElement)(j.A,{key:"eps"})},{name:"ideal",component:(0,n.createElement)(x.A,{key:"ideal"})},{name:"przelewy24",component:(0,n.createElement)(C.A,{key:"przelewy24"})},{name:"grabpay",component:(0,n.createElement)(S.A,{key:"grabpay"})}],A=({isWooPayEligible:e=!1,maxElements:t=10,tabletWidthBreakpoint:o=768,maxElementsTablet:c=7,mobileWidthBreakpoint:i=480,maxElementsMobile:a=5,totalPaymentMethods:l=21})=>{const[m,d]=(0,s.useState)(t),[u,p]=(0,s.useState)(!1),_=(0,s.useRef)(null),h=e=>{const t=e.target.closest(".woocommerce-woopayments-payment-methods-logos-count");_.current&&t!==_.current||p((e=>!e))},w=e?l:l-1,g=t=>e?t:t+1;(0,s.useEffect)((()=>{const e=()=>{window.innerWidth<=i?d(a):window.innerWidth<=o?d(c):d(t)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[t,a,c,o,i]);const k=N.slice(0,g(m)).filter((t=>e||"woopay"!==t.name)),y=N.slice(g(m)).filter((t=>e||"woopay"!==t.name));return(0,n.createElement)("div",{className:"woocommerce-woopayments-payment-methods-logos"},k.map((e=>e.component)),m<w&&(0,n.createElement)("div",{className:"woocommerce-woopayments-payment-methods-logos-count",role:"button",tabIndex:0,ref:_,onClick:h,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||h(e)}},"+ ",w-m,u&&(0,n.createElement)(r.Popover,{className:"woocommerce-woopayments-payment-methods-logos-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{p(!1)}},(0,n.createElement)("div",{className:"woocommerce-woopayments-payment-methods-logos"},y.map((e=>e.component))))))}},29568:(e,t,o)=>{o.d(t,{b:()=>i,J:()=>a});var s=o(84343),n=o.n(s),r=o(27723);const c={PH:{"National Capital Region":(0,r.__)("Metro Manila","woocommerce")},IT:{Rome:(0,r.__)("Roma","woocommerce")}},i=(e,t,o=.7)=>{if(!t)return null;let s=null,r=o;const i=(({country_short:e,region:t="",city:o=""})=>{if(!e)return null;const s=c[e];if(!s)return null;const n=s[t];return n||(s[o]||null)})(t);for(const o of e){if(o.key===t.country_short)return o;if(o.key.split(":")[0]===t.country_short&&o.label.includes("—")){const e=o.label.split("—")[1].trim();if(i===e)return o;if(0===e.localeCompare(t.region||"","en",{sensitivity:"base"})||0===e.localeCompare(t.city||"","en",{sensitivity:"base"}))return o;const c=Math.max(n().compareTwoStrings(e,t.region||""),n().compareTwoStrings(e,t.city||""));c>=r&&(s=o,r=c)}}return s},a=e=>{var t;return null!==(t=e?.split(":")[0])&&void 0!==t?t:void 0}},99010:(e,t,o)=>{o.d(t,{O:()=>_,p:()=>p});var s=o(4921),n=o(86087),r=o(94736),c=o(76154),i=o.n(c),a=o(98846),l=o(56427),m=o(39793),d=o(66087);class u extends n.Component{render(){const{className:e,hasAction:t,hasDate:o,hasSubtitle:n,lines:r}=this.props,c=(0,s.A)("woocommerce-activity-card is-loading",e);return(0,m.jsxs)("div",{className:c,"aria-hidden":!0,children:[(0,m.jsx)("span",{className:"woocommerce-activity-card__icon",children:(0,m.jsx)("span",{className:"is-placeholder"})}),(0,m.jsxs)("div",{className:"woocommerce-activity-card__header",children:[(0,m.jsx)("div",{className:"woocommerce-activity-card__title is-placeholder"}),n&&(0,m.jsx)("div",{className:"woocommerce-activity-card__subtitle is-placeholder"}),o&&(0,m.jsx)("div",{className:"woocommerce-activity-card__date",children:(0,m.jsx)("span",{className:"is-placeholder"})})]}),(0,m.jsx)("div",{className:"woocommerce-activity-card__body",children:(0,d.range)(r).map((e=>(0,m.jsx)("span",{className:"is-placeholder"},e)))}),t&&(0,m.jsx)("div",{className:"woocommerce-activity-card__actions",children:(0,m.jsx)("span",{className:"is-placeholder"})})]})}}u.defaultProps={hasAction:!1,hasDate:!1,hasSubtitle:!1,lines:1};const p=u;class _ extends n.Component{getCard(){const{actions:e,className:t,children:o,date:r,icon:c,subtitle:l,title:d,unread:u}=this.props,p=(0,s.A)("woocommerce-activity-card",t),_=Array.isArray(e)?e:[e],h=/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(r)?i().utc(r).fromNow():r;return(0,m.jsxs)("section",{className:p,children:[u&&(0,m.jsx)("span",{className:"woocommerce-activity-card__unread"}),c&&(0,m.jsx)("span",{className:"woocommerce-activity-card__icon","aria-hidden":!0,children:c}),d&&(0,m.jsxs)("header",{className:"woocommerce-activity-card__header",children:[(0,m.jsx)(a.H,{className:"woocommerce-activity-card__title",children:d}),l&&(0,m.jsx)("div",{className:"woocommerce-activity-card__subtitle",children:l}),h&&(0,m.jsx)("span",{className:"woocommerce-activity-card__date",children:h})]}),o&&(0,m.jsx)(a.Section,{className:"woocommerce-activity-card__body",children:o}),e&&(0,m.jsx)("footer",{className:"woocommerce-activity-card__actions",children:_.map(((e,t)=>(0,n.cloneElement)(e,{key:t})))})]})}render(){const{onClick:e}=this.props;return e?(0,m.jsx)(l.Button,{className:"woocommerce-activity-card__button",onClick:e,children:this.getCard()}):this.getCard()}}_.defaultProps={icon:(0,m.jsx)(r.A,{size:48}),unread:!1}},75655:(e,t,o)=>{o.d(t,{A:()=>a});var s=o(4921),n=o(86087),r=o(14908),c=(o(98846),o(39793));class i extends n.Component{render(){const{className:e,menu:t,subtitle:o,title:n,unreadMessages:i}=this.props,a=(0,s.A)({"woocommerce-layout__inbox-panel-header":o,"woocommerce-layout__activity-panel-header":!o},e),l=i||0;return(0,c.jsxs)("div",{className:a,children:[(0,c.jsxs)("div",{className:"woocommerce-layout__inbox-title",children:[(0,c.jsx)(r.Text,{size:16,weight:600,color:"#23282d",children:n}),(0,c.jsx)(r.Text,{variant:"button",weight:"600",size:"14",lineHeight:"20px",children:l>0&&(0,c.jsx)("span",{className:"woocommerce-layout__inbox-badge",children:i})})]}),(0,c.jsx)("div",{className:"woocommerce-layout__inbox-subtitle",children:o&&(0,c.jsx)(r.Text,{variant:"body.small",size:"14",lineHeight:"20px",children:o})}),t&&(0,c.jsx)("div",{className:"woocommerce-layout__activity-panel-header-menu",children:t})]})}}const a=i},85810:(e,t,o)=>{o.r(t),o.d(t,{HelpPanel:()=>x,SETUP_TASK_HELP_ITEMS_FILTER:()=>y,default:()=>f});var s=o(27723),n=o(14908),r=o(47143),c=o(86087),i=o(52619),a=o(24148),l=o(99669),m=o(45260),d=o(66087),u=o(98846),p=o(40314),_=o(86958),h=o(83306),w=o(75655),g=o(18886),k=o(39793);const y="woocommerce_admin_setup_task_help_items";function v(e,t){const{taskName:o}=e;t&&e.recordEvent("help_panel_click",{task_name:o||"homescreen",link:t.currentTarget.href})}const x=({taskName:e,recordEvent:t=h.recordEvent,...o})=>{(0,c.useEffect)((()=>{t("help_panel_open",{task_name:e||"homescreen"})}),[e,t]);const r=function(e){const t=function(e){const{taskName:t}=e;switch(t){case"products":return[{title:(0,s.__)("Adding and Managing Products","woocommerce"),link:"https://woocommerce.com/document/managing-products/?utm_source=help_panel&utm_medium=product"},{title:(0,s.__)("Import products using the CSV Importer and Exporter","woocommerce"),link:"https://woocommerce.com/document/product-csv-importer-exporter/?utm_source=help_panel&utm_medium=product"},{title:(0,s.__)("Migrate products using Cart2Cart","woocommerce"),link:"https://woocommerce.com/products/cart2cart/?utm_source=help_panel&utm_medium=product"},{title:(0,s.__)("Learn more about setting up products","woocommerce"),link:"https://woocommerce.com/documentation/plugins/woocommerce/getting-started/setup-products/?utm_source=help_panel&utm_medium=product"}];case"appearance":return[{title:(0,s.__)("Showcase your products and tailor your shopping experience using Blocks","woocommerce"),link:"https://woocommerce.com/document/woocommerce-blocks/?utm_source=help_panel&utm_medium=product"},{title:(0,s.__)("Manage Store Notice, Catalog View and Product Images","woocommerce"),link:"https://woocommerce.com/document/woocommerce-customizer/?utm_source=help_panel&utm_medium=product"},{title:(0,s.__)("How to choose and change a theme","woocommerce"),link:"https://woocommerce.com/document/choose-change-theme/?utm_source=help_panel&utm_medium=product"}];case"shipping":return function({activePlugins:e,countryCode:t}){const o="US"===t&&!e.includes("woocommerce-services")&&!e.includes("woocommerce-shipping")&&!e.includes("woocommerce-tax");return[{title:(0,s.__)("Setting up Shipping Zones","woocommerce"),link:"https://woocommerce.com/document/setting-up-shipping-zones/?utm_source=help_panel&utm_medium=product"},{title:(0,s.__)("Core Shipping Options","woocommerce"),link:"https://woocommerce.com/documentation/plugins/woocommerce/getting-started/shipping/core-shipping-options/?utm_source=help_panel&utm_medium=product"},{title:(0,s.__)("Product Shipping Classes","woocommerce"),link:"https://woocommerce.com/document/product-shipping-classes/?utm_source=help_panel&utm_medium=product"},o&&{title:(0,s.__)("WooCommerce Shipping setup and configuration","woocommerce"),link:"https://woocommerce.com/document/woocommerce-shipping-and-tax/?utm_source=help_panel&utm_medium=product#section-3"},{title:(0,s.__)("Learn more about configuring your shipping settings","woocommerce"),link:"https://woocommerce.com/document/plugins/woocommerce/getting-started/shipping/?utm_source=help_panel&utm_medium=product"}].filter(Boolean)}(e);case"tax":return function(e){const{countryCode:t,taskLists:o}=e,n=o.reduce(((e,t)=>[...e,...t.tasks]),[]).find((e=>"tax"===e.id));if(!n)return;const{additionalData:r}=n,{woocommerceTaxCountries:c=[],taxJarActivated:i,woocommerceTaxActivated:a,woocommerceShippingActivated:l}=r,m=!i&&c.includes(t)&&!a&&!l;return[{title:(0,s.__)("Setting up Taxes in WooCommerce","woocommerce"),link:"https://woocommerce.com/document/setting-up-taxes-in-woocommerce/?utm_source=help_panel&utm_medium=product"},m&&{title:(0,s.__)("Automated Tax calculation using WooCommerce Tax","woocommerce"),link:"https://woocommerce.com/document/woocommerce-services/?utm_source=help_panel&utm_medium=product#section-10"}].filter(Boolean)}(e);case"payments":return function(e){const{paymentGatewaySuggestions:t}=e;return[{title:(0,s.__)("Which Payment Option is Right for Me?","woocommerce"),link:"https://woocommerce.com/document/premium-payment-gateway-extensions/?utm_source=help_panel&utm_medium=product"},t.woocommerce_payments&&{title:(0,s.__)("WooPayments Start Up Guide","woocommerce"),link:"https://woocommerce.com/document/payments/?utm_source=help_panel&utm_medium=product"},t.woocommerce_payments&&{title:(0,s.__)("WooPayments FAQs","woocommerce"),link:"https://woocommerce.com/documentation/woocommerce-payments/woocommerce-payments-faqs/?utm_source=help_panel&utm_medium=product"},t.stripe&&{title:(0,s.__)("Stripe Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/stripe/?utm_source=help_panel&utm_medium=product"},t["ppcp-gateway"]&&{title:(0,s.__)("PayPal Checkout Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/2-0/woocommerce-paypal-payments/?utm_medium=product#section-3"},t.square_credit_card&&{title:(0,s.__)("Square - Get started","woocommerce"),link:"https://woocommerce.com/document/woocommerce-square/?utm_source=help_panel&utm_medium=product"},t.kco&&{title:(0,s.__)("Klarna - Introduction","woocommerce"),link:"https://woocommerce.com/document/klarna-checkout/?utm_source=help_panel&utm_medium=product"},t.klarna_payments&&{title:(0,s.__)("Klarna - Introduction","woocommerce"),link:"https://woocommerce.com/document/klarna-payments/?utm_source=help_panel&utm_medium=product"},t.payfast&&{title:(0,s.__)("Payfast Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/payfast-payment-gateway/?utm_source=help_panel&utm_medium=product"},t.eway&&{title:(0,s.__)("Eway Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/eway/?utm_source=help_panel&utm_medium=product"},{title:(0,s.__)("Direct Bank Transfer (BACS)","woocommerce"),link:"https://woocommerce.com/document/bacs/?utm_source=help_panel&utm_medium=product"},{title:(0,s.__)("Cash on Delivery","woocommerce"),link:"https://woocommerce.com/document/cash-on-delivery/?utm_source=help_panel&utm_medium=product"}].filter(Boolean)}(e);case"marketing":return function(e){const{activePlugins:t}=e;return[t.includes("mailpoet")&&{title:(0,s.__)("Get started with Mailpoet","woocommerce"),link:"https://kb.mailpoet.com/category/114-getting-started"},t.includes("google-listings-and-ads")&&{title:(0,s.__)("Set up Google for WooCommerce","woocommerce"),link:"https://woocommerce.com/document/google-listings-and-ads/?utm_medium=product#get-started"},t.includes("pinterest-for-woocommerce")&&{title:(0,s.__)("Set up Pinterest for WooCommerce","woocommerce"),link:"https://woocommerce.com/products/pinterest-for-woocommerce/"},t.includes("mailchimp-for-woocommerce")&&{title:(0,s.__)("Connect Mailchimp for WooCommerce","woocommerce"),link:"https://mailchimp.com/help/connect-or-disconnect-mailchimp-for-woocommerce/"},t.includes("creative-mail-by-constant-contact")&&{title:(0,s.__)("Set up Creative Mail for WooCommerce","woocommerce"),link:"https://app.creativemail.com/kb/help/WooCommerce"}].filter(Boolean)}(e);default:return[{title:(0,s.__)("Get Support","woocommerce"),link:"https://woocommerce.com/my-account/create-a-ticket/?utm_medium=product"},{title:(0,s.__)("Home Screen","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product"},{title:(0,s.__)("Inbox","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product#section-4"},{title:(0,s.__)("Stats Overview","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product#section-5"},{title:(0,s.__)("Store Management","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product#section-10"},{title:(0,s.__)("Store Setup Checklist","woocommerce"),link:"https://woocommerce.com/document/woocommerce-setup-wizard?utm_medium=product#store-setup-checklist"}]}}(e),o={title:(0,s.__)("WooCommerce Docs","woocommerce"),link:"https://woocommerce.com/documentation/?utm_source=help_panel&utm_medium=product"};t.push(o);const r=(0,i.applyFilters)(y,t,e.taskName,e);let c=Array.isArray(r)?r.filter((e=>e instanceof Object&&e.title&&e.link)):[];c.length||(c=[o]);const u=(0,d.partial)(v,e);return c.map((e=>{var t,o;return{title:(0,k.jsx)(n.Text,{as:"div",variant:"button",weight:"600",size:"14",lineHeight:"20px",children:e.title}),before:(0,k.jsx)(a.A,{icon:l.A}),after:(0,k.jsx)(a.A,{icon:m.A}),linkType:null!==(t=e.linkType)&&void 0!==t?t:"external",target:null!==(o=e.target)&&void 0!==o?o:"_blank",href:e.link,onClick:u}}))}({taskName:e,recordEvent:t,...o});return(0,k.jsxs)(c.Fragment,{children:[(0,k.jsx)(w.A,{title:(0,s.__)("Documentation","woocommerce")}),(0,k.jsx)(u.Section,{children:(0,k.jsx)(u.List,{items:r,className:"woocommerce-quick-links__list"})})]})},f=(0,_.Zz)((0,r.withSelect)((e=>{const{getSettings:t}=e(p.settingsStore),{getActivePlugins:o}=e(p.pluginsStore),{general:s={}}=t("general"),n=o(),r=e(p.onboardingStore).getPaymentGatewaySuggestions().reduce(((e,t)=>{const{id:o}=t;return e[o]=!0,e}),{}),c=e(p.onboardingStore).getTaskLists();return{activePlugins:n,countryCode:(0,g.gI)(s.woocommerce_default_country),paymentGatewaySuggestions:r,taskLists:c}})))(x)},59505:(e,t,o)=>{o.d(t,{m:()=>u,w:()=>d});var s=o(76154),n=o.n(s),r=o(66087),c=o(77374),i=o(40314),a=o(96476),l=o(43577),m=o(15703);const d=({indicator:e,primaryData:t,secondaryData:o,currency:s,formatAmount:n,persistedQuery:c})=>{const i=(0,r.find)(t.data,(t=>t.stat===e.stat)),d=(0,r.find)(o.data,(t=>t.stat===e.stat));if(!i||!d)return{};const u=i._links&&i._links.report[0]&&i._links.report[0].href||"",p=function(e,t,o){return e?"/jetpack"===e?(0,m.getAdminLink)("admin.php?page=jetpack#/dashboard"):(0,a.getNewPath)(t,e,{chart:o.chart}):""}(u,c,i),_="/jetpack"===u?"wp-admin":"wc-admin",h="currency"===i.format,w=(0,l.calculateDelta)(i.value,d.value);return{primaryValue:h?n(i.value):(0,l.formatValue)(s,i.format,i.value),secondaryValue:h?n(d.value):(0,l.formatValue)(s,d.format,d.value),delta:w,reportUrl:p,reportUrlType:_}},u=(e,t,o,s)=>{const{getReportItems:r,getReportItemsError:a,isResolving:l}=e(i.reportsStore),{woocommerce_default_date_range:m}=e(i.settingsStore).getSetting("wc_admin","wcAdminSettings"),d=(0,c.getCurrentDates)(o,m),u=d.primary.before,p=d.secondary.before,_=t.map((e=>e.stat)).join(","),h=(0,i.getFilterQuery)({filters:s,query:o}),w={...h,after:(0,c.appendTimestamp)(d.primary.after,"start"),before:(0,c.appendTimestamp)(u,u.isSame(n()(),"day")?"now":"end"),stats:_},g={...h,after:(0,c.appendTimestamp)(d.secondary.after,"start"),before:(0,c.appendTimestamp)(p,p.isSame(n()(),"day")?"now":"end"),stats:_};return{primaryData:r("performance-indicators",w),primaryError:a("performance-indicators",w)||null,primaryRequesting:l("getReportItems",["performance-indicators",w]),secondaryData:r("performance-indicators",g),secondaryError:a("performance-indicators",g)||null,secondaryRequesting:l("getReportItems",["performance-indicators",g]),defaultDateRange:m}}},71503:(e,t,o)=>{o.r(t),o.d(t,{default:()=>nt});var s=o(29491),n=o(47143),r=o(86087),c=o(40314),i=o(96476),a=o(4921),l=o(27723),m=o(75655),d=o(79492),u=o(98846),p=o(56427),_=o(83306),h=o(66087),w=o(29332),g=o(18537),k=o(36849),y=o(15703),v=o(94111),x=o(99010),f=o(56109),j=o(39793);function b(e){(0,_.recordEvent)(`activity_panel_orders_${e}`,{})}function C(e,t,o){if(0===e.length)return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)(x.O,{className:"woocommerce-empty-activity-card",title:"",icon:"",children:[(0,j.jsx)("span",{className:"woocommerce-order-empty__success-icon",role:"img","aria-labelledby":"woocommerce-order-empty-message",children:"🎉"}),(0,j.jsx)(u.H,{id:"woocommerce-order-empty-message",children:(0,l.__)("You’ve fulfilled all your orders","woocommerce")})]}),(0,j.jsx)(u.Link,{href:"edit.php?post_type=shop_order",onClick:()=>b("orders_manage"),className:"woocommerce-layout__activity-panel-outbound-link woocommerce-layout__activity-panel-empty",type:"wp-admin",children:(0,l.__)("Manage all orders","woocommerce")})]});const s=e=>{const{name:t}=e||{};return t?`{{customerLink}}${t}{{/customerLink}}`:""},n=e=>{const{id:o,number:n,customer_id:r}=e,c=t.find((e=>e.user_id===r))||{};let a=null;return c&&c.id&&(a=window.wcAdminFeatures.analytics?(0,i.getNewPath)({},"/analytics/customers",{filter:"single_customer",customers:c.id}):(0,y.getAdminLink)("user-edit.php?user_id="+c.id)),(0,j.jsx)(j.Fragment,{children:(0,k.A)({mixedString:(0,l.sprintf)((0,l.__)("{{orderLink}}Order #%(orderNumber)s{{/orderLink}} %(customerString)s","woocommerce"),{orderNumber:n,customerString:s(c)}),components:{orderLink:(0,j.jsx)(u.Link,{href:(0,y.getAdminLink)("post.php?action=edit&post="+o),onClick:()=>b("order_number"),type:"wp-admin"}),destinationFlag:c&&c.country?(0,j.jsx)(u.Flag,{code:c&&c.country,round:!1}):null,customerLink:a?(0,j.jsx)(u.Link,{href:a,onClick:()=>b("customer_name"),type:"wc-admin"}):(0,j.jsx)("span",{})}})})},r=[];return e.forEach((e=>{const{date_created_gmt:t,line_items:s,id:c}=e,i=s?s.length:0;r.push((0,j.jsx)(x.O,{className:"woocommerce-order-activity-card",title:n(e),date:t,onClick:({target:e})=>{b("orders_begin_fulfillment"),e.href||(window.location.href=(0,y.getAdminLink)(`post.php?action=edit&post=${c}`))},subtitle:(0,j.jsxs)("div",{children:[(0,j.jsx)("span",{children:(0,l.sprintf)((0,l._n)("%d product","%d products",i,"woocommerce"),i)}),(0,j.jsx)("span",{children:o(e.total,e.currency)})]}),children:(0,j.jsx)(u.OrderStatus,{order:e,orderStatusMap:(0,f.Qk)("orderStatuses",{})})},c))})),(0,j.jsxs)(j.Fragment,{children:[r,(0,j.jsx)(u.Link,{href:"edit.php?post_type=shop_order",className:"woocommerce-layout__activity-panel-outbound-link",onClick:()=>b("orders_manage"),type:"wp-admin",children:(0,l.__)("Manage all orders","woocommerce")})]})}const S=function({unreadOrdersCount:e,orderStatuses:t}){const o=(0,r.useMemo)((()=>({page:1,per_page:5,status:t,_fields:["id","number","currency","status","total","customer","line_items","customer_id","date_created_gmt"]})),[t]),s=(0,r.useContext)(v.CurrencyContext),i=s.getCurrencyConfig(),{currencySymbols:a={}}=(0,f.Qk)("onboarding",{}),{orders:m=[],isRequesting:d,isError:p,customerItems:_}=(0,n.useSelect)((s=>{const{getOrders:n,hasFinishedResolution:r,getOrdersError:i}=s(c.ordersStore);if(!t.length&&0===e)return{isRequesting:!1};const a=n(o,null),l=r("getOrders",[o]);if(l||null===e||null===a)return{isError:Boolean(i(o)),isRequesting:!0,orderStatuses:t};const{getItems:m}=s(c.itemsStore),d=m("customers",{users:a.map((e=>e.customer_id)).filter((e=>0!==e)),_fields:["id","name","country","user_id"]});return{orders:a,isError:Boolean(i(a)),isRequesting:l,orderStatuses:t,customerItems:d}}));if(p){if(!t.length&&window.wcAdminFeatures.analytics)return(0,j.jsx)(u.EmptyContent,{title:(0,l.__)("You currently don’t have any actionable statuses. To display orders here, select orders that require further review in settings.","woocommerce"),actionLabel:(0,l.__)("Settings","woocommerce"),actionURL:(0,y.getAdminLink)("admin.php?page=wc-admin&path=/analytics/settings")});throw new Error("Failed to load orders, raise error to trigger ErrorBoundary")}const h=_?Array.from(_,(([,e])=>e)):[];return(0,j.jsx)(j.Fragment,{children:(0,j.jsx)(u.Section,{children:d?(0,j.jsx)(x.p,{className:"woocommerce-order-activity-card",hasAction:!0,hasDate:!0,lines:1}):C(m,h,((e,t)=>{if(!t)return null;if(i&&i.code===t)return s.formatAmount(e);const o=a[t];return o?(0,v.CurrencyFactory)({...i,symbol:(0,g.decodeEntities)(o),code:t}).formatAmount(e):`${t}${e}`}))})})};var N=o(48558),A=o(76154),E=o.n(A);class T extends r.Component{constructor(e){super(e),this.state={quantity:e.product.stock_quantity,editing:!1,edited:!1},this.beginEdit=this.beginEdit.bind(this),this.cancelEdit=this.cancelEdit.bind(this),this.onQuantityChange=this.onQuantityChange.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this),this.onSubmit=this.onSubmit.bind(this)}recordStockEvent(e,t={}){(0,_.recordEvent)(`activity_panel_stock_${e}`,t)}beginEdit(){const{product:e}=this.props;this.setState({editing:!0,quantity:e.stock_quantity},(()=>{this.quantityInput&&this.quantityInput.focus()})),this.recordStockEvent("update_stock")}cancelEdit(){const{product:e}=this.props;this.setState({editing:!1,quantity:e.stock_quantity}),this.recordStockEvent("cancel")}handleKeyDown(e){e.keyCode===N.ESCAPE&&this.cancelEdit()}onQuantityChange(e){this.setState({quantity:e.target.value})}async onSubmit(){const{product:e,updateProductStock:t,createNotice:o}=this.props,s=parseInt(this.state.quantity,10);e.stock_quantity!==s?(this.setState({editing:!1,edited:!0}),await t(e,s)?o("success",(0,l.sprintf)((0,l.__)("%s stock updated","woocommerce"),e.name),{actions:[{label:(0,l.__)("Undo","woocommerce"),onClick:()=>{t(e,e.stock_quantity),this.recordStockEvent("undo")}}]}):o("error",(0,l.sprintf)((0,l.__)("%s stock could not be updated","woocommerce"),e.name)),this.recordStockEvent("save",{quantity:s})):this.setState({editing:!1})}getActions(){const{editing:e}=this.state;return e?[(0,j.jsx)(p.Button,{type:"submit",isPrimary:!0,children:(0,l.__)("Save","woocommerce")},"save"),(0,j.jsx)(p.Button,{type:"reset",children:(0,l.__)("Cancel","woocommerce")},"cancel")]:[(0,j.jsx)(p.Button,{isSecondary:!0,onClick:this.beginEdit,children:(0,l.__)("Update stock","woocommerce")},"update")]}getBody(){const{product:e}=this.props,{editing:t,quantity:o}=this.state;return t?(0,j.jsxs)(r.Fragment,{children:[(0,j.jsx)(p.BaseControl,{className:"woocommerce-stock-activity-card__edit-quantity",children:(0,j.jsx)("input",{className:"components-text-control__input",type:"number",value:o,onKeyDown:this.handleKeyDown,onChange:this.onQuantityChange,ref:e=>{this.quantityInput=e}})}),(0,j.jsx)("span",{children:(0,l.__)("in stock","woocommerce")})]}):(0,j.jsx)("span",{className:(0,a.A)("woocommerce-stock-activity-card__stock-quantity",{"out-of-stock":e.stock_quantity<1}),children:(0,l.sprintf)((0,l.__)("%d in stock","woocommerce"),e.stock_quantity)})}render(){const{product:e}=this.props,{edited:t,editing:o}=this.state,s=(0,f.Qk)("notifyLowStockAmount",0),n=Number.isFinite(e.low_stock_amount)?e.low_stock_amount:s,r=e.stock_quantity<=n,c=e.last_order_date?(0,l.sprintf)((0,l.__)("Last ordered %s","woocommerce"),E().utc(e.last_order_date).fromNow()):null;if(!r&&!t)return null;const i=(0,j.jsx)(u.Link,{href:"post.php?action=edit&post="+(e.parent_id||e.id),onClick:()=>this.recordStockEvent("product_name"),type:"wp-admin",children:e.name});let m=null;"variation"===e.type&&(m=Object.values(e.attributes).map((e=>e.option)).join(", "));const d=(0,h.get)(e,["images",0])||(0,h.get)(e,["image"]),p=(0,a.A)("woocommerce-stock-activity-card__image-overlay__product",{"is-placeholder":!d||!d.src}),_=(0,j.jsx)("div",{className:"woocommerce-stock-activity-card__image-overlay",children:(0,j.jsx)("div",{className:p,children:(0,j.jsx)(u.ProductImage,{product:e})})}),w=(0,a.A)("woocommerce-stock-activity-card",{"is-dimmed":!o&&!r}),g=(0,j.jsx)(x.O,{className:w,title:i,subtitle:m,icon:_,date:c,actions:this.getActions(),children:this.getBody()});return o?(0,j.jsx)("form",{onReset:this.cancelEdit,onSubmit:this.onSubmit,children:g}):g}}const L={page:1,per_page:5,status:"publish",_fields:["attributes","id","images","last_order_date","low_stock_amount","name","parent_id","stock_quantity","type"]};class P extends r.Component{constructor(e){super(e),this.updateStock=this.updateStock.bind(this)}async updateStock(e,t){const{invalidateResolution:o,updateProductStock:s}=this.props,n=await s(e,t);return n&&(o("getItems",["products/low-in-stock",L]),o("getItemsTotalCount",["products/count-low-in-stock",w.e9,null])),n}renderProducts(){const{products:e,createNotice:t}=this.props;return e.map((e=>(0,j.jsx)(T,{product:e,updateProductStock:this.updateStock,createNotice:t},e.id)))}render(){const{lowStockProductsCount:e,isError:t,isRequesting:o,products:s}=this.props;if(t)throw new Error("Failed to load low stock products, Raise error to trigger ErrorBoundary");if(o||!s.length){const t=Math.min(5,null!=e?e:1),o=Array.from(new Array(t)).map(((e,t)=>(0,j.jsx)(x.p,{className:"woocommerce-stock-activity-card",hasAction:!0,lines:1},t)));return(0,j.jsx)(u.Section,{children:o})}return(0,j.jsx)(u.Section,{children:this.renderProducts()})}}P.defaultProps={products:[],isError:!1,isRequesting:!1};const I=(0,s.compose)((0,n.withSelect)((e=>{const{getItems:t,getItemsError:o,isResolving:s}=e(c.itemsStore);return{products:Array.from(t("products/low-in-stock",L).values()),isError:Boolean(o("products/low-in-stock",L)),isRequesting:s("getItems",["products/low-in-stock",L])}})),(0,n.withDispatch)((e=>{const{invalidateResolution:t,updateProductStock:o}=e(c.itemsStore),{createNotice:s}=e("core/notices");return{createNotice:s,invalidateResolution:t,updateProductStock:o}})))(P);var R=o(81739),M=o(73572);const F=()=>(0,j.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,j.jsx)("mask",{id:"mask0",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"1",y:"1",width:"14",height:"14",children:(0,j.jsx)("path",{d:"M7.99992 1.33301C4.31992 1.33301 1.33325 4.31967 1.33325 7.99967C1.33325 11.6797 4.31992 14.6663 7.99992 14.6663C11.6799 14.6663 14.6666 11.6797 14.6666 7.99967C14.6666 4.31967 11.6799 1.33301 7.99992 1.33301ZM7.99992 13.333C5.05992 13.333 2.66659 10.9397 2.66659 7.99967C2.66659 5.05967 5.05992 2.66634 7.99992 2.66634C10.9399 2.66634 13.3333 5.05967 13.3333 7.99967C13.3333 10.9397 10.9399 13.333 7.99992 13.333ZM6.66658 9.44634L11.0599 5.05301L11.9999 5.99967L6.66658 11.333L3.99992 8.66634L4.93992 7.72634L6.66658 9.44634Z",fill:"white"})}),(0,j.jsx)("g",{mask:"url(#mask0)",children:(0,j.jsx)("rect",{width:"16",height:"16",fill:"#4AB866"})})]});var B=o(12974),D=o(42288);const U={page:1,per_page:D.D9,status:"hold",_embed:1};class q extends r.Component{recordReviewEvent(e,t){(0,_.recordEvent)(`reviews_${e}`,t||{})}deleteReview(e){const{deleteReview:t,createNotice:o,updateReview:s,clearReviewsCache:n}=this.props;e&&t(e).then((()=>{n(),o("success",(0,l.__)("Review successfully deleted.","woocommerce"),{actions:[{label:(0,l.__)("Undo","woocommerce"),onClick:()=>{s(e,{status:"untrash"},{_embed:1}).then((()=>n()))}}]})})).catch((()=>{o("error",(0,l.__)("Review could not be deleted.","woocommerce"))}))}updateReviewStatus(e,t,o){const{createNotice:s,updateReview:n,clearReviewsCache:r}=this.props;e&&n(e,{status:t}).then((()=>{r(),s("success",(0,l.__)("Review successfully updated.","woocommerce"),{actions:[{label:(0,l.__)("Undo","woocommerce"),onClick:()=>{n(e,{status:o},{_embed:1}).then((()=>r()))}}]})})).catch((()=>{s("error",(0,l.__)("Review could not be updated.","woocommerce"))}))}renderReview(e){const t=e&&e._embedded&&e._embedded.up&&e._embedded.up[0]||null;if(e.isUpdating)return(0,j.jsx)(x.p,{className:"woocommerce-review-activity-card",hasAction:!0,hasDate:!0,lines:1},e.id);if((0,h.isNull)(t)||e.status!==U.status)return null;const o=(0,k.A)({mixedString:(0,l.sprintf)((0,l.__)("{{authorLink}}%1$s{{/authorLink}}{{verifiedCustomerIcon/}} reviewed {{productLink}}%2$s{{/productLink}}","woocommerce"),e.reviewer,t.name),components:{productLink:(0,j.jsx)(u.Link,{href:t.permalink,onClick:()=>this.recordReviewEvent("product"),type:"external"}),authorLink:(0,j.jsx)(u.Link,{href:(0,y.getAdminLink)("admin.php?page=wc-admin&path=%2Fcustomers&search="+e.reviewer),onClick:()=>this.recordReviewEvent("customer"),type:"external"}),verifiedCustomerIcon:e.verified?(0,j.jsx)("span",{className:"woocommerce-review-activity-card__verified",children:(0,j.jsx)(p.Tooltip,{text:(0,l.__)("Verified owner","woocommerce"),children:(0,j.jsx)("span",{children:(0,j.jsx)(F,{})})})}):null}}),s=(0,j.jsx)(r.Fragment,{children:(0,j.jsx)(u.ReviewRating,{review:e,icon:M.A,outlineIcon:R.A,size:13})}),n=(0,h.get)(t,["images",0])||(0,h.get)(t,["image"]),c=(0,a.A)("woocommerce-review-activity-card__image-overlay__product",{"is-placeholder":!n||!n.src}),i=(0,j.jsx)("div",{className:"woocommerce-review-activity-card__image-overlay",children:(0,j.jsx)("div",{className:c,children:(0,j.jsx)(u.ProductImage,{product:t,width:33})})}),m={date:e.date_created_gmt,status:e.status},d=[(0,j.jsx)(p.Button,{isSecondary:!0,onClick:()=>{this.recordReviewEvent("approve",m),this.updateReviewStatus(e.id,"approved",e.status)},children:(0,l.__)("Approve","woocommerce")},"approve-action"),(0,j.jsx)(p.Button,{isTertiary:!0,onClick:()=>{this.recordReviewEvent("mark_as_spam",m),this.updateReviewStatus(e.id,"spam",e.status)},children:(0,l.__)("Mark as spam","woocommerce")},"spam-action"),(0,j.jsx)(p.Button,{isDestructive:!0,isTertiary:!0,onClick:()=>{this.recordReviewEvent("delete",m),this.deleteReview(e.id)},children:(0,l.__)("Delete","woocommerce")},"delete-action")];return(0,j.jsx)(x.O,{className:"woocommerce-review-activity-card",title:o,subtitle:s,date:e.date_created_gmt,icon:i,actions:d,children:(0,j.jsx)("span",{dangerouslySetInnerHTML:(0,B.Ay)(e.review)})},e.id)}renderReviews(e){const t=e.map((e=>this.renderReview(e,this.props)));return 0===t.filter(Boolean).length?(0,j.jsx)(j.Fragment,{}):(0,j.jsxs)(j.Fragment,{children:[t,(0,j.jsx)(u.Link,{href:(0,y.getAdminLink)("edit.php?post_type=product&page=product-reviews"),onClick:()=>this.recordReviewEvent("reviews_manage"),className:"woocommerce-layout__activity-panel-outbound-link woocommerce-layout__activity-panel-empty",type:"wp-admin",children:(0,l.__)("Manage all reviews","woocommerce")})]})}render(){const{isRequesting:e,isError:t,reviews:o}=this.props;if(t)throw new Error("Failed to load reviews, Raise error to trigger ErrorBoundary");return(0,j.jsx)(r.Fragment,{children:(0,j.jsx)(u.Section,{children:e||!o.length?(0,j.jsx)(x.p,{className:"woocommerce-review-activity-card",hasAction:!0,hasDate:!0,lines:1}):(0,j.jsx)(j.Fragment,{children:this.renderReviews(o)})})})}}q.defaultProps={reviews:[],isError:!1,isRequesting:!1},q.contextType=v.CurrencyContext;const H=(0,s.compose)([(0,n.withSelect)(((e,t)=>{const{hasUnapprovedReviews:o}=t,{getReviews:s,getReviewsError:n,isResolving:r}=e(c.reviewsStore);let i=[],a=!1,l=!1;return o&&(i=s(U),a=Boolean(n(U)),l=r("getReviews",[U])),{reviews:i,isError:a,isRequesting:l}})),(0,n.withDispatch)(((e,t)=>{const{deleteReview:o,updateReview:s,invalidateResolution:n}=e(c.reviewsStore),{createNotice:r}=e("core/notices");return{deleteReview:o,createNotice:r,updateReview:s,clearReviewsCache:()=>{n("getReviews",[U]),t.reviews&&t.reviews.length<2&&n("getReviewsTotalCount",[D.YY])}}}))])(q);var O=o(24060),W=o(99915);const z={_fields:["id"]},Q={status:"publish",_fields:["id"]},V=()=>{const e=(0,n.useSelect)((e=>{const{getOrdersTotalCount:t,hasFinishedResolution:o}=e(c.ordersStore),{getProductsTotalCount:s,hasFinishedResolution:n}=e(c.productsStore),r=t(z,0),i=(0,w.VJ)(e),a=(0,f.Qk)("reviewsEnabled","no"),l=(0,w.xC)(e,i),m=(0,f.Qk)("manageStock","no"),d=(0,w.G9)(e),u=(0,D.my)(e),p=s(Q,0);return{loadingOrderAndProductCount:!o("getOrdersTotalCount",[z,0])||!n("getProductsTotalCount",[Q,0]),lowStockProductsCount:d,unapprovedReviewsCount:u,unreadOrdersCount:l,manageStock:m,isTaskListHidden:!(0,W.Oh)("setup"),publishedProductCount:p,reviewsEnabled:a,totalOrderCount:r,orderStatuses:i}})),t=e.loadingOrderAndProductCount?[]:function({lowStockProductsCount:e,unapprovedReviewsCount:t,unreadOrdersCount:o,manageStock:s,isTaskListHidden:n,orderStatuses:r,publishedProductCount:c,reviewsEnabled:i,totalOrderCount:a}){return n?[a>0&&{className:"woocommerce-homescreen-card",count:o,collapsible:!0,id:"orders-panel",initialOpen:!1,panel:(0,j.jsx)(u.__experimentalErrorBoundary,{errorMessage:(0,j.jsxs)(j.Fragment,{children:[(0,l.__)("There was an error getting your orders.","woocommerce"),(0,j.jsx)("br",{}),(0,l.__)("Please try again.","woocommerce")]}),children:(0,j.jsx)(S,{unreadOrdersCount:o,orderStatuses:r})}),title:(0,l.__)("Orders","woocommerce")},a>0&&c>0&&"yes"===s&&{className:"woocommerce-homescreen-card",count:e,id:"stock-panel",initialOpen:!1,collapsible:0!==e,panel:(0,j.jsx)(u.__experimentalErrorBoundary,{errorMessage:(0,j.jsxs)(j.Fragment,{children:[(0,l.__)("There was an error getting your low stock products.","woocommerce"),(0,j.jsx)("br",{}),(0,l.__)("Please try again.","woocommerce")]}),children:(0,j.jsx)(I,{lowStockProductsCount:e})}),title:(0,l.__)("Stock","woocommerce")},c>0&&t>0&&"yes"===i&&{className:"woocommerce-homescreen-card",id:"reviews-panel",count:t,initialOpen:!1,collapsible:0!==t,panel:(0,j.jsx)(u.__experimentalErrorBoundary,{errorMessage:(0,j.jsxs)(j.Fragment,{children:[(0,l.__)("There was an error getting your reviews.","woocommerce"),(0,j.jsx)("br",{}),(0,l.__)("Please try again.","woocommerce")]}),children:(0,j.jsx)(H,{hasUnapprovedReviews:t>0})}),title:(0,l.__)("Reviews","woocommerce")}].filter(Boolean):[]}(e);if((0,r.useEffect)((()=>{if(void 0!==e.isTaskListHidden){const o=t.reduce(((e,t)=>(e[(0,h.snakeCase)(t.id)]=!0,e)),{task_list:e.isTaskListHidden});(0,_.recordEvent)("activity_panel_visible_panels",o)}}),[e.isTaskListHidden,t]),0===t.length)return null;const o=e=>{const{opened_panel:t}=(0,O.al)(window.location.search);return e===t};return(0,j.jsx)(p.Panel,{className:"woocommerce-activity-panel",children:t.map((e=>{const{className:t,count:s,id:n,initialOpen:r,panel:c,title:i,collapsible:a}=e;return a?(0,j.jsx)(p.PanelBody,{title:[(0,j.jsx)(p.__experimentalText,{variant:"title.small",size:"20",lineHeight:"28px",children:i},i),null!==s&&(0,j.jsx)(u.Badge,{count:s},`${i}-badge`)],className:t,initialOpen:o(n)||r,collapsible:a,disabled:!a,onToggle:e=>{e&&(0,_.recordEvent)("activity_panel_open",{tab:n})},children:(0,j.jsx)(p.PanelRow,{children:c})},n):(0,j.jsx)("div",{className:"components-panel__body",children:(0,j.jsx)("h2",{className:"components-panel__body-title",children:(0,j.jsxs)(p.Button,{className:"components-panel__body-toggle","aria-expanded":!1,disabled:!0,children:[(0,j.jsx)(p.__experimentalText,{variant:"title.small",size:"20",lineHeight:"28px",children:i}),null!==s&&(0,j.jsx)(u.Badge,{count:s})]})})},n)}))})},J=({children:e,shouldStick:t=!1})=>{const[o,s]=(0,r.useState)(!1),n=(0,r.useRef)(null),c=(0,r.useRef)(null),i=(0,r.useCallback)((()=>{if(!n.current)return;const{bottom:e,top:t}=n.current.getBoundingClientRect();null===c.current&&(c.current=t);const o=e<window.innerHeight;t===c.current&&s(o)}),[]);return(0,r.useLayoutEffect)((()=>{if(t)return i(),window.addEventListener("resize",i),window.addEventListener("scroll",i),()=>{window.removeEventListener("resize",i),window.removeEventListener("scroll",i)}}),[i,t]),(0,j.jsx)("div",{className:"woocommerce-homescreen-column",ref:n,style:{position:t&&o?"sticky":"static"},children:e})};var $=o(41173),Y=o(14908),G=o(52619);const K=(0,G.applyFilters)("woocommerce_admin_homepage_default_stats",["revenue/total_sales","revenue/net_revenue","orders/orders_count","products/items_sold","jetpack/stats/visitors","jetpack/stats/views"]),Z=["revenue/net_revenue","products/items_sold"];var X=o(59505);const ee=(0,n.withSelect)(((e,{stats:t,query:o})=>(0,X.m)(e,t,o)))((({stats:e,primaryData:t,secondaryData:o,primaryRequesting:s,secondaryRequesting:n,primaryError:c,secondaryError:m,query:d})=>{const{formatAmount:p,getCurrencyConfig:h}=(0,r.useContext)(v.CurrencyContext);if(c||m)return null;const w=(0,i.getPersistedQuery)(d),g=h();return(0,j.jsx)("ul",{className:(0,a.A)("woocommerce-stats-overview__stats",{"is-even":e.length%2==0}),children:e.map((e=>{if(s||n)return(0,j.jsx)(u.SummaryNumberPlaceholder,{},e.stat);const{primaryValue:r,secondaryValue:c,delta:i,reportUrl:a,reportUrlType:m}=(0,X.w)({indicator:e,primaryData:t,secondaryData:o,currency:g,formatAmount:p,persistedQuery:w});return(0,j.jsx)(u.SummaryNumber,{isHomescreen:!0,href:a,hrefType:m,label:e.label,value:r,prevLabel:(0,l.__)("Previous period:","woocommerce"),prevValue:c,delta:i,onLinkClickCallback:()=>{(0,_.recordEvent)("statsoverview_indicators_click",{key:e.stat})}},e.stat)}))})}));o(66161),(0,l.__)("Facebook for WooCommerce","woocommerce"),(0,l.__)("Jetpack","woocommerce"),(0,l.__)("Klarna Checkout for WooCommerce","woocommerce"),(0,l.__)("Klarna Payments for WooCommerce","woocommerce"),(0,l.__)("Mailchimp for WooCommerce","woocommerce"),(0,l.__)("Creative Mail for WooCommerce","woocommerce"),(0,l.__)("WooCommerce PayPal","woocommerce"),(0,l.__)("WooCommerce Stripe","woocommerce"),(0,l.__)("WooCommerce Payfast","woocommerce"),(0,l.__)("WooPayments","woocommerce"),(0,l.__)("WooCommerce Shipping & Tax","woocommerce"),(0,l.__)("WooCommerce Shipping & Tax","woocommerce"),(0,l.__)("WooCommerce Shipping & Tax","woocommerce"),(0,l.__)("WooCommerce ShipStation Gateway","woocommerce"),(0,l.__)("Mercado Pago payments for WooCommerce","woocommerce"),(0,l.__)("Google for WooCommerce","woocommerce"),(0,l.__)("Razorpay","woocommerce"),(0,l.__)("MailPoet","woocommerce"),(0,l.__)("Pinterest for WooCommerce","woocommerce"),(0,l.__)("TikTok for WooCommerce","woocommerce"),(0,l.__)("Omnichannel for WooCommerce","woocommerce"),Error;const te=e=>n.controls.dispatch("core/notices","createNotice","error",e),oe=e=>({unavailable:(0,l.__)("Get Jetpack","woocommerce"),installed:(0,l.__)("Activate Jetpack","woocommerce"),activated:(0,l.__)("Connect Jetpack","woocommerce")}[e]||""),se=({onClickInstall:e,onClickDismiss:t,isBusy:o,jetpackInstallState:s})=>(0,j.jsxs)("article",{className:"woocommerce-stats-overview__install-jetpack-promo",children:[(0,j.jsxs)("div",{className:"woocommerce-stats-overview__install-jetpack-promo__content",children:[(0,j.jsx)(u.H,{children:(0,l.__)("Get traffic stats with Jetpack","woocommerce")}),(0,j.jsx)("p",{children:(0,l.__)("Keep an eye on your views and visitors metrics with Jetpack. Requires Jetpack plugin and a WordPress.com account.","woocommerce")})]}),(0,j.jsxs)("footer",{children:[(0,j.jsx)(p.Button,{isSecondary:!0,onClick:()=>{(0,_.recordEvent)("statsoverview_install_jetpack"),e()},disabled:o,isBusy:o,children:oe(s)}),(0,j.jsx)(p.Button,{isTertiary:!0,onClick:()=>{(0,_.recordEvent)("statsoverview_dismiss_install_jetpack"),t()},disabled:o,isBusy:o,children:(0,l.__)("No thanks","woocommerce")})]})]}),ne=()=>{const{currentUserCan:e}=(0,c.useUser)(),{updateUserPreferences:t,...o}=(0,c.useUserPreferences)(),{canUserInstallPlugins:s,jetpackInstallState:r,isBusy:i}=(0,n.useSelect)((t=>{const{getPluginInstallState:o,isPluginsRequesting:s}=t(c.pluginsStore),n=o("jetpack");return{isBusy:s("getJetpackConnectUrl")||s("installPlugins")||s("activatePlugins"),jetpackInstallState:n,canUserInstallPlugins:e("install_plugins")}})),{installJetpackAndConnect:a}=(0,n.useDispatch)(c.pluginsStore);return s?(0,j.jsx)(se,{jetpackInstallState:r,isBusy:i,onClickInstall:()=>{a(te,y.getAdminLink)},onClickDismiss:()=>{const e=o.homepage_stats||{};e.installJetpackDismissed=!0,t({homepage_stats:e})}}):null},{performanceIndicators:re=[]}=(0,f.Qk)("dataEndpoints",{performanceIndicators:[]}),ce=re.filter((e=>K.includes(e.stat))),ie=()=>(0,j.jsx)(Y.Text,{variant:"title.small",size:"20",lineHeight:"28px",children:(0,l.__)("Stats overview","woocommerce")}),ae=()=>{const{updateUserPreferences:e,...t}=(0,c.useUserPreferences)(),o=(0,h.get)(t,["homepage_stats","hiddenStats"],Z),s=(0,n.useSelect)((e=>{const t=e(c.pluginsStore);return t.isJetpackConnected()&&"activated"===t.getPluginInstallState("jetpack")}),[]),a=(t.homepage_stats||{}).installJetpackDismissed,m=ce.filter((e=>!o.includes(e.stat)));return(0,j.jsxs)(p.Card,{size:"large",className:"woocommerce-stats-overview woocommerce-homescreen-card",children:[(0,j.jsxs)(p.CardHeader,{size:"medium",children:[(0,j.jsx)(ie,{}),(0,j.jsx)(u.EllipsisMenu,{label:(0,l.__)("Choose which values to display","woocommerce"),renderContent:()=>(0,j.jsxs)(r.Fragment,{children:[(0,j.jsx)(u.MenuTitle,{children:(0,l.__)("Display stats:","woocommerce")}),ce.map((t=>{const s=!o.includes(t.stat);return(0,j.jsx)(u.MenuItem,{checked:s,isCheckbox:!0,isClickable:!0,onInvoke:()=>(t=>{const s=(0,h.xor)(o,[t]);e({homepage_stats:{hiddenStats:s}}),(0,_.recordEvent)("statsoverview_indicators_toggle",{indicator_name:t,status:s.includes(t)?"off":"on"})})(t.stat),children:t.label},t.stat)}))]})})]}),(0,j.jsx)(p.TabPanel,{className:"woocommerce-stats-overview__tabs",onSelect:e=>{(0,_.recordEvent)("statsoverview_date_picker_update",{period:e})},tabs:[{title:(0,l.__)("Today","woocommerce"),name:"today"},{title:(0,l.__)("Week to date","woocommerce"),name:"week"},{title:(0,l.__)("Month to date","woocommerce"),name:"month"}],children:e=>(0,j.jsxs)(r.Fragment,{children:[!s&&!a&&(0,j.jsx)(ne,{}),(0,j.jsx)(ee,{query:{period:e.name,compare:"previous_period"},stats:m})]})}),(0,j.jsx)(p.CardFooter,{children:(0,j.jsx)(u.Link,{className:"woocommerce-stats-overview__more-btn",href:(0,i.getNewPath)({},"/analytics/overview"),type:"wc-admin",onClick:()=>{(0,_.recordEvent)("statsoverview_indicators_click",{key:"view_detailed_stats"})},children:(0,l.__)("View detailed stats","woocommerce")})})]})};var le=o(94302),me=o(7833),de=o(44412),ue=o(31613),pe=o(37455),_e=o(35166),he=o(20273),we=o(24652);const ge=({title:e,children:t})=>(0,j.jsxs)("div",{className:"woocommerce-quick-links__category",children:[(0,j.jsx)("h3",{className:"woocommerce-quick-links__category-header",children:e}),t]});var ke=o(24148),ye=o(6513);const ve=({icon:e,title:t,href:o,linkType:s,onClick:n})=>{const r="external"===s;return(0,j.jsx)("div",{className:"woocommerce-quick-links__item",children:(0,j.jsxs)(u.Link,{onClick:n,href:o,type:s,target:r?"_blank":null,className:"woocommerce-quick-links__item-link",children:[(0,j.jsx)(ke.A,{className:"woocommerce-quick-links__item-link__icon",icon:e}),(0,j.jsx)(Y.Text,{className:"woocommerce-quick-links__item-link__text",as:"div",variant:"button",weight:"600",size:"14",lineHeight:"20px",children:t}),r&&(0,j.jsx)(ke.A,{icon:ye.A})]})})};function xe({path:e,tab:t=null,type:o,href:s=null}){return{"wc-admin":{href:`admin.php?page=wc-admin&path=%2F${e}`,linkType:"wc-admin"},"wp-admin":{href:e,linkType:"wp-admin"},"wc-settings":{href:`admin.php?page=wc-settings&tab=${t}`,linkType:"wp-admin"}}[o]||{href:s,linkType:"external"}}const fe=()=>{const e=(0,f.Qk)("shopUrl"),t=(0,G.applyFilters)("woocommerce_admin_homescreen_quicklinks",[]).reduce(((e,{icon:t,href:o,title:s})=>(new URL(o,window.location.href).origin===window.location.origin&&e.push({icon:t,link:{href:o,linkType:"wp-admin"},title:s,listItemTag:"quick-links-extension-link"}),e)),[]),o=function(e){return[{title:(0,l.__)("Marketing & Merchandising","woocommerce"),items:[{title:(0,l.__)("Marketing","woocommerce"),link:xe({type:"wc-admin",path:"marketing"}),icon:le.A,listItemTag:"marketing"},{title:(0,l.__)("Add products","woocommerce"),link:xe({type:"wp-admin",path:"post-new.php?post_type=product"}),icon:me.A,listItemTag:"add-products"},{title:(0,l.__)("Personalize my store","woocommerce"),link:xe({type:"wp-admin",path:"customize.php"}),icon:de.A,listItemTag:"personalize-store"},{title:(0,l.__)("View my store","woocommerce"),link:xe({type:"external",href:e}),icon:ue.A,listItemTag:"view-store"}]},{title:(0,l.__)("Settings","woocommerce"),items:[{title:(0,l.__)("Store details","woocommerce"),link:xe({type:"wc-settings",tab:"general"}),icon:pe.A,listItemTag:"edit-store-details"},{title:(0,l.__)("Payments","woocommerce"),link:xe({type:"wc-settings",tab:"checkout"}),icon:_e.A,listItemTag:"payment-settings"},{title:(0,l.__)("Tax","woocommerce"),link:xe({type:"wc-settings",tab:"tax"}),icon:he.A,listItemTag:"tax-settings"},{title:(0,l.__)("Shipping","woocommerce"),link:xe({type:"wc-settings",tab:"shipping"}),icon:we.A,listItemTag:"shipping-settings"}]}]}(e),s={title:(0,l.__)("Extensions","woocommerce"),items:t},n=t.length?[...o,s]:o;return(0,j.jsxs)(p.Card,{size:"medium",children:[(0,j.jsx)(p.CardHeader,{size:"medium",children:(0,j.jsx)(Y.Text,{variant:"title.small",size:"20",lineHeight:"28px",children:(0,l.__)("Store management","woocommerce")})}),(0,j.jsx)(p.CardBody,{size:"custom",className:"woocommerce-store-management-links__card-body",children:n.map((e=>(0,j.jsx)(ge,{title:e.title,children:e.items.map((({icon:e,listItemTag:t,title:o,link:{href:s,linkType:n}})=>(0,j.jsx)(ve,{icon:e,title:o,linkType:n,href:s,onClick:()=>{(0,_.recordEvent)("home_quick_links_click",{task_name:t})}},`${o}_${t}_${s}`)))},e.title)))})]})};var je=o(87979),be=o(48808),Ce=o(92279),Se=o(48214);const Ne=o.p+"9a6ad9a6f33d5f8cefde.png",Ae=({body:e,onDismiss:t})=>(0,j.jsxs)("div",{className:"mobile-app-modal-layout",children:[(0,j.jsx)("div",{className:"mobile-app-modal-content",children:e}),(0,j.jsx)("div",{className:"mobile-app-modal-illustration",children:(0,j.jsx)("img",{src:Ne,alt:(0,l.__)("Screen captures of the WooCommerce mobile app","woocommerce")})}),(0,j.jsx)(p.Button,{variant:"tertiary",className:"woocommerce__mobile-app-welcome-modal__close-button",label:(0,l.__)("Close","woocommerce"),icon:(0,j.jsx)(p.Icon,{icon:Se.A,viewBox:"6 4 12 14"}),iconSize:16,onClick:t})]}),Ee="full-connection";var Te=o(1455),Le=o.n(Te);const Pe="fetching",Ie="success",Re="error",Me=({onClickHandler:e,isFetching:t})=>(0,j.jsxs)(p.Button,{className:"send-magic-link-button",onClick:e,children:[t&&(0,j.jsx)(u.Spinner,{className:"send-magic-link-spinner"}),(0,j.jsx)("div",{style:{visibility:t?"hidden":"visible"},className:"send-magic-link-button-contents",children:(0,j.jsx)("div",{className:"send-magic-link-button-text",children:(0,l.__)("✨️ Send the sign-in link","woocommerce")})})]}),Fe=({returnToSendLinkPage:e})=>(0,j.jsxs)("div",{className:"email-sent-modal-body",children:[(0,j.jsx)("div",{className:"email-sent-illustration"}),(0,j.jsx)("div",{className:"email-sent-title",children:(0,j.jsx)("h1",{children:(0,l.__)("Check your email!","woocommerce")})}),(0,j.jsx)("div",{className:"email-sent-subheader-spacer",children:(0,j.jsx)("div",{className:"email-sent-subheader",children:(0,l.__)("We just sent you the magic link. Open it on your mobile device and follow the instructions.","woocommerce")})}),(0,j.jsxs)("div",{className:"email-sent-footer",children:[(0,j.jsx)("div",{className:"email-sent-footer-prompt",children:(0,l.__)("DIDN’T GET IT?","woocommerce")}),(0,j.jsx)("div",{className:"email-sent-footer-text",children:(0,k.A)({mixedString:(0,l.__)("Check your spam/junk email folder or {{ sendAnotherLink /}}.","woocommerce"),components:{sendAnotherLink:(0,j.jsx)(p.Button,{className:"email-sent-send-another-link",onClick:()=>{e()},children:(0,l.__)("send another link","woocommerce")})}})})]})]}),Be=({children:e})=>(0,j.jsxs)("div",{className:"jetpack-installation-content",children:[(0,j.jsxs)("div",{className:"modal-layout-header",children:[(0,j.jsx)("div",{className:"woo-icon"}),(0,j.jsx)("div",{className:"modal-header",children:(0,j.jsx)("h1",{children:(0,l.__)("Manage orders and track sales in real-time with the free mobile app","woocommerce")})})]}),(0,j.jsx)("div",{className:"modal-layout-body",children:e}),(0,j.jsxs)("div",{className:"modal-layout-footer",children:[(0,j.jsxs)("div",{className:"mobile-footer-icons",children:[(0,j.jsx)("div",{className:"apple-icon"}),(0,j.jsx)("div",{className:"android-icon"})]}),(0,j.jsx)("div",{className:"mobile-footer-blurb",children:(0,l.__)("The WooCommerce Mobile App is available on iOS and Android","woocommerce")})]})]});var De=o(42851);const Ue=()=>(0,j.jsx)("div",{children:(0,j.jsx)(De.hp,{value:"https://woocommerce.com/mobile/?utm_source=wc_onboarding_mobile_task",size:140})}),qe=({loginUrl:e})=>(0,j.jsxs)("div",{children:[e&&(0,j.jsxs)("div",{children:[(0,j.jsx)(De.hp,{value:e,size:140}),(0,j.jsx)("p",{children:(0,l.__)("The app version needs to be 15.7 or above to sign in with this link.","woocommerce")})]}),(0,j.jsx)("div",{children:(0,k.A)({mixedString:(0,l.__)("Any troubles signing in? Check out the {{link}}FAQ{{/link}}.","woocommerce"),components:{link:(0,j.jsx)(u.Link,{href:"https://woocommerce.com/document/android-ios-apps-login-help-faq/",target:"_blank",type:"external",onClick:()=>{(0,_.recordEvent)("onboarding_app_login_faq_click")}}),strong:(0,j.jsx)("strong",{})}})})]}),He=({step:e,isJetpackPluginInstalled:t,wordpressAccountEmailAddress:o,completeInstallationStepHandler:s,sendMagicLinkHandler:n,sendMagicLinkStatus:c})=>{const[i,a]=(0,r.useState)(void 0);return(0,r.useEffect)((()=>{if("first"===e)a([{key:"first",label:(0,l.__)("Install the mobile app","woocommerce"),description:(0,l.__)("Scan the code below to download or upgrade the app, or visit woo.com/mobile from your mobile device.","woocommerce"),content:(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(Ue,{}),(0,j.jsx)(p.Button,{variant:"primary",className:"install-app-button",onClick:()=>{s()},children:(0,l.__)("App is installed","woocommerce")})]})},{key:"second",label:(0,l.__)("Sign into the app","woocommerce"),description:"",content:(0,j.jsx)(j.Fragment,{})}]);else if("second"===e)if(t&&void 0!==o)a([{key:"first",label:(0,l.__)("App installed","woocommerce"),description:"",content:(0,j.jsx)(j.Fragment,{})},{key:"second",label:"Sign into the app",description:(0,l.sprintf)((0,l.__)("We’ll send a magic link to %s. Open it on your smartphone or tablet to sign into your store instantly.","woocommerce"),o),content:(0,j.jsx)(Me,{isFetching:c===Pe,onClickHandler:n})}]);else{const e=(0,f.Qk)("siteUrl"),t=(0,f.Qk)("currentUserData").username,o=`woocommerce://app-login?siteUrl=${encodeURIComponent(e)}&username=${encodeURIComponent(t)}`,s=o?(0,l.__)("Scan the QR code below and enter the wp-admin password in the app.","woocommerce"):(0,l.__)("Follow the instructions in the app to sign in.","woocommerce");a([{key:"first",label:(0,l.__)("App installed","woocommerce"),description:"",content:(0,j.jsx)(j.Fragment,{})},{key:"second",label:"Sign into the app",description:s,content:(0,j.jsx)(qe,{loginUrl:o})}])}}),[e,t,o,s,n,c]),(0,j.jsx)("div",{className:"login-stepper-wrapper",children:i&&(0,j.jsx)(u.Stepper,{isVertical:!0,currentStep:e,steps:i})})},Oe=({appInstalledClicked:e,isJetpackPluginInstalled:t,wordpressAccountEmailAddress:o,completeInstallationHandler:s,sendMagicLinkHandler:n,sendMagicLinkStatus:r})=>(0,j.jsxs)(Be,{children:[(0,j.jsx)("div",{className:"modal-subheader",children:(0,j.jsx)("h3",{children:(0,l.__)("Run your store from anywhere in two easy steps.","woocommerce")})}),(0,j.jsx)(He,{step:e?"second":"first",isJetpackPluginInstalled:t,wordpressAccountEmailAddress:o,completeInstallationStepHandler:s,sendMagicLinkHandler:n,sendMagicLinkStatus:r})]});var We=o(85810);const ze=()=>{const[e,t]=(0,r.useState)(!1),[o,s]=(0,r.useState)(!1),{state:a,jetpackConnectionData:m}=(()=>{const{currentUserCan:e}=(0,c.useUser)(),{canUserInstallPlugins:t,jetpackInstallState:o,jetpackConnectionData:s}=(0,n.useSelect)((t=>{const{getPluginInstallState:o,getJetpackConnectionData:s}=t(c.pluginsStore),n=o("jetpack");return{jetpackConnectionData:s(),jetpackInstallState:n,canUserInstallPlugins:e("install_plugins")}}),[e]),{installJetpackAndConnect:i}=(0,n.useDispatch)(c.pluginsStore),[a,l]=(0,r.useState)("initializing"),m=(0,r.useCallback)((()=>{const e=window.location.href;i(te,(()=>e+"&jetpackState=returning")),l("installing")}),[i]);return(0,r.useEffect)((()=>{if(t)switch(o){case"installed":l("not-activated");break;case"unavailable":l("not-installed");break;case"activated":s&&!s?.connectionOwner?l("userless-connection"):s&&!s?.currentUser?.isMaster?l("not-owner-of-connection"):s&&s?.currentUser?.isConnected&&s?.currentUser?.isMaster&&l(Ee)}else l("user-cannot-install")}),[t,o,s]),{state:a,installHandler:m,jetpackConnectionData:s}})(),{updateOptions:d}=(0,n.useDispatch)(c.optionsStore),[u,h]=(0,r.useState)(),[w]=(0,be.ok)(),{invalidateResolutionForStoreSelector:g}=(0,n.useDispatch)(c.onboardingStore);(0,r.useEffect)((()=>{w.get("mobileAppModal")?t(!0):t(!1),"returning"===w.get("jetpackState")&&s(!0)}),[w]);const[k,y]=(0,r.useState)(!1),[v,x]=(0,r.useState)(!1),[f,b]=(0,r.useState)(!1),{requestState:C,fetchMagicLinkApiCall:S}=(()=>{const[e,t]=(0,r.useState)("initializing"),{createNotice:o}=(0,n.useDispatch)("core/notices");return{requestState:e,fetchMagicLinkApiCall:(0,r.useCallback)((()=>{t(Pe),Le()({path:`${c.WC_ADMIN_NAMESPACE}/mobile-app/send-magic-link`}).then((e=>{"success"===e.code?t(Ie):(t(Re),o("error",(0,l.__)("Sorry, an unknown error occurred.","woocommerce")))})).catch((e=>{t(Re),(0,_.recordEvent)("magic_prompt_send_magic_link_error",{error:e.message,code:e.code}),"error_sending_mobile_magic_link"===e.code?o("error",(0,l.__)("We couldn’t send the link. Try again in a few seconds.","woocommerce")):"invalid_user_permission_view_admin"===e.code?o("error",(0,l.__)("Sorry, your account doesn’t have sufficient permission.","woocommerce")):"jetpack_not_connected"===e.code?o("error",e.message):o("error","We couldn’t send the link. Try again in a few seconds.")}))}),[o])}})(),N=(0,r.useCallback)((()=>{y(!0),(0,_.recordEvent)("onboarding_app_install_click")}),[]),A=(0,r.useCallback)((()=>{S(),(0,_.recordEvent)("magic_prompt_send_signin_link_click")}),[S]);(0,r.useEffect)((()=>{C===Ie&&x(!0)}),[C]),(0,r.useEffect)((()=>{if(v)h((0,j.jsx)(Fe,{returnToSendLinkPage:()=>{x(!1),b(!0),(0,_.recordEvent)("magic_prompt_retry_send_signin_link")}}));else{var e;const t=null!==(e=a===Ee&&void 0!==m?.currentUser?.wpcomUser?.email)&&void 0!==e&&e,o=m?.currentUser?.wpcomUser?.email;h((0,j.jsx)(Oe,{appInstalledClicked:k,isJetpackPluginInstalled:t,wordpressAccountEmailAddress:o,completeInstallationHandler:N,sendMagicLinkHandler:A,sendMagicLinkStatus:C}))}}),[k,A,v,o,m?.currentUser?.wpcomUser?.email,a,f,C,N]);const E=(0,r.useCallback)((()=>{(0,i.updateQueryString)({jetpackState:void 0,mobileAppModal:void 0},void 0,Object.fromEntries(w.entries()))}),[w]),T=()=>{d({woocommerce_admin_dismissed_mobile_app_modal:"yes"}).then((()=>g("getTaskLists"))),E(),t(!1)};return(0,j.jsx)(j.Fragment,{children:e&&(0,j.jsx)(p.Guide,{onFinish:T,contentLabel:"",className:"woocommerce__mobile-app-welcome-modal",pages:[{content:(0,j.jsx)(Ae,{body:u,onDismiss:T})}]})})},Qe="wc/admin/mobile-app-help-entry-callback";(0,Ce.registerPlugin)("woocommerce-mobile-app-modal",{render:()=>{const e=(0,r.useCallback)((e=>[...e,{title:(0,l.__)("Get the WooCommerce app","woocommerce"),link:(0,y.getAdminLink)("./admin.php?page=wc-admin&mobileAppModal=true"),linkType:"wc-admin"}]),[]);return(0,r.useEffect)((()=>{(0,G.removeFilter)(We.SETUP_TASK_HELP_ITEMS_FILTER,Qe),(0,G.addFilter)(We.SETUP_TASK_HELP_ITEMS_FILTER,Qe,e,10)}),[e]),null},scope:"woocommerce-admin"});const Ve=o.p+"205ccd8b65027df184d2.png",Je=({type:e})=>{const[t,o]=(0,r.useState)(!1),[s]=(0,be.ok)();let n=(0,l.__)("Your store emails have had an upgrade!","woocommerce"),c=(0,l.__)("We’ve made some exciting improvements to your email templates, including modern, shopper-friendly designs and new customization options. Head to your email settings to explore the new changes.","woocommerce");"try"===e&&(n=(0,l.__)("Store emails have had an upgrade!","woocommerce"),c=(0,l.__)("We’ve made some exciting improvements to our email templates, including modern, shopper-friendly designs and new customization options. Head to your email settings to explore the new features.","woocommerce")),(0,r.useEffect)((()=>{s.get("emailImprovementsModal")?o(!0):o(!1)}),[s]);const a=()=>{(0,i.updateQueryString)({emailImprovementsModal:void 0},void 0,Object.fromEntries(s.entries())),o(!1)};return(0,j.jsx)(j.Fragment,{children:t&&(0,j.jsx)(p.Guide,{onFinish:a,contentLabel:"",className:"woocommerce__email-improvements-modal",pages:[{content:(0,j.jsxs)("div",{className:"email-improvements-modal-layout",children:[(0,j.jsxs)("div",{className:"email-improvements-modal-content",children:[(0,j.jsx)("div",{className:"email-improvements-modal-content-image",children:(0,j.jsx)("img",{src:Ve,alt:"",width:250,height:240})}),(0,j.jsxs)("div",{children:[(0,j.jsx)("h1",{children:n}),(0,j.jsx)("p",{children:c})]}),(0,j.jsxs)("div",{className:"email-improvements-modal-footer",children:[(0,j.jsx)(p.Button,{variant:"tertiary",href:"https://developer.woocommerce.com/2025/04/09/woocommerce-9-8-modernized-designs-and-email-previews/",target:"_blank",children:(0,l.__)("Learn more","woocommerce")}),"try"===e?(0,j.jsx)(p.Button,{variant:"primary",href:"?page=wc-settings&tab=email&try-new-templates",children:(0,l.__)("Try the new templates","woocommerce")}):(0,j.jsx)(p.Button,{variant:"primary",href:"?page=wc-settings&tab=email",children:(0,l.__)("Customize your emails","woocommerce")})]})]}),(0,j.jsx)(p.Button,{variant:"tertiary",className:"email-improvements-modal-close-button",label:(0,l.__)("Close","woocommerce"),icon:(0,j.jsx)(p.Icon,{icon:Se.A,viewBox:"6 4 12 14"}),iconSize:24,onClick:a})]})}]})})},$e="woocommerce_homescreen_experimental_header_banner_item",Ye=({children:e,order:t=1})=>(0,j.jsx)(p.Fill,{name:$e,children:o=>(0,u.createOrderedChildren)(e,t,o)});Ye.Slot=({fillProps:e})=>(0,j.jsx)(p.Slot,{name:$e,fillProps:e,children:u.sortFillsByOrder});const Ge=({className:e})=>{const t=(0,Y.useSlot)($e);return Boolean(t?.fills?.length)?(0,j.jsx)("div",{className:(0,a.A)("woocommerce-homescreen__header",e),children:(0,j.jsx)(Ye.Slot,{})}):null},Ke="experimental_woocommerce_wcpay_feature",Ze=({children:e,order:t=1})=>(0,j.jsx)(p.Fill,{name:Ke,children:o=>(0,u.createOrderedChildren)(e,t,o)});Ze.Slot=({fillProps:e})=>(0,j.jsx)(p.Slot,{name:Ke,fillProps:e,children:u.sortFillsByOrder});const Xe=({className:e})=>{const t=(0,Y.useSlot)(Ke);return Boolean(t?.fills?.length)?(0,j.jsx)("div",{className:(0,a.A)("woocommerce-homescreen__header",e),children:(0,j.jsx)(Ze.Slot,{})}):null};var et=o(10837);const tt=(0,r.lazy)((()=>Promise.resolve().then(o.bind(o,87979)).then((e=>({default:e.TaskLists}))))),ot=(0,s.compose)((0,n.withSelect)((e=>{const{isNotesRequesting:t}=e(c.notesStore),{getOption:o}=e(c.optionsStore),s=o("woocommerce_default_homepage_layout")||"single_column",{getTaskLists:n,hasFinishedResolution:r}=e(c.onboardingStore),i=(0,f.Qk)("visibleTaskListIds",[]).length>0;let a=!1,l=[];return i&&(a=!r("getTaskLists"),l=n()),{defaultHomescreenLayout:s,isBatchUpdating:t("batchUpdateNotes"),isLoadingTaskLists:a,hasTaskList:i,showingProgressHeader:!!l.find((e=>e.isVisible&&e.displayProgressHeader))}})))((({defaultHomescreenLayout:e,query:t,hasTaskList:o,showingProgressHeader:s,isLoadingTaskLists:i})=>{var u,p;const _=(0,c.useUserPreferences)(),{createInfoNotice:h}=(0,n.dispatch)("core/notices"),{setupTaskListActive:w,setupTaskListHidden:g}=(0,W.fK)({setupTasklist:!0,extendedTaskList:!1}),k=!(Object.keys(t).length>0&&t.task),y=(0,et.B)(_.homepage_layout,e,w),v=(0,r.useRef)(!0),x=(0,r.useCallback)((()=>{v.current=window.innerWidth>=782}),[]);(0,r.useLayoutEffect)((()=>(x(),window.addEventListener("resize",x),()=>{window.removeEventListener("resize",x)})),[x]),(0,r.useEffect)((()=>{"test_account_created"===t?.nox&&h((0,l.__)("Your WooPayments test account was successfully created.","woocommerce"),{type:"info",duration:5e3})}),[t?.nox,h]);const f=v.current&&y,b=null!==(u=t.mobileAppModal)&&void 0!==u&&u,C=null!==(p=t.emailImprovementsModal)&&void 0!==p&&p,S="enabled"===C?"enabled":"try",N=()=>(0,j.jsxs)(r.Suspense,{fallback:(0,j.jsx)(je.TasksPlaceholder,{query:t}),children:[!g&&k&&(0,j.jsx)(j.Fragment,{children:(0,j.jsx)(je.ProgressTitle,{taskListId:"setup"})}),(0,j.jsx)(tt,{query:t})]});return(0,j.jsxs)(j.Fragment,{children:[k&&(0,j.jsx)(Ge,{className:(0,a.A)("woocommerce-homescreen",{"woocommerce-homescreen-column":!y})}),(0,j.jsxs)("div",{className:(0,a.A)("woocommerce-homescreen",{"two-columns":y}),children:[k?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)(J,{shouldStick:f,children:[!i&&!s&&(0,j.jsx)(m.A,{className:"your-store-today",title:(0,l.__)("Your store today","woocommerce"),subtitle:(0,l.__)("To-dos, tips, and insights for your business","woocommerce")}),!w&&(0,j.jsx)(Xe,{}),!(0,W.Oh)("setup")&&(0,j.jsx)(V,{}),o&&N(),(0,j.jsx)(d.A,{format:"promo-card"}),(0,j.jsx)($.A,{})]}),(0,j.jsxs)(J,{shouldStick:f,children:[window.wcAdminFeatures.analytics&&(0,j.jsx)(ae,{}),!w&&(0,j.jsx)(fe,{})]})]}):N(),b&&(0,j.jsx)(ze,{}),C&&(0,j.jsx)(Je,{type:S})]})]})})),st=(0,f.Qk)("onboarding",{}),nt=(0,s.compose)((0,c.withOnboardingHydration)({profileItems:st.profile}),(0,n.withSelect)((e=>{const{getProfileItems:t,hasFinishedResolution:o}=e(c.onboardingStore);return{profileItems:t(),hasFinishedResolution:o("getProfileItems",[])}})))((({profileItems:{completed:e,skipped:t}={},hasFinishedResolution:o})=>{(0,r.useEffect)((()=>{!o||e||t||(0,i.getHistory)().push((0,i.getNewPath)({},"/setup-wizard",{}))}),[o,e,t]);const s=(0,i.useQuery)();return(0,j.jsx)(ot,{query:s})}))},10837:(e,t,o)=>{o.d(t,{B:()=>s});const s=(e,t,o)=>{const s=!o||window.wcAdminFeatures.analytics;return"two_columns"===(e||t)&&s}},41173:(e,t,o)=>{o.d(t,{A:()=>S});var s=o(27723),n=o(86087),r=o(98846),c=o(56427),i=o(40314),a=o(47143),l=o(83306),m=o(14098),d=o(14484),u=o(14908),p=o(76154),_=o.n(p),h=o(99010),w=o(46591),g=o(24060),k=o(39793);const y=({onClose:e})=>{const{createNotice:t}=(0,a.useDispatch)("core/notices"),{batchUpdateNotes:o,removeAllNotes:n}=(0,a.useDispatch)(i.notesStore);return(0,k.jsx)(k.Fragment,{children:(0,k.jsx)(c.Modal,{title:(0,s.__)("Dismiss all messages","woocommerce"),className:"woocommerce-inbox-dismiss-all-modal",onRequestClose:e,children:(0,k.jsxs)("div",{className:"woocommerce-inbox-dismiss-all-modal__wrapper",children:[(0,k.jsx)("div",{className:"woocommerce-usage-modal__message",children:(0,s.__)("Are you sure? Inbox messages will be dismissed forever.","woocommerce")}),(0,k.jsxs)("div",{className:"woocommerce-usage-modal__actions",children:[(0,k.jsx)(c.Button,{onClick:e,children:(0,s.__)("Cancel","woocommerce")}),(0,k.jsx)(c.Button,{isPrimary:!0,onClick:()=>{(async()=>{(0,l.recordEvent)("wcadmin_inbox_action_dismissall",{});try{const e=await n({status:"unactioned"});t("success",(0,s.__)("All messages dismissed","woocommerce"),{actions:[{label:(0,s.__)("Undo","woocommerce"),onClick:()=>{o(e.map((e=>e.id)),{is_deleted:0})}}]})}catch(o){t("error",(0,s.__)("Messages could not be dismissed","woocommerce")),e()}})(),e()},children:(0,s.__)("Yes, dismiss all","woocommerce")})]})]})})})},v={page:1,per_page:5,status:"unactioned",type:i.QUERY_DEFAULTS.noteTypes,orderby:"date",order:"desc",_fields:["id","name","title","content","type","status","actions","date_created","date_created_gmt","layout","image","is_deleted","is_read","locale"]},x=["en_US","en_AU","en_CA","en_GB","en_ZA"],f=_()("2022-01-11","YYYY-MM-DD").valueOf(),j=(e,t)=>{(0,l.recordEvent)("inbox_action_click",{note_name:e.name,note_title:e.title,note_content_inner_link:t})};let b=!1;const C=({hasNotes:e,isBatchUpdating:t,notes:o,onDismiss:n,onNoteActionClick:i,onNoteVisible:a,setShowDismissAllModal:p,showHeader:_=!0,loadMoreNotes:w,allNotesFetched:g,notesHaveResolved:y,unreadNotesCount:x})=>{if(t)return;if(!e)return(0,k.jsx)(h.O,{className:"woocommerce-empty-activity-card",title:(0,s.__)("Your inbox is empty","woocommerce"),icon:!1,children:(0,s.__)("As things begin to happen in your store your inbox will start to fill up. You’ll see things like achievements, new feature announcements, extension recommendations and more!","woocommerce")});b||((0,l.recordEvent)("inbox_panel_view",{total:o.length}),b=!0);const f=Object.keys(o).map((e=>o[e]));return(0,k.jsxs)(c.Card,{size:"large",children:[_&&(0,k.jsxs)(c.CardHeader,{size:"medium",children:[(0,k.jsxs)("div",{className:"woocommerce-inbox-card__header",children:[(0,k.jsx)(u.Text,{size:"20",lineHeight:"28px",variant:"title.small",children:(0,s.__)("Inbox","woocommerce")}),(0,k.jsx)(r.Badge,{count:x})]}),(0,k.jsx)(r.EllipsisMenu,{label:(0,s.__)("Inbox Notes Options","woocommerce"),renderContent:({onToggle:e})=>(0,k.jsx)("div",{className:"woocommerce-inbox-card__section-controls",children:(0,k.jsx)(c.Button,{onClick:()=>{p(!0),e()},children:(0,s.__)("Dismiss all","woocommerce")})})})]}),(0,k.jsx)(m.A,{role:"menu",children:f.map((e=>{const{id:t,is_deleted:o}=e;return o?null:(0,k.jsx)(d.A,{timeout:500,classNames:"woocommerce-inbox-message",children:(0,k.jsx)(u.InboxNoteCard,{note:e,onDismiss:n,onNoteActionClick:i,onBodyLinkClick:j,onNoteVisible:a},t)},t)}))}),g?null:y?(0,k.jsx)(c.CardFooter,{className:"woocommerce-inbox-card__footer",size:"medium",children:(0,k.jsx)(c.Button,{isPrimary:!0,onClick:()=>{w()},children:f.length>v.per_page?(0,s.__)("Show more","woocommerce"):(0,s.__)("Show older","woocommerce")})}):(0,k.jsx)(u.InboxNotePlaceholder,{className:"banner message-is-unread"})]})},S=({showHeader:e=!0})=>{const[t,o]=(0,n.useState)(v.per_page),[c,m]=(0,n.useState)(!1),[d,p]=(0,n.useState)([]),[h,j]=(0,n.useState)({}),{createNotice:b}=(0,a.useDispatch)("core/notices"),{removeNote:S,updateNote:N,triggerNoteAction:A,invalidateResolutionForStoreSelector:E}=(0,a.useDispatch)(i.notesStore),T=(0,g.s9)(),L=(0,n.useMemo)((()=>({...v,per_page:t})),[t]),{isError:P,notes:I,notesHaveResolved:R,isBatchUpdating:M,unreadNotesCount:F}=(0,a.useSelect)((e=>{const{getNotes:t,getNotesError:o,isNotesRequesting:s,hasFinishedResolution:n}=e(i.notesStore);return{notes:t(L),unreadNotesCount:t({...v,is_read:!1,per_page:-1}).length,isError:Boolean(o("getNotes",[L])),isBatchUpdating:s("batchUpdateNotes"),notesHaveResolved:!s("batchUpdateNotes")&&n("getNotes",[L])}}));(0,n.useEffect)((()=>{R&&I.length<t&&m(!0),R&&I.length&&p(I.map((e=>{const t=_()(e.date_created_gmt,"YYYY-MM-DD").valueOf();return x.includes(e.locale)&&t>=f?{...e,content:(0,w.yz)(e.content,320)}:e})))}),[I,R]);const[B,D]=(0,n.useState)(!1);if(P){const e=(0,s.__)("There was an error getting your inbox. Please try again.","woocommerce"),t=(0,s.__)("Reload","woocommerce"),o=()=>{window.location.reload()};return(0,k.jsx)(r.EmptyContent,{title:e,actionLabel:t,actionURL:null,actionCallback:o})}return R&&!d.length?null:(0,k.jsxs)(k.Fragment,{children:[B&&(0,k.jsx)(y,{onClose:()=>{D(!1)}}),(0,k.jsxs)("div",{className:"woocommerce-homepage-notes-wrapper",children:[!R&&!d.length&&(0,k.jsx)(r.Section,{children:(0,k.jsx)(u.InboxNotePlaceholder,{className:"banner message-is-unread"})}),(0,k.jsx)(r.Section,{children:Boolean(d.length)&&C({loadMoreNotes:()=>{(0,l.recordEvent)("inbox_action_load_more",{quantity_shown:d.length}),o(t+10)},hasNotes:(0,w.e8)(d),isBatchUpdating:M,notes:d,onDismiss:async e=>{(0,l.recordEvent)("inbox_action_dismiss",{note_name:e.name,note_title:e.title,note_name_dismiss_all:!1,note_name_dismiss_confirmation:!0,screen:T});const t=e.id;try{await S(t),E("getNotes"),b("success",(0,s.__)("Message dismissed","woocommerce"),{actions:[{label:(0,s.__)("Undo","woocommerce"),onClick:async()=>{await N(t,{is_deleted:0}),E("getNotes")}}]})}catch(e){b("error",(0,s._n)("Message could not be dismissed","Messages could not be dismissed",1,"woocommerce"))}},onNoteActionClick:(e,t)=>{A(e.id,t.id)},onNoteVisible:e=>{h[e.id]||e.is_read||(j({...h,[e.id]:!0}),setTimeout((()=>{N(e.id,{is_read:!0})}),3e3)),(0,l.recordEvent)("inbox_note_view",{note_content:e.content,note_name:e.name,note_title:e.title,note_type:e.type,screen:T})},setShowDismissAllModal:D,showHeader:e,allNotesFetched:c,notesHaveResolved:R,unreadNotesCount:F})})]})]})}},12974:(e,t,o)=>{o.d(t,{Ay:()=>c});var s=o(13240);const n=["a","b","em","i","strong","p","br"],r=["target","href","rel","name","download"],c=e=>({__html:(0,s.sanitize)(e,{ALLOWED_TAGS:n,ALLOWED_ATTR:r})})},25595:(e,t,o)=>{o.d(t,{A:()=>p});var s=o(4921),n=o(86087),r=o(73290),c=o(72744),i=o(20273),a=o(24148),l=o(48214),m=o(12974),d=o(39793);const u={info:r.A,check:c.A,percent:i.A};function p(e){const{id:t,description:o,children:r,icon:c,isDismissible:i=!0,variant:p="info",className:_,onClose:h,onLoad:w}=e,[g,k]=(0,n.useState)("true"!==localStorage.getItem(`wc-marketplaceNoticeClosed-${t}`));if((0,n.useEffect)((()=>{g&&"function"==typeof w&&w()}),[g]),!g)return null;const y=(0,s.A)("woocommerce-marketplace__notice",`woocommerce-marketplace__notice--${p}`,{"is-dismissible":i},_),v=u[c||"info"],x=(0,s.A)("woocommerce-marketplace__notice-icon",`woocommerce-marketplace__notice-icon--${p}`);return(0,d.jsxs)("div",{className:y,children:[c&&(0,d.jsx)("span",{className:x,children:(0,d.jsx)(a.A,{icon:v})}),(0,d.jsxs)("div",{className:"woocommerce-marketplace__notice-content",children:[(0,d.jsx)("p",{className:"woocommerce-marketplace__notice-description",dangerouslySetInnerHTML:(0,m.Ay)(o)}),r&&(0,d.jsx)("div",{className:"woocommerce-marketplace__notice-children",children:r})]}),i&&(0,d.jsx)("button",{className:"woocommerce-marketplace__notice-close","aria-label":"Close",onClick:()=>{k(!1),localStorage.setItem(`wc-marketplaceNoticeClosed-${t}`,"true"),"function"==typeof h&&h()},children:(0,d.jsx)(a.A,{icon:l.A})})]})}},55516:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(39793);function n(){return(0,s.jsxs)("svg",{width:"72",height:"60",viewBox:"0 0 72 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsxs)("g",{clipPath:"url(#clip0_4074_10418)",children:[(0,s.jsx)("path",{d:"M68.5301 33.3144C68.0263 32.1006 66.3348 32.344 65.8443 31.1636C65.3538 29.9832 66.7251 28.9562 66.2213 27.7458C65.7175 26.5354 64.0259 26.7755 63.5355 25.5951C63.045 24.4147 64.4163 23.3877 63.9125 22.1773C63.4087 20.9669 61.7171 21.207 61.2267 20.0266C60.7362 18.8462 62.1075 17.8192 61.6037 16.6088C61.0999 15.395 59.4083 15.6385 58.9179 14.4581C58.4274 13.2777 59.7987 12.2507 59.2949 11.0403C58.7911 9.82652 57.0995 10.0699 56.6091 8.88955C56.1186 7.70915 57.4899 6.68214 56.9861 5.47174C56.4823 4.26134 54.7907 4.50142 54.3003 3.32102C53.8465 2.22733 55.0476 1.11696 54.8274 -0.00341797L0 22.5941C0.5038 23.8079 2.19537 23.5644 2.68582 24.7448C3.17627 25.9252 1.805 26.9522 2.3088 28.1626C2.8126 29.373 4.50417 29.133 4.99462 30.3134C5.48508 31.4937 4.11381 32.5208 4.61761 33.7312C5.12141 34.9416 6.81297 34.7015 7.30343 35.8819C7.79388 37.0623 6.42261 38.0893 6.92641 39.2997C7.43021 40.5134 9.12178 40.27 9.61223 41.4504C10.1027 42.6308 8.73142 43.6578 9.23522 44.8682C9.73902 46.0786 11.4306 45.8385 11.921 47.0189C12.4115 48.1993 11.0402 49.2263 11.544 50.4367C12.0478 51.6471 13.7394 51.4071 14.2298 52.5874C14.6836 53.6811 13.4825 54.7915 13.7027 55.9119L28.1928 49.9232L68.5368 33.3177L68.5301 33.3144Z",fill:"#720EEC"}),(0,s.jsx)("path",{d:"M13.696 55.912L28.1861 49.9234L52.3851 39.9634H7.46021C8.17086 40.4802 9.23852 40.5569 9.60886 41.4539C10.0993 42.6343 8.72805 43.6613 9.23185 44.8717C9.73565 46.0821 11.4272 45.842 11.9177 47.0224C12.4081 48.2028 11.0368 49.2298 11.5406 50.4402C12.0444 51.6506 13.736 51.4105 14.2265 52.5909C14.6802 53.6846 13.4791 54.795 13.6993 55.9154L13.696 55.912Z",fill:"#3C087E"}),(0,s.jsx)("path",{d:"M63.8523 41.9907C63.8523 37.4925 67.499 33.848 71.9998 33.848V23.988H17.873V33.848C22.3739 33.848 26.0206 37.4925 26.0206 41.9907C26.0206 46.4889 22.3739 50.1334 17.873 50.1334V59.9934H71.9998V50.1334C67.499 50.1334 63.8523 46.4889 63.8523 41.9907Z",fill:"#D1C1FF"}),(0,s.jsx)("path",{d:"M35.2527 37.676C35.2527 35.2051 37.0143 33.2878 39.6968 33.2878C42.3793 33.2878 44.1643 35.2051 44.1643 37.676C44.1643 40.1468 42.4026 42.0107 39.6968 42.0107C36.991 42.0107 35.2527 40.1201 35.2527 37.676ZM41.7954 37.676C41.7954 36.2288 40.9046 35.3385 39.6935 35.3385C38.4823 35.3385 37.6182 36.2288 37.6182 37.676C37.6182 39.1231 38.509 39.9601 39.6935 39.9601C40.8779 39.9601 41.7954 39.0664 41.7954 37.676ZM37.9852 51.0704L49.1789 33.5513H51.1774L39.9537 51.0704H37.9819H37.9852ZM44.8983 47.0524C44.8983 44.5849 46.6566 42.641 49.3391 42.641C52.0215 42.641 53.8065 44.5849 53.8065 47.0524C53.8065 49.5199 52.0182 51.3872 49.3391 51.3872C46.6599 51.3872 44.8983 49.4966 44.8983 47.0524ZM51.441 47.0524C51.441 45.6053 50.5468 44.715 49.3357 44.715C48.1246 44.715 47.2605 45.6053 47.2605 47.0524C47.2605 48.4996 48.1279 49.3365 49.3357 49.3365C50.5435 49.3365 51.441 48.4696 51.441 47.0524Z",fill:"#720EEC"})]}),(0,s.jsx)("defs",{children:(0,s.jsx)("clipPath",{id:"clip0_4074_10418",children:(0,s.jsx)("rect",{width:"72",height:"60",fill:"white"})})})]})}},62640:(e,t,o)=>{o.d(t,{A:()=>d});var s=o(56427),n=o(27723),r=o(86087),c=o(83306),i=o(12974),a=o(55516),l=o(39793);const m={percent:a.A},d=({promotion:e})=>{var t,o;const a=window.location.pathname+window.location.search,d=()=>JSON.parse(localStorage.getItem("wc-marketplaceDismissedPromos")||"[]"),[u,p]=(0,r.useState)(!d().includes(a));if((0,r.useEffect)((()=>{u&&(0,c.recordEvent)("marketplace_promotion_viewed",{path:a,format:"promo-card"})}),[u]),!u)return null;const _="promo-card"+(e.style?` ${e.style}`:""),h=(0,l.jsxs)("div",{className:"promo-content",children:[(0,l.jsx)("h2",{className:"promo-title",children:e.title?.en_US}),(0,l.jsx)("div",{className:"promo-text",dangerouslySetInnerHTML:(0,i.Ay)(e.content?.en_US)})]}),w=(0,l.jsxs)("div",{className:"promo-links",children:[(0,l.jsx)(s.Button,{className:"promo-cta",href:null!==(t=e.cta_link)&&void 0!==t?t:"",onClick:()=>((0,c.recordEvent)("marketplace_promotion_actioned",{path:a,target_uri:e.cta_link,format:"promo-card"}),!0),children:null!==(o=e.cta_label?.en_US)&&void 0!==o?o:""}),(0,l.jsx)(s.Button,{className:"promo-cta-link",onClick:()=>{p(!1),localStorage.setItem("wc-marketplaceDismissedPromos",JSON.stringify(d().concat(a))),(0,c.recordEvent)("marketplace_promotion_dismissed",{path:a,format:"promo-card"})},children:(0,n.__)("Dismiss","woocommerce")})]});function g(){if(e.icon&&Object.hasOwn(m,e.icon)){const t=m[e.icon];return t?(0,l.jsx)("div",{className:"promo-image",children:(0,r.createElement)(t)}):null}return null}return(0,l.jsx)("div",{className:_,children:"has-background"===e?.style?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"promo-content-links",children:[h,w]}),g()]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"promo-content-image",children:[h,g()]}),w]})})}},79492:(e,t,o)=>{o.d(t,{A:()=>a});var s=o(83306),n=o(56109),r=o(25595),c=o(62640),i=o(39793);const a=({format:e})=>{var t;if(!window?.wcMarketplace?.promotions||!Array.isArray(window?.wcMarketplace?.promotions))return null;const o=(null!==(t=window?.wcMarketplace?.promotions)&&void 0!==t?t:[]).filter((t=>t.format===e)),a=new URLSearchParams(window.location.search),l=a.get("page"),m=Date.now(),d=decodeURIComponent(a.get("path")||""),u=a.get("tab"),p=window.location.pathname+window.location.search,_=()=>{(0,s.recordEvent)("marketplace_promotion_viewed",{path:p,format:e})},h=()=>{(0,s.recordEvent)("marketplace_promotion_dismissed",{path:p,format:e})};return(0,i.jsx)(i.Fragment,{children:o.map(((e,t)=>{if(!e.pages)return null;if(!e.pages.some((e=>{if(e.pathname)return e.pathname===p;if(!e.path)return!1;const t=e=>e.startsWith("/")?e:`/${e}`,o=t(e.path),s=t(d);return e.page===l&&o===s&&(e.tab?u:!u)})))return null;const o=new Date(e.date_from_gmt).getTime(),s=new Date(e.date_to_gmt).getTime();return m<o||m>s?null:"promo-card"===e.format?(0,i.jsx)(c.A,{promotion:e},t):"notice"===e.format&&e?.content?(0,i.jsx)(r.A,{id:null!==(a=e.menu_item_id)&&void 0!==a?a:`promotion-${t}`,description:e.content[n.ne.userLocale]||e.content.en_US,variant:e.style?e.style:"info",icon:e?.icon||"",isDismissible:e.is_dismissible||!1,onLoad:_,onClose:h},t):null;var a}))})}},13832:(e,t,o)=>{o.d(t,{W:()=>n});var s=o(39793);const n=({numTasks:e=5,query:t})=>Boolean(t.task)?null:(0,s.jsx)("div",{className:"woocommerce-task-dashboard__container",children:(0,s.jsxs)("div",{className:"woocommerce-card woocommerce-task-card is-loading","aria-hidden":!0,children:[(0,s.jsx)("div",{className:"woocommerce-card__header",children:(0,s.jsx)("div",{className:"woocommerce-card__title-wrapper",children:(0,s.jsx)("div",{className:"woocommerce-card__title woocommerce-card__header-item",children:(0,s.jsx)("span",{className:"is-placeholder"})})})}),(0,s.jsx)("div",{className:"woocommerce-card__body",children:(0,s.jsx)("div",{className:"woocommerce-list",children:Array.from(new Array(e)).map(((e,t)=>(0,s.jsx)("div",{className:"woocommerce-list__item has-action",children:(0,s.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,s.jsx)("div",{className:"woocommerce-list__item-before",children:(0,s.jsx)("span",{className:"is-placeholder"})}),(0,s.jsx)("div",{className:"woocommerce-list__item-text",children:(0,s.jsx)("div",{className:"woocommerce-list__item-title",children:(0,s.jsx)("span",{className:"is-placeholder"})})}),(0,s.jsx)("div",{className:"woocommerce-list__item-after",children:(0,s.jsx)("span",{className:"is-placeholder"})})]})},t)))})})]})})},72685:(e,t,o)=>{o.d(t,{i:()=>w});var s=o(14908),n=o(56427),r=o(98846),c=o(39793);const i="woocommerce_tasklist_experimental_progress_title_item",a=({children:e,order:t=1})=>(0,c.jsx)(n.Fill,{name:i,children:o=>(0,r.createOrderedChildren)(e,t,o)});a.Slot=({fillProps:e})=>(0,c.jsx)(n.Slot,{name:i,fillProps:e,children:r.sortFillsByOrder});var l=o(27723),m=o(86087),d=o(47143),u=o(40314),p=o(15703),_=o(12974);const h=({taskListId:e})=>{const{loading:t,tasksCount:o,completedCount:s,hasVisitedTasks:n}=(0,d.useSelect)((t=>{const o=t(u.onboardingStore).getTaskList(e),s=t(u.onboardingStore).hasFinishedResolution("getTaskList",[e]),n=(0,u.getVisibleTasks)(o?.tasks||[]);return{loading:!s,tasksCount:n?.length,completedCount:n?.filter((e=>e.isComplete)).length,hasVisitedTasks:n?.filter((e=>e.isVisited&&"store_details"!==e.id)).length>0}}),[e]),r=(0,m.useMemo)((()=>{if(!n||s===o){const e=(0,p.getSetting)("siteTitle");return e?(0,l.sprintf)((0,l.__)("Welcome to %s","woocommerce"),e):(0,l.__)("Welcome to your store","woocommerce")}return s<=3?(0,l.__)("Let’s get you started","woocommerce")+"   🚀":s>3&&s<6?(0,l.__)("You’re on the right track","woocommerce"):(0,l.__)("You’re almost there","woocommerce")}),[s,n,o]);return t?null:(0,c.jsx)("h1",{className:"woocommerce-task-progress-header__title",dangerouslySetInnerHTML:(0,_.Ay)(r)})},w=({taskListId:e})=>{const t=(0,s.useSlot)(i);return Boolean(t?.fills?.length)?(0,c.jsx)(a.Slot,{fillProps:{taskListId:e}}):(0,c.jsx)(h,{taskListId:e})}}}]);