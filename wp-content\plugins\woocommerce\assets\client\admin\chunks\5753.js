"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[5753],{75753:(e,o,t)=>{t.d(o,{LO:()=>w,PE:()=>g,S:()=>_,CS:()=>b}),t(28327);var n=t(27723),i=t(56427),s=t(86087),a=t(47143),r=t(40314),c=t(96476),d=t(1069),m=t(22861),l=t(39793);const _=({gatewayProvider:e,settingsHref:o,onboardingHref:t,isOffline:_,acceptIncentive:u=()=>{},gatewayHasRecommendedPaymentMethods:p,installingPlugin:w,buttonText:y=(0,n.__)("Enable","woocommerce"),incentive:g=null,setOnboardingModalOpen:h,onboardingType:v})=>{const[b,x]=(0,s.useState)(!1),{createErrorNotice:f}=(0,a.dispatch)("core/notices"),{togglePaymentGateway:j,invalidateResolutionForStoreSelector:k}=(0,a.useDispatch)(r.paymentSettingsStore),P=()=>{f((0,n.__)("An error occurred. You will be redirected to the settings page, try enabling the payment gateway there.","woocommerce"),{type:"snackbar",explicitDismiss:!0})};return(0,l.jsx)(i.Button,{variant:"primary",isBusy:b,disabled:b||!!w,onClick:i=>{if(i.preventDefault(),e.state.enabled)return;(0,d.g2)("enable_click",e,{incentive_id:g?g.promo_id:"none"});const s=window.woocommerce_admin.nonces?.gateway_toggle||"";if(!s)return P(),void(window.location.href=o);x(!0),g&&u(g.promo_id),j(e.id,window.woocommerce_admin.ajax_url,s).then((i=>{if("needs_setup"===i.data)if(e.state.account_connected)f((0,n.__)("The provider could not be enabled. Check the Manage page for details.","woocommerce"),{type:"snackbar",explicitDismiss:!0,actions:[{label:(0,n.__)("Manage","woocommerce"),url:o}]}),(0,d.g2)("enable_failed",e,{reason:"needs_setup",incentive_id:g?g.promo_id:"none"});else if("native_in_context"===v&&h)(0,d.W7)("woopayments_onboarding_modal_opened",{from:"enable_gateway_button",source:m.Fx}),h(!0);else{if(!p)return void(window.location.href=t);(0,c.getHistory)().push((0,c.getNewPath)({},"/payment-methods"))}k("getPaymentProviders"),_&&k("getOfflinePaymentGateways"),x(!1)})).catch((()=>{(0,d.g2)("enable_failed",e,{reason:"error",incentive_id:g?g.promo_id:"none"}),x(!1),P(),window.location.href=o}))},href:o,children:y})};var u=t(1455),p=t.n(u);const w=({acceptIncentive:e,installingPlugin:o,buttonText:t=(0,n.__)("Activate payments","woocommerce"),incentive:a=null,setOnboardingModalOpen:r,onboardingType:c,disableTestAccountUrl:_})=>{const[u,w]=(0,s.useState)(!1);return(0,l.jsx)(i.Button,{variant:"primary",isBusy:u,disabled:u||!!o,onClick:()=>{if(w(!0),(0,d.TH)("activate_payments_button_click",{provider_id:m.$8,suggestion_id:m.eD,incentive_id:a?a.promo_id:"none",onboarding_type:c||"unknown",provider_extension_slug:m.bw}),!_)return a&&e(a.promo_id),w(!1),void("native_in_context"===c?((0,d.W7)("woopayments_onboarding_modal_opened",{from:"activate_payments_button",source:m.Fx}),r(!0)):window.location.href=(0,d.ZV)());p()({url:_,method:"POST"}).then((()=>{a&&e(a.promo_id),w(!1),"native_in_context"===c?((0,d.W7)("woopayments_onboarding_modal_opened",{from:"activate_payments_button",source:m.Fx}),r(!0)):window.location.href=(0,d.ZV)()})).catch((()=>{w(!1)}))},children:t})};var y=t(28239);const g=({gatewayProvider:e,settingsHref:o,onboardingHref:t,gatewayHasRecommendedPaymentMethods:c,installingPlugin:_,buttonText:u=(0,n.__)("Complete setup","woocommerce"),setOnboardingModalOpen:p,onboardingType:w,acceptIncentive:g=()=>{},incentive:h=null})=>{const[v,b]=(0,s.useState)(!1),[x,f]=(0,s.useState)(!1),{select:j}=(0,a.useSelect)((e=>({select:e})),[]),k=e.state.account_connected,P=e.onboarding.state.started,S=e.onboarding.state.completed;return(0,s.useEffect)((()=>{(0,d.j4)(e.id)&&"native_in_context"===w&&!S&&j(r.woopaymentsOnboardingStore).getOnboardingData()}),[e.id,S,w,j]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.Button,{variant:"primary",isBusy:v,disabled:v||!!_,onClick:()=>{if((0,d.g2)("complete_setup_click",e),b(!0),h&&g(h.promo_id),"native_in_context"===w)(0,d.W7)("woopayments_onboarding_modal_opened",{from:"complete_setup_button",source:m.Fx}),p(!0);else{if(k&&P)return k&&P&&!S?void(window.location.href=t):void(window.location.href=o);if(!c)return void(window.location.href=t);f(!0),b(!1)}b(!1)},children:u},e.id),(0,l.jsx)(y.Gw,{isOpen:x,onClose:()=>f(!1)})]})};var h=t(33068),v=t(93832);const b=({gatewayProvider:e,settingsHref:o,isInstallingPlugin:t,buttonText:s=(0,n.__)("Manage","woocommerce")})=>{const c=!!(0,v.getQueryArg)(o,"path"),m=(0,h.Zp)(),{invalidateResolutionForStoreSelector:_}=(0,a.useDispatch)(r.paymentGatewaysStore);return(0,l.jsx)(i.Button,{variant:"secondary",disabled:t,onClick:t=>{(0,d.g2)("provider_manage_click",e),t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||1===t.button?window.open(o,"_blank"):c?(_("getPaymentGateway"),m((0,d.Wg)(o))):window.location.href=o},children:s})}},28239:(e,o,t)=>{t.d(o,{lQ:()=>h,Mk:()=>u,Gw:()=>v});var n=t(27723),i=t(56427),s=t(86087),a=t(47143),r=t(40314),c=t(1455),d=t.n(c),m=t(1069),l=t(22861),_=t(39793);const u=({isOpen:e,onClose:o,hasAccount:t,isTestMode:c,isEmbeddedResetFlow:u=!1,resetUrl:p})=>{const[w,y]=(0,s.useState)(!1),{invalidateResolutionForStoreSelector:g}=(0,a.useDispatch)(r.paymentSettingsStore),{invalidateResolutionForStoreSelector:h}=(0,a.useDispatch)(r.woopaymentsOnboardingStore),{createNotice:v}=(0,a.useDispatch)("core/notices");let b,x,f;return t?(b=c?(0,n.__)("Reset your test account","woocommerce"):(0,n.__)("Reset your account","woocommerce"),x=c?(0,n.sprintf)((0,n.__)("When you reset your test account, all payment data — including your %s account details, test transactions, and payouts history — will be lost. Your order history will remain. This action cannot be undone, but you can create a new test account at any time.","woocommerce"),"WooPayments"):(0,n.sprintf)((0,n.__)("When you reset your account, all payment data — including your %s account details, test transactions, and payouts history — will be lost. Your order history will remain. This action cannot be undone, but you can create a new test account at any time.","woocommerce"),"WooPayments"),u&&(x=(0,n.sprintf)((0,n.__)("You need to reset your test account to continue onboarding with %1$s. This will create a new test account and reset any existing %2$s account details and test transactions.","woocommerce"),"WooPayments","WooPayments")),f=c?(0,n.__)("Yes, reset test account","woocommerce"):(0,n.__)("Yes, reset account","woocommerce")):(b=(0,n.__)("Reset onboarding","woocommerce"),x=(0,n.sprintf)((0,n.__)("When you reset the %s onboarding your progress and the provided data will be lost. This action cannot be undone, but you can restart the onboarding any time.","woocommerce"),"WooPayments"),f=(0,n.__)("Yes, reset onboarding","woocommerce")),(0,_.jsx)(_.Fragment,{children:e&&(0,_.jsxs)(i.Modal,{title:b,className:"woocommerce-woopayments-modal",isDismissible:!0,onRequestClose:o,children:[(0,_.jsxs)("div",{className:"woocommerce-woopayments-modal__content",children:[(0,_.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,_.jsx)("div",{children:(0,_.jsx)("span",{children:x})})}),(0,_.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,_.jsx)("h3",{children:(0,n.__)("Are you sure you'd like to continue?","woocommerce")})})]}),(0,_.jsx)("div",{className:"woocommerce-woopayments-modal__actions",children:(0,_.jsx)(i.Button,{className:u?"":"danger",variant:u?"primary":"secondary",isBusy:w,disabled:w,onClick:()=>{(0,m.TH)("provider_reset_onboarding_confirmation_click",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw}),(()=>{if(y(!0),!p)return(0,m.TH)("provider_reset_onboarding_failed",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw,reason:"missing_reset_url"}),v("error",(0,n.__)("Failed to reset: missing reset URL. Please refresh and try again.","woocommerce"),{isDismissible:!0}),void y(!1);d()({url:p,method:"POST"}).then((()=>{(0,m.TH)("provider_reset_onboarding_success",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw}),g("getPaymentProviders"),h("getOnboardingData")})).catch((()=>{(0,m.TH)("provider_reset_onboarding_failed",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw,reason:"error"}),v("error",t?(0,n.sprintf)((0,n.__)("Failed to reset your %s account.","woocommerce"),"WooPayments"):(0,n.sprintf)((0,n.__)("Failed to reset your %s onboarding.","woocommerce"),"WooPayments"),{isDismissible:!0})})).finally((()=>{y(!1),o()}))})()},children:f})})]})})};var p=t(98846),w=t(15703),y=t(36849),g=t(56109);const h=({isOpen:e,devMode:o,onClose:t})=>{const[a,r]=(0,s.useState)(!1),[c,d]=(0,s.useState)(!1);return(0,_.jsx)(_.Fragment,{children:e&&(0,_.jsxs)(i.Modal,{title:(0,n.__)("You're ready to test payments!","woocommerce"),className:"woocommerce-woopayments-modal",isDismissible:!0,onRequestClose:t,children:[(0,_.jsxs)("div",{className:"woocommerce-woopayments-modal__content",children:[(0,_.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,_.jsx)("div",{className:"woocommerce-woopayments-modal__content__item__description",children:(0,_.jsx)("p",{children:(0,y.A)({mixedString:(0,n.__)("We've created a test account for you so that you can begin testing payments on your store. {{break/}}Not sure what to test? Take a look at {{link}}how to test payments{{/link}}.","woocommerce"),components:{link:(0,_.jsx)(p.Link,{href:"https://woocommerce.com/document/woopayments/testing-and-troubleshooting/test-accounts/",target:"_blank",rel:"noreferrer",type:"external"}),break:(0,_.jsx)("br",{})}})})})}),(0,_.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,_.jsx)("h2",{children:(0,n.__)("What's next:","woocommerce")})}),(0,_.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,_.jsx)("img",{src:g.GZ+"images/icons/store.svg",alt:"",role:"presentation"}),(0,_.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,_.jsx)("h3",{children:(0,n.__)("Continue your store setup","woocommerce")}),(0,_.jsx)("div",{children:(0,n.__)("Finish completing the tasks required to launch your store.","woocommerce")})]})]}),!o&&(0,_.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,_.jsx)("img",{src:g.GZ+"images/icons/dollar.svg",alt:"",role:"presentation"}),(0,_.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,_.jsx)("h3",{children:(0,n.__)("Activate payments","woocommerce")}),(0,_.jsx)("div",{children:(0,_.jsx)("p",{children:(0,y.A)({mixedString:(0,n.__)("Provide some additional details about your business so you can begin accepting real payments. {{link}}Learn more{{/link}}","woocommerce"),components:{link:(0,_.jsx)(p.Link,{href:"https://woocommerce.com/document/woopayments/startup-guide/#sign-up-process",target:"_blank",rel:"noreferrer",type:"external"})}})})})]})]})]}),(0,_.jsxs)("div",{className:"woocommerce-woopayments-modal__actions",children:[(0,_.jsx)(i.Button,{variant:"primary",isBusy:c,disabled:c,onClick:()=>{(0,m.TH)("continue_store_setup_click",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw}),d(!0),window.location.href=(0,w.getAdminLink)("admin.php?page=wc-admin")},children:(0,n.__)("Continue store setup","woocommerce")}),!o&&(0,_.jsx)(i.Button,{variant:"secondary",isBusy:a,disabled:a,onClick:()=>{(0,m.TH)("switch_to_live_account_click",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw}),r(!0),window.location.href=(0,m.ZV)()},children:(0,n.__)("Activate payments","woocommerce")})]})]})})},v=({isOpen:e,onClose:o})=>{const[t,a]=(0,s.useState)(!1);return(0,_.jsx)(_.Fragment,{children:e&&(0,_.jsxs)(i.Modal,{title:(0,n.sprintf)((0,n.__)("An update to %s is required","woocommerce"),"WooPayments"),className:"woocommerce-woopayments-modal",isDismissible:!0,onRequestClose:o,children:[(0,_.jsxs)("div",{className:"woocommerce-woopayments-modal__content",children:[(0,_.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,_.jsx)("div",{children:(0,_.jsx)("span",{children:(0,n.sprintf)((0,n.__)("To continue, please update your %s plugin to the latest version. This update includes critical security enhancements and new features.","woocommerce"),"WooPayments")})})}),(0,_.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,_.jsx)("h3",{children:(0,n.__)("Would you like to update now?","woocommerce")})})]}),(0,_.jsxs)("div",{className:"woocommerce-woopayments-modal__actions",children:[(0,_.jsx)(i.Button,{variant:"primary",isBusy:t,disabled:t,onClick:()=>{a(!0),window.location.href=(0,w.getAdminLink)("plugins.php")},children:(0,n.__)("Update WooPayments","woocommerce")}),(0,_.jsx)(i.Button,{variant:"secondary",onClick:o,disabled:t,children:(0,n.__)("Not now","woocommerce")})]})]})})}}}]);