@keyframes wc-skeleton-shimmer{to{transform:translateX(-100%)}}.wp-block-woocommerce-accordion-item{display:grid;grid-template-rows:max-content 0fr}.wp-block-woocommerce-accordion-item.is-open{grid-template-rows:max-content 1fr}.wp-block-woocommerce-accordion-item .accordion-item__heading{color:inherit;margin:0;padding:0}.accordion-item__toggle{align-items:center;background:none;border:none;color:inherit;cursor:pointer;display:flex;font-family:inherit;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;outline:none;padding:var(--wp--preset--spacing--20,1em) 0;position:relative;text-align:inherit;text-decoration:inherit;text-transform:inherit;width:100%;word-spacing:inherit}.accordion-item__toggle>span{width:100%}.is-layout-flow>.wp-block-woocommerce-accordion-panel,.wp-block-woocommerce-accordion-panel{margin:0;overflow:hidden}.accordion-panel__wrapper{padding-bottom:var(--wp--preset--spacing--20,1em)}.is-style-no-icon .accordion-item__toggle-icon{background-color:unset}.wp-block-woocommerce-accordion-header.icon-position-left .accordion-item__toggle{flex-direction:row-reverse}.accordion-item__toggle:focus-visible{outline:2px solid -webkit-focus-ring-color;outline-offset:2px}@media(prefers-reduced-motion:no-preference){.wp-block-woocommerce-accordion-item .accordion-item__toggle-icon{transition:transform .2s ease-in-out}.wp-block-woocommerce-accordion-item{transition:grid-template-rows .3s ease-out}}.is-open .accordion-item__toggle-icon.has-icon-plus{transform:rotate(-45deg)}.is-open .accordion-item__toggle-icon.has-icon-chevron{transform:rotate(180deg)}.is-open .accordion-item__toggle-icon.has-icon-circlePlus{transform:rotate(-45deg)}.is-open .accordion-item__toggle-icon.has-icon-caret{transform:rotate(180deg)}.is-open .accordion-item__toggle-icon.has-icon-chevronRight{transform:rotate(-90deg)}
