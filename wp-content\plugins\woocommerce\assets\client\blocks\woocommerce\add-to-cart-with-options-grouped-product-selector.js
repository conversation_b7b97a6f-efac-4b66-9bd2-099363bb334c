import*as t from"@woocommerce/stores/woocommerce/product-data";import*as e from"@woocommerce/stores/woocommerce/cart";import*as r from"@wordpress/interactivity";var o={7860:(t,e,r)=>{r.d(e,{O:()=>c});var o=r(2833);r.d({},{});const n="I acknowledge that using a private store means my plugin will inevitably break on the next store release.",{state:a}=(0,o.store)("woocommerce",{},{lock:n}),i=(t,e,r,o)=>{let n,i=t;if("variable"===e){const e=((t,e)=>Array.isArray(t)&&Array.isArray(e)&&0!==t.length&&0!==e.length&&t.find((t=>Object.entries(t.attributes).every((([t,r])=>e.some((e=>!(e.attribute!==t)&&(e.value===r||e.value&&""===r)))))))||null)(r,o);e?.variation_id&&(i=e.variation_id,n=a?.products?.[t]?.variations?.[e?.variation_id])}else n=a?.products?.[i];if("object"!=typeof n||null===n)return null;const s="number"==typeof n.min?n.min:1,c="number"==typeof n.max&&n.max>=1?n.max:1/0,u=n.step||1;return{id:i,...n,min:s,max:c,step:u}},s=t=>{const e=(t=>{let e=null;return t.target instanceof HTMLButtonElement&&(e=t.target.parentElement?.querySelector(".qty")),t.target instanceof HTMLInputElement&&(e=t.target),e})(t);if(!e)return;const r=parseInt(e.value,10);return{currentValue:isNaN(r)?0:r,inputElement:e}},c=(t,e,r)=>{const o=a.cart?.items.find((e=>{return"variation"===e.type?!(e.id!==t||!e.variation||!r||e.variation.length!==r.length)&&(o=e,n=r,!(!Array.isArray(o.variation)||!Array.isArray(n))&&o.variation.length===n.length&&o.variation.every((({raw_attribute:t,value:e})=>n.some((r=>r.attribute===t&&(r.value.toLowerCase()===e.toLowerCase()||r.value&&""===e)))))):e.id===t;var o,n}));return(o?.quantity||0)+e},u=t=>{const e=new Event("change",{bubbles:!0});t.dispatchEvent(e)},{actions:d,state:l}=(0,o.store)("woocommerce/add-to-cart-with-options",{state:{noticeIds:[],get validationErrors(){const t=(0,o.getContext)();return t&&t.validationErrors?t.validationErrors:[]},get isFormValid(){const t=(0,o.getContext)();if(!t)return!0;const{productType:e,quantity:r}=t;return"grouped"===e?Object.values(r).some((t=>t>0)):0===l.validationErrors.length},get allowsDecrease(){const{quantity:t,childProductId:e,productType:r,productId:n,availableVariations:a,selectedAttributes:s}=(0,o.getContext)();if("grouped"===r&&t[e]>0)return!0;const c=i(e||n,r,a,s);if(!c)return!0;const{id:u,min:d,step:l}=c;return"number"!=typeof l||"number"!=typeof d||(t[u]||0)-l>=d},get allowsIncrease(){const{quantity:t,childProductId:e,productType:r,productId:n,availableVariations:a,selectedAttributes:s}=(0,o.getContext)(),c=i(e||n,r,a,s);if(!c)return!0;const{id:u,max:d,step:l}=c;return"number"!=typeof l||"number"!=typeof d||(t[u]||0)+l<=d}},actions:{setQuantity(t){const e=(0,o.getContext)();if("variable"===e.productType)e.availableVariations.map((t=>t.variation_id)).forEach((r=>{e.quantity[r]=t}));else{const r=e.childProductId||e.productId;e.quantity={...e.quantity,[r]:t}}},addError:t=>{const{validationErrors:e}=l;return e.push(t),t.code},clearErrors:t=>{const{validationErrors:e}=l;if(t){const r=e.filter((e=>e.group!==t));e.splice(0,e.length,...r)}else e.length=0},increaseQuantity:t=>{const e=s(t);if(!e)return;const{currentValue:r,inputElement:n}=e,{childProductId:a,productType:c,productId:l,availableVariations:p,selectedAttributes:m}=(0,o.getContext)(),y=i(a||l,c,p,m);if(!y)return;const{max:g,min:v,step:f}=y;if("number"!=typeof f||"number"!=typeof v||"number"!=typeof g)return;const b=r+f;if(b<=g){const t=Math.max(v,b);d.setQuantity(t),n.value=t.toString(),u(n)}},decreaseQuantity:t=>{const e=s(t);if(!e)return;const{currentValue:r,inputElement:n}=e,{childProductId:a,productType:c,productId:l,availableVariations:p,selectedAttributes:m}=(0,o.getContext)(),y=i(a||l,c,p,m);if(!y)return;const{min:g,max:v,step:f}=y;if("number"!=typeof f||"number"!=typeof g||"number"!=typeof v)return;let b=r-f;"grouped"===c&&b<g&&(b=r>g?g:0),b!==r&&(d.setQuantity(b),n.value=b.toString(),u(n))},handleQuantityInput:t=>{const e=s(t);if(!e)return;const{currentValue:r}=e;d.setQuantity(r)},handleQuantityChange:t=>{const e=s(t);if(!e)return;const{childProductId:r}=(0,o.getContext)(),{currentValue:n}=e,{productType:a,productId:c,availableVariations:l,selectedAttributes:p}=(0,o.getContext)(),m=i(r||c,a,l,p);if(!m)return;const{min:y,max:g,step:v}=m;if("number"!=typeof v||"number"!=typeof y||"number"!=typeof g)return;let f=Math.min(null!=g?g:1/0,Math.max(y,n));"grouped"===a&&n<y&&(f=0),t.target.value!==f.toString()&&(d.setQuantity(f),t.target.value=f.toString(),u(t.target))},handleQuantityCheckboxChange:t=>{const e=s(t);if(!e)return;const{inputElement:r}=e;d.setQuantity(r.checked?1:0)},*addToCart(){yield Promise.resolve().then(r.bind(r,1401));const{productId:t,quantity:e,selectedAttributes:a,productType:i}=(0,o.getContext)(),{variationId:s}=l,u=s||t,d=c(u,e[u],a),{actions:p}=(0,o.store)("woocommerce",{},{lock:n});yield p.addCartItem({id:u,quantity:d,variation:a,type:i})},*handleSubmit(t){t.preventDefault();const{isFormValid:e}=l;if(e)yield d.addToCart();else{yield Promise.resolve().then(r.bind(r,7908));const{actions:t}=(0,o.store)("woocommerce/store-notices",{},{lock:n}),{noticeIds:e,validationErrors:a}=l;e.forEach((e=>{t.removeNotice(e)})),e.splice(0,e.length);const i=a.map((e=>t.addNotice({notice:e.message,type:"error",dismissible:!0})));e.push(...i)}}}},{lock:n})},7908:t=>{t.exports=import("@woocommerce/stores/store-notices")},1401:t=>{t.exports=e},2833:(t,e,o)=>{var n,a;t.exports=(n={getConfig:()=>r.getConfig,getContext:()=>r.getContext,store:()=>r.store},a={},o.d(a,n),a)}},n={};function a(t){var e=n[t];if(void 0!==e)return e.exports;var r=n[t]={exports:{}};return o[t](r,r.exports,a),r.exports}a.d=(t,e)=>{for(var r in e)a.o(e,r)&&!a.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var i=a(2833),s=a(7860);const c="I acknowledge that using a private store means my plugin will inevitably break on the next store release.",{actions:u}=(0,i.store)("woocommerce/add-to-cart-with-options",{actions:{*addToCart(){yield Promise.resolve().then(a.bind(a,1401));const{quantity:t,selectedAttributes:e,productType:r,groupedProductIds:o}=(0,i.getContext)(),n=[];for(const a of o){if(0===t[a])continue;const o=(0,s.O)(a,t[a]);n.push({id:a,quantity:o,variation:e,type:r})}const{actions:u}=(0,i.store)("woocommerce",{},{lock:c});yield u.batchAddCartItems(n)}},callbacks:{validateGrouped:()=>{u.clearErrors("grouped-product");const{errorMessages:t}=(0,i.getConfig)(),{quantity:e}=(0,i.getContext)();Object.values(e).some((t=>t>0))||u.addError({code:"groupedProductAddToCartMissingItems",message:t?.groupedProductAddToCartMissingItems||"",group:"grouped-product"})}}},{lock:c});