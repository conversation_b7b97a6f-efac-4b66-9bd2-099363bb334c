import*as e from"@wordpress/interactivity";var t={7908:e=>{e.exports=import("@woocommerce/stores/store-notices")}},r={};function o(e){var a=r[e];if(void 0!==a)return a.exports;var n=r[e]={exports:{}};return t[e](n,n.exports,o),n.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const a=(s={store:()=>e.store},i={},o.d(i,s),i),n=({preserveCartData:e=!1})=>{((e,{bubbles:t=!1,cancelable:r=!1,element:o,detail:a={}})=>{if(!CustomEvent)return;o||(o=document.body);const n=new CustomEvent(e,{bubbles:t,cancelable:r,detail:a});o.dispatchEvent(n)})("wc-blocks_added_to_cart",{bubbles:!0,cancelable:!0,detail:{preserveCartData:e}})};var s,i;function c(e){return"name"in e}function d(e,t){return!e.ok}function y(e){return Object.assign(new Error(e.message||"Unknown error."),{code:e.code||"unknown_error"})}const l=e=>({notice:e.message,type:"error",dismissible:!0}),p=e=>({notice:e,type:"notice",dismissible:!0}),u=(e,t,r)=>{const o=e.items,a=t.items,{productsPendingAdd:n=[],cartItemsPendingQuantity:s=[],cartItemsPendingDelete:i=[]}=r,d=o.filter((e=>e.key&&c(e)&&!a.some((t=>e.key===t.key))&&!i.includes(e.key))),y=a.filter((e=>{if(!c(e))return!1;const t=o.find((t=>t.key===e.key));return t?!s.includes(e.key)&&e.quantity!==t.quantity:!n.includes(e.id)}));return[...d.map((e=>p('"%s" was removed from your cart.'.replace("%s",e.name)))),...y.map((e=>p('The quantity of "%1$s" was changed to %2$d.'.replace("%1$s",e.name).replace("%2$d",e.quantity.toString()))))]};let m=!1,h=3e3;function v({quantityChanges:e}){window.dispatchEvent(new CustomEvent("wc-blocks_store_sync_required",{detail:{type:"from_iAPI",quantityChanges:e}}))}const{state:f,actions:b}=(0,a.store)("woocommerce",{actions:{*removeCartItem(e){const t=JSON.stringify(f.cart);f.cart.items=f.cart.items.filter((t=>t.key!==e));try{const t=yield fetch(`${f.restUrl}wc/store/v1/cart/remove-item`,{method:"POST",headers:{Nonce:f.nonce,"Content-Type":"application/json"},body:JSON.stringify({key:e})}),r=yield t.json();if(d(t))throw y(r);const o={cartItemsPendingDelete:[e]},a=u(f.cart,r,o),n=r.errors.map(l);yield b.updateNotices([...a,...n],!0),f.cart=r,v({quantityChanges:o})}catch(e){f.cart=JSON.parse(t),b.showNoticeError(e)}},*addCartItem({id:e,quantity:t,variation:r,updateOptimistically:o=!0}){let a=f.cart.items.find((t=>"variation"===t.type?!(e!==t.id||!t.variation||!r||t.variation.length!==r.length)&&((e,t)=>!(!Array.isArray(e.variation)||!Array.isArray(t))&&e.variation.length===t.length&&e.variation.every((({raw_attribute:e,value:r})=>t.some((t=>t.attribute===e&&(t.value.toLowerCase()===r.toLowerCase()||t.value&&""===r))))))(t,r):e===t.id));const s=a?"update-item":"add-item",i=JSON.stringify(f.cart),c={};a?(a.key&&(c.cartItemsPendingQuantity=[a.key]),o&&(a.quantity=t)):(a={id:e,quantity:t,variation:r},c.productsPendingAdd=[e],o&&f.cart.items.push(a));try{const e=yield fetch(`${f.restUrl}wc/store/v1/cart/${s}`,{method:"POST",headers:{Nonce:f.nonce,"Content-Type":"application/json"},body:JSON.stringify(a)}),t=yield e.json();if(d(e))throw y(t);const r=u(f.cart,t,c),o=t.errors.map(l);yield b.updateNotices([...r,...o],!0),f.cart=t,n({preserveCartData:!0}),v({quantityChanges:c})}catch(e){f.cart=JSON.parse(i),b.showNoticeError(e)}},*batchAddCartItems(e){const t=JSON.stringify(f.cart),r={};try{const t=e.map((e=>{const t=f.cart.items.find((({id:t})=>e.id===t));return t?(t.quantity=e.quantity,t.key&&(r.cartItemsPendingQuantity=[t.key]),{method:"POST",path:"/wc/store/v1/cart/update-item",headers:{Nonce:f.nonce,"Content-Type":"application/json"},body:t}):(e={id:e.id,quantity:e.quantity,variation:e.variation},f.cart.items.push(e),r.productsPendingAdd=r.productsPendingAdd?[...r.productsPendingAdd,e.id]:[e.id],{method:"POST",path:"/wc/store/v1/cart/add-item",headers:{Nonce:f.nonce,"Content-Type":"application/json"},body:e})})),o=yield fetch(`${f.restUrl}wc/store/v1/batch`,{method:"POST",headers:{Nonce:f.nonce,"Content-Type":"application/json"},body:JSON.stringify({requests:t})}),a=yield o.json(),s=Array.isArray(a.responses)?a.responses.filter((e=>e.status<200||e.status>=300)):[];if(s.length>0)throw y(s[0].body);const i=Array.isArray(a.responses)?a.responses.filter((e=>e.status>=200&&e.status<300)):[];if(i.length>0){const e=i[i.length-1]?.body,t=u(f.cart,e,r),o=i.flatMap((e=>{var t;return(null!==(t=e.body.errors)&&void 0!==t?t:[]).map(l)}));yield b.updateNotices([...t,...o],!0),f.cart=e,n({preserveCartData:!0}),v({quantityChanges:r})}yield b.updateNotices(s.filter((e=>e.body&&"object"==typeof e.body)).map((({body:e})=>l(e))))}catch(e){f.cart=JSON.parse(t),b.showNoticeError(e)}},*refreshCartItems(){if(!m){m=!0;try{const e=yield fetch(`${f.restUrl}wc/store/v1/cart`,{headers:{"Content-Type":"application/json"}}),t=yield e.json();if(d(e))throw y(t);f.cart=t,h=3e3}catch(e){setTimeout(b.refreshCartItems,h),h*=2}finally{m=!1}}},*showNoticeError(e){yield Promise.resolve().then(o.bind(o,7908));const{actions:t}=(0,a.store)("woocommerce/store-notices",{},{lock:"I acknowledge that using a private store means my plugin will inevitably break on the next store release."}),{code:r,message:n}=e,s=f.errorMessages?.[r]||n;t.addNotice({notice:s,type:"error",dismissible:!0}),console.error(e)},*updateNotices(e=[],t=!1){yield Promise.resolve().then(o.bind(o,7908));const{state:r,actions:n}=(0,a.store)("woocommerce/store-notices",{},{lock:"I acknowledge that using a private store means my plugin will inevitably break on the next store release."}),s=e.map((e=>n.addNotice(e))),{notices:i}=r;t&&i.map((({id:e})=>e)).filter((e=>!s.includes(e))).forEach((e=>n.removeNotice(e)))}}},{lock:!0});window.addEventListener("wc-blocks_store_sync_required",(async e=>{"from_@wordpress/data"===e.detail.type&&b.refreshCartItems()}));