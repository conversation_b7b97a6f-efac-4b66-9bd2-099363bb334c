"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[8],{26016:(r,e,t)=>{t.r(e),t.d(e,{default:()=>y});var s=t(86087),n=t(29491),o=t(47143),i=t(66087),c=t(96476),a=t(40314),u=t(98846),p=t(94111),h=t(11357),l=t(5741),g=t(39793);const d=({params:r,path:e})=>r.report||e.replace(/^\/+/,"");class m extends s.Component{constructor(){super(...arguments),this.state={hasError:!1}}componentDidCatch(r){this.setState({hasError:!0}),console.warn(r)}render(){if(this.state.hasError)return null;const{isError:r,reports:e}=this.props;if(r)return(0,g.jsx)(u.AnalyticsError,{});const t=d(this.props),s=(0,i.find)(e,{report:t});if(!s)return(0,g.jsx)(h.N,{});const n=s.component;return(0,g.jsx)(p.CurrencyContext.Provider,{value:(0,p.getFilteredCurrencyInstance)((0,c.getQuery)()),children:(0,g.jsx)(n,{...this.props})})}}const y=(0,n.compose)((function(r){return function(e){const t=(0,l.r)();return(0,g.jsx)(r,{...e,reports:t})}}),(0,o.withSelect)(((r,e)=>{const t=(0,c.getQuery)(),{search:s}=t;if(!s)return{};const n=d(e),o=(0,c.getSearchWords)(t),i="categories"===n&&"single_category"===t.filter?"products":n,u=r(a.itemsStore),p=(0,a.searchItemsByString)(u,i,o,{per_page:100}),{isError:h,isRequesting:l,items:g}=p,m=Object.keys(g);return m.length?{isError:h,isRequesting:l,query:{...e.query,[i]:m.join(",")}}:{isError:h,isRequesting:l}})))(m)}}]);