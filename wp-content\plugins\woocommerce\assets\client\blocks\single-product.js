(()=>{var e,t,o,r,s,c={4264:(e,t,o)=>{"use strict";o.d(t,{e:()=>r});let r=function(e){return e.SINGLE="single",e.THUMBNAIL="thumbnail",e}({})},8723:(e,t,o)=>{"use strict";const r=window.wp.blocks;var s=o(4530),c=o(3492);const n=window.wc.wcBlocksRegistry;var i=o(6087),a=o(8331);o.p=a.XK,(0,n.registerBlockComponent)({blockName:"woocommerce/product-price",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(2388)]).then(o.bind(o,1308))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-image",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(4232)]).then(o.bind(o,933))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-title",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(2105)]).then(o.bind(o,5168))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-rating",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(462)]).then(o.bind(o,9812))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-rating-stars",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(8578)]).then(o.bind(o,7220))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-rating-counter",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(8553)]).then(o.bind(o,9147))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-average-rating",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(8647)]).then(o.bind(o,4514))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-button",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(7409)]).then(o.bind(o,595))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-summary",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(3895)]).then(o.bind(o,4001))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-sale-badge",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(4442)]).then(o.bind(o,3848))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-sku",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(2724)]).then(o.bind(o,1648))))}),(0,n.registerBlockComponent)({blockName:"woocommerce/product-stock-indicator",component:(0,i.lazy)((()=>Promise.all([o.e(94),o.e(345)]).then(o.bind(o,6374))))});const l=JSON.parse('{"name":"woocommerce/single-product","icon":"info","title":"Product","description":"Display a single product of your choice with full control over its presentation.","category":"woocommerce","keywords":["WooCommerce","single product"],"supports":{"interactivity":true,"align":["wide","full"]},"attributes":{"isPreview":{"type":"boolean","default":false},"productId":{"type":"number"}},"example":{"attributes":{"isPreview":true}},"usesContext":["postId","postType","queryId"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}');var d=o(6427),m=o(3925),u=o(8992),p=o(790);const h="woocommerce/product-query/product-title";!function(e,{blockDescription:t,blockIcon:o,blockTitle:s,variationName:c,scope:n}){(0,r.registerBlockVariation)(e,{description:t,name:c,title:s,isActive:e=>e.__woocommerceNamespace===c,icon:{src:o},attributes:{__woocommerceNamespace:c},scope:n})}("core/post-title",{blockDescription:m.description,blockIcon:(0,p.jsx)(d.Icon,{icon:u.A}),blockTitle:m.title,variationName:h,scope:["block"]});var g=o(4264);const w=(0,p.jsx)(s.A,{icon:c.A,className:"wc-block-editor-components-block-icon"}),b=[["core/columns",{},[["core/column",{},[["woocommerce/product-image",{showSaleBadge:!1,isDescendentOfSingleProductBlock:!0,imageSizing:g.e.SINGLE},[["woocommerce/product-sale-badge",{align:"right"}]]]]],["core/column",{},[["core/post-title",{headingLevel:2,isLink:!0,__woocommerceNamespace:h}],["woocommerce/product-rating",{isDescendentOfSingleProductBlock:!0}],["woocommerce/product-price",{isDescendentOfSingleProductBlock:!0}],["woocommerce/product-summary",{isDescendentOfSingleProductBlock:!0}],["woocommerce/add-to-cart-form"],["woocommerce/product-meta"]]]]]],x=["core/columns","core/column","core/post-title","core/post-excerpt","woocommerce/add-to-cart-form","woocommerce/add-to-cart-with-options","woocommerce/product-meta","woocommerce/product-gallery","woocommerce/product-reviews","woocommerce/product-details",...Object.keys((_=l.name,(0,n.getRegisteredBlockComponents)(_)))];var _,v=o(7723),f=o(9491);const k=window.wp.url,j=window.wp.apiFetch;var y=o.n(j),S=o(5703);const P=({selected:e=[],search:t="",queryArgs:o={}})=>{const r=(({selected:e=[],search:t="",queryArgs:o={}})=>{const r=a.r7.productCount>100,s={per_page:r?100:0,catalog_visibility:"any",search:t,orderby:"title",order:"asc"},c=[(0,k.addQueryArgs)("/wc/store/v1/products",{...s,...o})];return r&&e.length&&c.push((0,k.addQueryArgs)("/wc/store/v1/products",{catalog_visibility:"any",include:e,per_page:0})),c})({selected:e,search:t,queryArgs:o});return Promise.all(r.map((e=>y()({path:e})))).then((e=>{const t=((e,t)=>{const o=new Map;return e.filter((e=>{const r=t(e);return!o.has(r)&&(o.set(r,e),!0)}))})(e.flat(),(e=>e.id));return t.map((e=>({...e,parent:0})))})).catch((e=>{throw e}))},E=async e=>{if(!("json"in e))return{code:e.code||"",message:e.message,type:e.type||"general"};try{const t=await e.json();return{code:t.code||"",message:t.message,type:t.type||"api"}}catch(e){return{message:e.message,type:"general"}}},N=(0,f.createHigherOrderComponent)((e=>class extends i.Component{state={error:null,loading:!1,product:"preview"===this.props.attributes.productId?this.props.attributes.previewProduct:null};componentDidMount(){this.loadProduct()}componentDidUpdate(e){e.attributes.productId!==this.props.attributes.productId&&this.loadProduct()}loadProduct=()=>{const{productId:e}=this.props.attributes;"preview"!==e&&(e?(this.setState({loading:!0}),(e=>y()({path:`/wc/store/v1/products/${e}`}))(e).then((e=>{this.setState({product:e,loading:!1,error:null})})).catch((async e=>{const t=await E(e);this.setState({product:null,loading:!1,error:t})}))):this.setState({product:null,loading:!1,error:null}))};render(){const{error:t,loading:o,product:r}=this.state;return(0,p.jsx)(e,{...this.props,error:t,getProduct:this.loadProduct,isLoading:o,product:r})}}),"withProduct"),C=({imageUrl:e=`${a.sW}/block-error.svg`,header:t=(0,v.__)("Oops!","woocommerce"),text:o=(0,v.__)("There was an error loading the content.","woocommerce"),errorMessage:r,errorMessagePrefix:s=(0,v.__)("Error:","woocommerce"),button:c,showErrorBlock:n=!0})=>n?(0,p.jsxs)("div",{className:"wc-block-error wc-block-components-error",children:[e&&(0,p.jsx)("img",{className:"wc-block-error__image wc-block-components-error__image",src:e,alt:""}),(0,p.jsxs)("div",{className:"wc-block-error__content wc-block-components-error__content",children:[t&&(0,p.jsx)("p",{className:"wc-block-error__header wc-block-components-error__header",children:t}),o&&(0,p.jsx)("p",{className:"wc-block-error__text wc-block-components-error__text",children:o}),r&&(0,p.jsxs)("p",{className:"wc-block-error__message wc-block-components-error__message",children:[s?s+" ":"",r]}),c&&(0,p.jsx)("p",{className:"wc-block-error__button wc-block-components-error__button",children:c})]})]}):null;o(5893);class I extends i.Component{state={errorMessage:"",hasError:!1};static getDerivedStateFromError(e){return void 0!==e.statusText&&void 0!==e.status?{errorMessage:(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("strong",{children:e.status}),": ",e.statusText]}),hasError:!0}:{errorMessage:e.message,hasError:!0}}render(){const{header:e,imageUrl:t,showErrorMessage:o=!0,showErrorBlock:r=!0,text:s,errorMessagePrefix:c,renderError:n,button:i}=this.props,{errorMessage:a,hasError:l}=this.state;return l?"function"==typeof n?n({errorMessage:a}):(0,p.jsx)(C,{showErrorBlock:r,errorMessage:o?a:null,header:e,imageUrl:t,text:s,errorMessagePrefix:c,button:i}):this.props.children}}const A=I;var B=o(7035),O=o(4715),T=o(415);const L=e=>{const t=((0,T.useProductDataContext)().product||{}).id||e.productId||0;return t&&1!==t?(0,p.jsx)(O.InspectorControls,{children:(0,p.jsxs)("div",{className:"wc-block-single-product__edit-card",children:[(0,p.jsx)("div",{className:"wc-block-single-product__edit-card-title",children:(0,p.jsxs)("a",{href:`${S.ADMIN_URL}post.php?post=${t}&action=edit`,target:"_blank",rel:"noopener noreferrer",children:[(0,v.__)("Edit this product's details","woocommerce"),(0,p.jsx)(s.A,{icon:B.A,size:16})]})}),(0,p.jsx)("div",{className:"wc-block-single-product__edit-card-description",children:(0,v.__)("Edit details such as title, price, description and more.","woocommerce")})]})}):null};var R=o(2098),M=o(4921);const $=window.wp.escapeHtml,F=({message:e,type:t})=>e?"general"===t?(0,p.jsxs)("span",{children:[(0,v.__)("The following error was returned","woocommerce"),(0,p.jsx)("br",{}),(0,p.jsx)("code",{children:(0,$.escapeHTML)(e)})]}):"api"===t?(0,p.jsxs)("span",{children:[(0,v.__)("The following error was returned from the API","woocommerce"),(0,p.jsx)("br",{}),(0,p.jsx)("code",{children:(0,$.escapeHTML)(e)})]}):e:(0,v.__)("An error has prevented the block from being updated.","woocommerce"),D=({error:e})=>(0,p.jsx)("div",{className:"wc-block-error-message",children:F(e)});o(3120);const z=({className:e="",error:t,isLoading:o=!1,onRetry:r})=>(0,p.jsxs)(d.Placeholder,{icon:(0,p.jsx)(s.A,{icon:R.A}),label:(0,v.__)("Sorry, an error occurred","woocommerce"),className:(0,M.A)("wc-block-api-error",e),children:[(0,p.jsx)(D,{error:t}),r&&(0,p.jsx)(p.Fragment,{children:o?(0,p.jsx)(d.Spinner,{}):(0,p.jsx)(d.Button,{variant:"secondary",onClick:r,children:(0,v.__)("Retry","woocommerce")})})]}),V=window.wc.data;var G=o(7143),H=o(2624),U=(o(7539),o(3993));function W(e,t,o){const r=new Set(t.map((e=>e[o])));return e.filter((e=>!r.has(e[o])))}var q=o(8537);const J={clear:(0,v.__)("Clear all selected items","woocommerce"),noItems:(0,v.__)("No items found.","woocommerce"),
/* Translators: %s search term */
noResults:(0,v.__)("No results for %s","woocommerce"),search:(0,v.__)("Search for items","woocommerce"),selected:e=>(0,v.sprintf)(/* translators: Number of items selected from list. */ /* translators: Number of items selected from list. */
(0,v._n)("%d item selected","%d items selected",e,"woocommerce"),e),updated:(0,v.__)("Search results updated.","woocommerce")},K=(e,t=e)=>{const o=e.reduce(((e,t)=>{const o=t.parent||0;return e[o]||(e[o]=[]),e[o].push(t),e}),{}),r=t.reduce(((e,t)=>(e[String(t.id)]=t,e)),{});const s=["0"],c=(e={})=>e.parent?[...c(r[e.parent]),e.name]:e.name?[e.name]:[],n=e=>e.map((e=>{const t=o[e.id];return s.push(""+e.id),{...e,breadcrumbs:c(r[e.parent]),children:t&&t.length?n(t):[]}})),i=n(o[0]||[]);return Object.entries(o).forEach((([e,t])=>{s.includes(e)||i.push(...n(t||[]))})),i},X=(e,t)=>{if(!t)return e;const o=new RegExp(`(${t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")})`,"ig");return e.split(o).map(((e,t)=>o.test(e)?(0,p.jsx)("strong",{children:e},t):(0,p.jsx)(i.Fragment,{children:e},t)))},Q=({label:e})=>(0,p.jsx)("span",{className:"woocommerce-search-list__item-count",children:e}),Y=e=>{const{item:t,search:o}=e,r=t.breadcrumbs&&t.breadcrumbs.length;return(0,p.jsxs)("span",{className:"woocommerce-search-list__item-label",children:[r?(0,p.jsx)("span",{className:"woocommerce-search-list__item-prefix",children:(s=t.breadcrumbs,1===s.length?s.slice(0,1).toString():2===s.length?s.slice(0,1).toString()+" › "+s.slice(-1).toString():s.slice(0,1).toString()+" … "+s.slice(-1).toString())}):null,(0,p.jsx)("span",{className:"woocommerce-search-list__item-name",children:X((0,q.decodeEntities)(t.name),o)})]});var s},Z=({countLabel:e,className:t,depth:o=0,controlId:r="",item:s,isSelected:c,isSingle:n,onSelect:a,search:l="",selected:m,useExpandedPanelId:u,...h})=>{const[g,w]=u,b=null!=e&&void 0!==s.count&&null!==s.count,x=!!s.breadcrumbs?.length,_=!!s.children?.length,v=g===s.id,f=(0,M.A)(["woocommerce-search-list__item",`depth-${o}`,t],{"has-breadcrumbs":x,"has-children":_,"has-count":b,"is-expanded":v,"is-radio-button":n});(0,i.useEffect)((()=>{_&&c&&w(s.id)}),[s,_,c,w]);const k=h.name||`search-list-item-${r}`,j=`${k}-${s.id}`,y=(0,i.useCallback)((()=>{w(v?-1:Number(s.id))}),[v,s.id,w]);return _?(0,p.jsx)("div",{className:f,onClick:y,onKeyDown:e=>"Enter"===e.key||" "===e.key?y():null,role:"treeitem",tabIndex:0,children:n?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("input",{type:"radio",id:j,name:k,value:s.value,onChange:a(s),onClick:e=>e.stopPropagation(),checked:c,className:"woocommerce-search-list__item-input",...h}),(0,p.jsx)(Y,{item:s,search:l}),b?(0,p.jsx)(Q,{label:e||s.count}):null]}):(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(d.CheckboxControl,{className:"woocommerce-search-list__item-input",checked:c,...!c&&s.children.some((e=>m.find((t=>t.id===e.id))))?{indeterminate:!0}:{},label:X((0,q.decodeEntities)(s.name),l),onChange:()=>{c?a(W(m,s.children,"id"))():a(function(e,t){const o=W(t,e,"id");return[...e,...o]}(m,s.children))()},onClick:e=>e.stopPropagation()}),b?(0,p.jsx)(Q,{label:e||s.count}):null]})}):(0,p.jsxs)("label",{htmlFor:j,className:f,children:[n?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("input",{...h,type:"radio",id:j,name:k,value:s.value,onChange:a(s),checked:c,className:"woocommerce-search-list__item-input"}),(0,p.jsx)(Y,{item:s,search:l})]}):(0,p.jsx)(d.CheckboxControl,{...h,id:j,name:k,className:"woocommerce-search-list__item-input",value:(0,q.decodeEntities)(s.value),label:X((0,q.decodeEntities)(s.name),l),onChange:a(s),checked:c}),b?(0,p.jsx)(Q,{label:e||s.count}):null]})},ee=Z;var te=o(3028);o(5022);const oe=({id:e,label:t,popoverContents:o,remove:r,screenReaderLabel:c,className:n=""})=>{const[a,l]=(0,i.useState)(!1),m=(0,f.useInstanceId)(oe);if(c=c||t,!t)return null;t=(0,q.decodeEntities)(t);const u=(0,M.A)("woocommerce-tag",n,{"has-remove":!!r}),h=`woocommerce-tag__label-${m}`,g=(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("span",{className:"screen-reader-text",children:c}),(0,p.jsx)("span",{"aria-hidden":"true",children:t})]});return(0,p.jsxs)("span",{className:u,children:[o?(0,p.jsx)(d.Button,{className:"woocommerce-tag__text",id:h,onClick:()=>l(!0),children:g}):(0,p.jsx)("span",{className:"woocommerce-tag__text",id:h,children:g}),o&&a&&(0,p.jsx)(d.Popover,{onClose:()=>l(!1),children:o}),r&&(0,p.jsx)(d.Button,{className:"woocommerce-tag__remove",onClick:r(e),label:(0,v.sprintf)(
// Translators: %s label.
// Translators: %s label.
(0,v.__)("Remove %s","woocommerce"),t),"aria-describedby":h,children:(0,p.jsx)(s.A,{icon:te.A,size:20,className:"clear-icon",role:"img"})})]})},re=oe;o(1939);const se=e=>(0,p.jsx)(ee,{...e}),ce=e=>{const{list:t,selected:o,renderItem:r,depth:s=0,onSelect:c,instanceId:n,isSingle:a,search:l,useExpandedPanelId:d}=e,[m]=d;return t?(0,p.jsx)(p.Fragment,{children:t.map((t=>{const u=t.children?.length&&!a?t.children.every((({id:e})=>o.find((t=>t.id===e)))):!!o.find((({id:e})=>e===t.id)),h=t.children?.length&&m===t.id;return(0,p.jsxs)(i.Fragment,{children:[(0,p.jsx)("li",{children:r({item:t,isSelected:u,onSelect:c,isSingle:a,selected:o,search:l,depth:s,useExpandedPanelId:d,controlId:n})}),h?(0,p.jsx)(ce,{...e,list:t.children,depth:s+1}):null]},t.id)}))}):null},ne=({isLoading:e,isSingle:t,selected:o,messages:r,onChange:s,onRemove:c})=>{if(e||t||!o)return null;const n=o.length;return(0,p.jsxs)("div",{className:"woocommerce-search-list__selected",children:[(0,p.jsxs)("div",{className:"woocommerce-search-list__selected-header",children:[(0,p.jsx)("strong",{children:r.selected(n)}),n>0?(0,p.jsx)(d.Button,{variant:"link",isDestructive:!0,onClick:()=>s([]),"aria-label":r.clear,children:(0,v.__)("Clear all","woocommerce")}):null]}),n>0?(0,p.jsx)("ul",{children:o.map(((e,t)=>(0,p.jsx)("li",{children:(0,p.jsx)(re,{label:e.name,id:e.id,remove:c})},t)))}):null]})},ie=({filteredList:e,search:t,onSelect:o,instanceId:r,useExpandedPanelId:c,...n})=>{const{messages:i,renderItem:a,selected:l,isSingle:d}=n,m=a||se;return 0===e.length?(0,p.jsxs)("div",{className:"woocommerce-search-list__list is-not-found",children:[(0,p.jsx)("span",{className:"woocommerce-search-list__not-found-icon",children:(0,p.jsx)(s.A,{icon:H.A,role:"img"})}),(0,p.jsx)("span",{className:"woocommerce-search-list__not-found-text",children:t?(0,v.sprintf)(i.noResults,t):i.noItems})]}):(0,p.jsx)("ul",{className:"woocommerce-search-list__list",children:(0,p.jsx)(ce,{useExpandedPanelId:c,list:e,selected:l,renderItem:m,onSelect:o,instanceId:r,isSingle:d,search:t})})},ae=e=>{const{className:t="",isCompact:o,isHierarchical:r,isLoading:s,isSingle:c,list:n,messages:a=J,onChange:l,onSearch:m,selected:u,type:h="text",debouncedSpeak:g}=e,[w,b]=(0,i.useState)(""),x=(0,i.useState)(-1),_=(0,f.useInstanceId)(ae),k=(0,i.useMemo)((()=>({...J,...a})),[a]),j=(0,i.useMemo)((()=>((e,t,o)=>{if(!t)return o?K(e):e;const r=new RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"i"),s=e.map((e=>!!r.test(e.name)&&e)).filter(Boolean);return o?K(s,e):s})(n,w,r)),[n,w,r]);(0,i.useEffect)((()=>{g&&g(k.updated)}),[g,k]),(0,i.useEffect)((()=>{"function"==typeof m&&m(w)}),[w,m]);const y=(0,i.useCallback)((e=>()=>{c&&l([]);const t=u.findIndex((({id:t})=>t===e));l([...u.slice(0,t),...u.slice(t+1)])}),[c,u,l]),S=(0,i.useCallback)((e=>()=>{Array.isArray(e)?l(e):-1===u.findIndex((({id:t})=>t===e.id))?l(c?[e]:[...u,e]):y(e.id)()}),[c,y,l,u]),P=(0,i.useCallback)((e=>{const[t]=u.filter((t=>!e.find((e=>t.id===e.id))));y(t.id)()}),[y,u]);return(0,p.jsxs)("div",{className:(0,M.A)("woocommerce-search-list",t,{"is-compact":o,"is-loading":s,"is-token":"token"===h}),children:["text"===h&&(0,p.jsx)(ne,{...e,onRemove:y,messages:k}),(0,p.jsx)("div",{className:"woocommerce-search-list__search",children:"text"===h?(0,p.jsx)(d.TextControl,{label:k.search,type:"search",value:w,onChange:e=>b(e)}):(0,p.jsx)(d.FormTokenField,{disabled:s,label:k.search,onChange:P,onInputChange:e=>b(e),suggestions:[],__experimentalValidateInput:()=>!1,value:s?[(0,v.__)("Loading…","woocommerce")]:u.map((e=>({...e,value:e.name}))),__experimentalShowHowTo:!1})}),s?(0,p.jsx)("div",{className:"woocommerce-search-list__list",children:(0,p.jsx)(d.Spinner,{})}):(0,p.jsx)(ie,{...e,search:w,filteredList:j,messages:k,onSelect:S,instanceId:_,useExpandedPanelId:x})]})},le=((0,d.withSpokenMessages)(ae),e=>t=>{let{selected:o}=t;o=void 0===o?null:o;const r=null===o;return Array.isArray(o)?(0,p.jsx)(e,{...t}):(0,p.jsx)(e,{...t,selected:r?[]:[o]})});var de=o(4347);var me=o(923),ue=o.n(me);const pe=(0,f.createHigherOrderComponent)((e=>{class t extends i.Component{state={error:null,loading:!1,variations:{}};componentDidMount(){const{selected:e,showVariations:t}=this.props;e&&t&&this.loadVariations()}componentDidUpdate(e){const{isLoading:t,selected:o,showVariations:r}=this.props;r&&(!ue()(e.selected,o)||e.isLoading&&!t)&&this.loadVariations()}loadVariations=()=>{const{products:e}=this.props,{loading:t,variations:o}=this.state;if(t)return;const r=this.getExpandedProduct();if(!r||o[r])return;const s=e.find((e=>e.id===r));var c;s?.variations&&0!==s.variations.length?(this.setState({loading:!0}),(c=r,y()({path:(0,k.addQueryArgs)("wc/store/v1/products",{per_page:0,type:"variation",parent:c})})).then((e=>{const t=e.map((e=>({...e,parent:r})));this.setState({variations:{...this.state.variations,[r]:t},loading:!1,error:null})})).catch((async e=>{const t=await E(e);this.setState({variations:{...this.state.variations,[r]:null},loading:!1,error:t})}))):this.setState({variations:{...this.state.variations,[r]:null},loading:!1,error:null})};isProductId(e){const{products:t}=this.props;return t.some((t=>t.id===e))}findParentProduct(e){const{products:t}=this.props,o=t.filter((t=>t.variations&&t.variations.find((({id:t})=>t===e))));return o[0]?.id}getExpandedProduct(){const{isLoading:e,selected:t,showVariations:o}=this.props;if(!o)return null;let r=t&&t.length?t[0]:null;return r?this.prevSelectedItem=r:!this.prevSelectedItem||e||this.isProductId(this.prevSelectedItem)||(r=this.prevSelectedItem),!e&&r?this.isProductId(r)?r:this.findParentProduct(r):null}render(){const{error:t,isLoading:o}=this.props,{error:r,loading:s,variations:c}=this.state;return(0,p.jsx)(e,{...this.props,error:r||t,expandedProduct:this.getExpandedProduct(),isLoading:o,variations:c,variationsLoading:s})}}return t}),"withProductVariations"),he=e=>{const{id:t,name:o,parent:r}=e;return{id:t,name:o,parent:r,breadcrumbs:[],children:[],details:e,value:e.slug}};var ge=o(1609);const we=({className:e,item:t,isSelected:o,isLoading:r,onSelect:s,disabled:c,...n})=>(0,p.jsxs)(p.Fragment,{children:[(0,ge.createElement)(Z,{...n,key:t.id,className:e,isSelected:o,item:t,onSelect:s,disabled:c}),o&&r&&(0,p.jsx)("div",{className:(0,M.A)("woocommerce-search-list__item","woocommerce-product-attributes__item","depth-1","is-loading","is-not-active"),children:(0,p.jsx)(d.Spinner,{})},"loading")]});o(5653);const be={list:(0,v.__)("Products","woocommerce"),noItems:(0,v.__)("Your store doesn't have any products.","woocommerce"),search:(0,v.__)("Search for a product to display","woocommerce"),updated:(0,v.__)("Product search results updated.","woocommerce")},xe=le((fe=pe((0,f.withInstanceId)((e=>{const{expandedProduct:t=null,error:o,instanceId:r,isCompact:s=!1,isLoading:c,onChange:n,onSearch:i,products:a,renderItem:l,selected:d=[],showVariations:m=!1,variations:u,variationsLoading:h}=e;if(o)return(0,p.jsx)(D,{error:o});const g=[...a,...u&&t&&u[t]?u[t]:[]].map(he);return(0,p.jsx)(ae,{className:"woocommerce-products",list:g,isCompact:s,isLoading:c,isSingle:!0,selected:g.filter((({id:e})=>d.includes(Number(e)))),onChange:n,renderItem:l||(m?e=>{const{item:t,search:o,depth:s=0,isSelected:n,onSelect:i}=e,a=t.details?.variations&&Array.isArray(t.details.variations)?t.details.variations.length:0,l=(0,M.A)("woocommerce-search-product__item","woocommerce-search-list__item",`depth-${s}`,"has-count",{"is-searching":o.length>0,"is-skip-level":0===s&&0!==t.parent,"is-variable":a>0});if(!t.breadcrumbs.length){const o=t.details?.variations&&t.details.variations.length>0;return(0,p.jsx)(we,{...e,className:(0,M.A)(l,{"is-selected":n}),isSelected:n,item:t,onSelect:()=>()=>{i(t)()},isLoading:c||h,countLabel:o?(0,v.sprintf)(/* translators: %1$d is the number of variations of a product product. */ /* translators: %1$d is the number of variations of a product product. */
(0,v.__)("%1$d variations","woocommerce"),t.details?.variations.length):null,name:`products-${r}`,"aria-label":o?(0,v.sprintf)(/* translators: %1$s is the product name, %2$d is the number of variations of that product. */ /* translators: %1$s is the product name, %2$d is the number of variations of that product. */
(0,v._n)("%1$s, has %2$d variation","%1$s, has %2$d variations",t.details?.variations?.length,"woocommerce"),t.name,t.details?.variations.length):void 0})}const d=(0,U.isEmpty)(t.details?.variation)?e:{...e,item:{...e.item,name:t.details?.variation},"aria-label":`${t.breadcrumbs[0]}: ${t.details?.variation}`};return(0,p.jsx)(Z,{...d,className:l,name:`variations-${r}`})}:void 0),onSearch:i,messages:{...be,...e.messages},isHierarchical:!0})}))),({selected:e,...t})=>{const[o,r]=(0,i.useState)(!0),[s,c]=(0,i.useState)(null),[n,l]=(0,i.useState)([]),d=a.r7.productCount>100,m=async e=>{const t=await E(e);c(t),r(!1)},u=(0,i.useRef)(e);(0,i.useEffect)((()=>{P({selected:u.current}).then((e=>{l(e),r(!1)})).catch(m)}),[u]);const h=(0,de.YQ)((t=>{P({selected:e,search:t}).then((e=>{l(e),r(!1)})).catch(m)}),400),g=(0,i.useCallback)((e=>{r(!0),h(e)}),[r,h]);return(0,p.jsx)(fe,{...t,selected:e,error:s,products:n,isLoading:o,onSearch:d?g:null})})),_e=({attributes:e,setAttributes:t,onChange:o})=>(0,p.jsx)(xe,{selected:e.productId||0,showVariations:!0,onChange:(e=[])=>{const r=e[0]?e[0].id:0;t({productId:r}),o&&o()}}),ve=({isEditing:e,setIsEditing:t})=>(0,p.jsx)(O.BlockControls,{children:(0,p.jsx)(d.ToolbarGroup,{controls:[{icon:"edit",title:(0,v.__)("Edit selected product","woocommerce"),onClick:()=>t(!e),isActive:e}]})});var fe,ke=o(1465);const je=({isLoading:e,product:t,clientId:o})=>{const s="wc-block-editor-single-product",{replaceInnerBlocks:c}=(0,G.useDispatch)("core/block-editor"),n=(0,i.useCallback)((()=>{c(o,(0,r.createBlocksFromInnerBlocksTemplate)(b),!1)}),[o,c]);return(0,p.jsx)(T.InnerBlockLayoutContextProvider,{parentName:l.name,parentClassName:s,children:(0,p.jsxs)(T.ProductDataContextProvider,{product:t,isLoading:e,children:[(0,p.jsx)(O.InspectorControls,{children:(0,p.jsx)(d.PanelBody,{title:(0,v.__)("Layout","woocommerce"),initialOpen:!0,children:(0,p.jsx)(d.Button,{label:(0,v.__)("Reset layout to default","woocommerce"),onClick:n,variant:"tertiary",className:"wc-block-editor-single-product__reset-layout",icon:ke.A,children:(0,v.__)("Reset layout","woocommerce")})})}),(0,p.jsx)("div",{className:s,children:(0,p.jsx)(O.BlockContextProvider,{value:{postId:t?.id,postType:"product"},children:(0,p.jsx)(O.InnerBlocks,{template:b,allowedBlocks:x,templateLock:!1})})})]})})},ye=N((({attributes:e,setAttributes:t,error:o,getProduct:r,product:c,isLoading:n,clientId:a})=>{const{productId:m,isPreview:u}=e,[h,g]=(0,i.useState)(!m),b=(0,O.useBlockProps)(),x=(0,G.useSelect)((e=>e("core/blocks").getBlockType(l.name)),[]),_=(0,G.useSelect)((e=>u?e(V.PRODUCTS_STORE_NAME).getProducts({per_page:1}):null)),f="object"==typeof o&&"woocommerce_rest_product_invalid_id"===o?.code;if((0,i.useEffect)((()=>{const o=_?_[0]?.id:null;o&&!m&&(t({...e,productId:o}),g(!1))}),[e,m,_,t]),(0,i.useEffect)((()=>{f&&!h&&g(!0)}),[f]),o&&!f)return(0,p.jsx)(z,{className:"wc-block-editor-single-product-error",error:o,isLoading:n,onRetry:r});const k=f?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(s.A,{icon:H.A,className:"wc-block-editor-single-product__info-icon"}),(0,p.jsx)(d.__experimentalText,{children:(0,v.__)("Previously selected product is no longer available.","woocommerce")})]}):(0,p.jsx)(d.__experimentalText,{children:x.description}),j=f?()=>g(!1):void 0;return(0,p.jsx)("div",{...b,children:(0,p.jsxs)(A,{header:(0,v.__)("Single Product Block Error","woocommerce"),children:[(0,p.jsx)(ve,{setIsEditing:g,isEditing:h}),h?(0,p.jsxs)(d.Placeholder,{icon:w,label:x.title,className:"wc-block-editor-single-product",children:[(0,p.jsxs)(d.__experimentalHStack,{alignment:"center",children:[" ",k," "]}),(0,p.jsxs)("div",{className:"wc-block-editor-single-product__selection",children:[(0,p.jsx)(_e,{attributes:e,setAttributes:t,onChange:j}),!f&&(0,p.jsx)(d.Button,{variant:"secondary",onClick:()=>{g(!1)},children:(0,v.__)("Done","woocommerce")})]})]}):(0,p.jsxs)("div",{children:[(0,p.jsx)(O.InspectorControls,{children:(0,p.jsx)(d.PanelBody,{title:(0,v.__)("Product","woocommerce"),initialOpen:!1,children:(0,p.jsx)(_e,{attributes:e,setAttributes:t})})}),(0,p.jsx)(L,{productId:m}),(0,p.jsx)(je,{clientId:a,product:c,isLoading:n})]})]})})})),Se=[{attributes:l.attributes,supports:l.supports,save:()=>{const e=O.useBlockProps.save();return(0,p.jsx)("div",{...e,children:(0,p.jsx)(O.InnerBlocks.Content,{})})}}];(0,r.registerBlockType)(l,{icon:w,edit:ye,save:()=>{const e=O.useBlockProps.save({className:"woocommerce"});return(0,p.jsx)("div",{...e,children:(0,p.jsx)(O.InnerBlocks.Content,{})})},deprecated:Se})},6070:(e,t,o)=>{"use strict";o.d(t,{Hw:()=>p,Vo:()=>i,XK:()=>n,iI:()=>m,r7:()=>s,sW:()=>c});var r=o(5703);const s=(0,r.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),c=s.pluginUrl+"assets/images/",n=s.pluginUrl+"assets/client/blocks/",i=(r.STORE_PAGES.shop,r.STORE_PAGES.checkout,r.STORE_PAGES.checkout,r.STORE_PAGES.privacy,r.STORE_PAGES.privacy,r.STORE_PAGES.terms,r.STORE_PAGES.terms,r.STORE_PAGES.cart,r.STORE_PAGES.cart?.permalink),a=(r.STORE_PAGES.myaccount?.permalink?r.STORE_PAGES.myaccount.permalink:(0,r.getSetting)("wpLoginUrl","/wp-login.php"),(0,r.getSetting)("localPickupEnabled",!1),(0,r.getSetting)("shippingMethodsExist",!1),(0,r.getSetting)("shippingEnabled",!0),(0,r.getSetting)("countries",{})),l=(0,r.getSetting)("countryData",{}),d={...Object.fromEntries(Object.keys(l).filter((e=>!0===l[e].allowBilling)).map((e=>[e,a[e]||""]))),...Object.fromEntries(Object.keys(l).filter((e=>!0===l[e].allowShipping)).map((e=>[e,a[e]||""])))},m=(Object.fromEntries(Object.keys(d).map((e=>[e,l[e].states||{}]))),Object.fromEntries(Object.keys(d).map((e=>[e,l[e].locale||{}])))),u={address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]},p=(0,r.getSetting)("addressFieldsLocations",u).address;(0,r.getSetting)("addressFieldsLocations",u).contact,(0,r.getSetting)("addressFieldsLocations",u).order,(0,r.getSetting)("additionalOrderFields",{}),(0,r.getSetting)("additionalContactFields",{}),(0,r.getSetting)("additionalAddressFields",{})},8331:(e,t,o)=>{"use strict";o.d(t,{Hw:()=>r.Hw,Vo:()=>r.Vo,XK:()=>r.XK,iI:()=>r.iI,r7:()=>r.r7,sW:()=>r.sW});var r=o(6070)},5893:()=>{},7539:()=>{},3120:()=>{},5653:()=>{},1939:()=>{},5022:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},4656:e=>{"use strict";e.exports=window.wc.blocksComponents},910:e=>{"use strict";e.exports=window.wc.priceFormat},7594:e=>{"use strict";e.exports=window.wc.wcBlocksData},415:e=>{"use strict";e.exports=window.wc.wcBlocksSharedContext},1616:e=>{"use strict";e.exports=window.wc.wcBlocksSharedHocs},5703:e=>{"use strict";e.exports=window.wc.wcSettings},3993:e=>{"use strict";e.exports=window.wc.wcTypes},6004:e=>{"use strict";e.exports=window.wp.autop},4715:e=>{"use strict";e.exports=window.wp.blockEditor},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},6087:e=>{"use strict";e.exports=window.wp.element},2619:e=>{"use strict";e.exports=window.wp.hooks},8537:e=>{"use strict";e.exports=window.wp.htmlEntities},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},5573:e=>{"use strict";e.exports=window.wp.primitives},9786:e=>{"use strict";e.exports=window.wp.styleEngine},9446:e=>{"use strict";e.exports=window.wp.wordcount},3925:e=>{"use strict";e.exports=JSON.parse('{"title":"Product Title","description":"Display the title of a product.","attributes":{"headingLevel":{"type":"number","default":2},"showProductLink":{"type":"boolean","default":true},"linkTarget":{"type":"string"},"productId":{"type":"number","default":0},"align":{"type":"string"}}}')}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}};return c[e].call(o.exports,o,o.exports,i),o.exports}i.m=c,e=[],i.O=(t,o,r,s)=>{if(!o){var c=1/0;for(d=0;d<e.length;d++){for(var[o,r,s]=e[d],n=!0,a=0;a<o.length;a++)(!1&s||c>=s)&&Object.keys(i.O).every((e=>i.O[e](o[a])))?o.splice(a--,1):(n=!1,s<c&&(c=s));if(n){e.splice(d--,1);var l=r();void 0!==l&&(t=l)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[o,r,s]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var s=Object.create(null);i.r(s);var c={};t=t||[null,o({}),o([]),o(o)];for(var n=2&r&&e;"object"==typeof n&&!~t.indexOf(n);n=o(n))Object.getOwnPropertyNames(n).forEach((t=>c[t]=()=>e[t]));return c.default=()=>e,i.d(s,c),s},i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,o)=>(i.f[o](e,t),t)),[])),i.u=e=>({345:"product-stock-indicator",462:"product-rating",2105:"product-title",2388:"product-price",2724:"product-sku",3895:"product-summary",4232:"product-image",4442:"product-sale-badge",7409:"product-button",8553:"product-rating-counter",8578:"product-rating-stars",8647:"product-average-rating"}[e]+".js?ver="+{345:"df5124dfa21ae95f6b99",462:"dbe142844072de177c7b",2105:"d92c3a074be89ba952d1",2388:"d74f2069624dae07b4c2",2724:"68c6a5abd5b457353eed",3895:"58e7ccf88f3a6b42fadc",4232:"c066235ed1ae7a69c1b2",4442:"e8a10a172a750d04f107",7409:"04cf4f54485de393ee40",8553:"50f738006badde9aa053",8578:"a949306fc89e03db24ad",8647:"7c7c74fa303f8fca8df1"}[e]),i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r={},s="webpackWcBlocksMainJsonp:",i.l=(e,t,o,c)=>{if(r[e])r[e].push(t);else{var n,a;if(void 0!==o)for(var l=document.getElementsByTagName("script"),d=0;d<l.length;d++){var m=l[d];if(m.getAttribute("src")==e||m.getAttribute("data-webpack")==s+o){n=m;break}}n||(a=!0,(n=document.createElement("script")).charset="utf-8",n.timeout=120,i.nc&&n.setAttribute("nonce",i.nc),n.setAttribute("data-webpack",s+o),n.src=e),r[e]=[t];var u=(t,o)=>{n.onerror=n.onload=null,clearTimeout(p);var s=r[e];if(delete r[e],n.parentNode&&n.parentNode.removeChild(n),s&&s.forEach((e=>e(o))),t)return t(o)},p=setTimeout(u.bind(null,void 0,{type:"timeout",target:n}),12e4);n.onerror=u.bind(null,n.onerror),n.onload=u.bind(null,n.onload),a&&document.head.appendChild(n)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=5065,(()=>{var e;i.g.importScripts&&(e=i.g.location+"");var t=i.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var o=t.getElementsByTagName("script");if(o.length)for(var r=o.length-1;r>-1&&(!e||!/^http(s?):/.test(e));)e=o[r--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=e})(),(()=>{var e={5065:0};i.f.j=(t,o)=>{var r=i.o(e,t)?e[t]:void 0;if(0!==r)if(r)o.push(r[2]);else{var s=new Promise(((o,s)=>r=e[t]=[o,s]));o.push(r[2]=s);var c=i.p+i.u(t),n=new Error;i.l(c,(o=>{if(i.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var s=o&&("load"===o.type?"missing":o.type),c=o&&o.target&&o.target.src;n.message="Loading chunk "+t+" failed.\n("+s+": "+c+")",n.name="ChunkLoadError",n.type=s,n.request=c,r[1](n)}}),"chunk-"+t,t)}},i.O.j=t=>0===e[t];var t=(t,o)=>{var r,s,[c,n,a]=o,l=0;if(c.some((t=>0!==e[t]))){for(r in n)i.o(n,r)&&(i.m[r]=n[r]);if(a)var d=a(i)}for(t&&t(o);l<c.length;l++)s=c[l],i.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return i.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var a=i.O(void 0,[94],(()=>i(8723)));a=i.O(a),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["single-product"]=a})();