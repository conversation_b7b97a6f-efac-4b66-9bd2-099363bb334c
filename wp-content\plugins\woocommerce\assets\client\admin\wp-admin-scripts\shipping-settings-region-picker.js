/*! For license information please see shipping-settings-region-picker.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,o,t)=>{var n=t(51609),r=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),l=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};o.jsx=function(e,o,t){var n,a={},c=null,p=null;for(n in void 0!==t&&(c=""+t),void 0!==o.key&&(c=""+o.key),void 0!==o.ref&&(p=o.ref),o)i.call(o,n)&&!s.hasOwnProperty(n)&&(a[n]=o[n]);if(e&&e.defaultProps)for(n in o=e.defaultProps)void 0===a[n]&&(a[n]=o[n]);return{$$typeof:r,type:e,key:c,ref:p,props:a,_owner:l.current}}},39793:(e,o,t)=>{e.exports=t(94931)},51609:e=>{e.exports=window.React}},o={};const t=window.wp.element,n=window.wp.htmlEntities,r=window.wc.components,i=window.wp.i18n;var l=function t(n){var r=o[n];if(void 0!==r)return r.exports;var i=o[n]={exports:{}};return e[n](i,i.exports,t),i.exports}(39793);const s="__WC_TREE_SELECT_COMPONENT_ROOT__",a=({options:e,initialValues:o})=>{const[n,a]=(0,t.useState)(o.length?o:[s]);return(0,l.jsx)(r.TreeSelectControl,{value:n,onChange:e=>{e=0===e.length||e[e.length-1]===s?[s]:e.filter((e=>e!==s)),document.body.dispatchEvent(new CustomEvent("wc_region_picker_update",{detail:e})),a(e)},options:e,placeholder:(0,i.__)("Start typing to filter zones","woocommerce"),alwaysShowPlaceholder:!0,selectAllLabel:(0,i.__)("Everywhere","woocommerce"),individuallySelectParent:!0,maxVisibleTags:5})},c=(e,o)=>Array.isArray(e)?e.map((e=>c(e,o))):(e.label&&(e.label=o(e.label)),e.children&&(e.children=c(e.children,o)),e);var p,d;const w=document.getElementById("wc-shipping-zone-region-picker-root"),_=null!==(p=c(window.shippingZoneMethodsLocalizeScript?.region_options,n.decodeEntities))&&void 0!==p?p:[],u=null!==(d=window.shippingZoneMethodsLocalizeScript?.locations)&&void 0!==d?d:[],h=()=>(0,l.jsx)("div",{children:(0,l.jsx)(a,{options:_,initialValues:u})});w&&(0,t.createRoot)(w).render((0,l.jsx)(h,{})),(window.wc=window.wc||{}).shippingSettingsRegionPicker={}})();