"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[7210],{12974:(e,s,t)=>{t.d(s,{Ay:()=>o});var i=t(13240);const a=["a","b","em","i","strong","p","br"],n=["target","href","rel","name","download"],o=e=>({__html:(0,i.sanitize)(e,{ALLOWED_TAGS:a,ALLOWED_ATTR:n})})},69222:(e,s,t)=>{t.r(s),t.d(s,{SettingsPaymentsOffline:()=>w,default:()=>h});var i=t(47143),a=t(86087),n=t(40314),o=t(51881),c=t(18537),m=t(12974),l=t(15698),r=t(75753),d=t(39793);const g=({gateway:e,...s})=>(0,d.jsx)(l.Uq,{id:e.id,className:"woocommerce-list__item woocommerce-list__item-enter-done"+(s.className?` ${s.className}`:""),...s,children:(0,d.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,d.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,d.jsx)(l.Gh,{}),e.icon&&(0,d.jsx)("img",{className:"woocommerce-list__item-image",src:e.icon,alt:e.title+" logo"})]}),(0,d.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,d.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,d.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,m.Ay)((0,c.decodeEntities)(e.description))})]}),(0,d.jsx)("div",{className:"woocommerce-list__item-after",children:(0,d.jsx)("div",{className:"woocommerce-list__item-after__actions",children:e.state.enabled?(0,d.jsx)(r.CS,{gatewayProvider:e,settingsHref:e.management._links.settings.href,isInstallingPlugin:!1}):(0,d.jsx)(r.S,{installingPlugin:null,gatewayProvider:e,settingsHref:e.management._links.settings.href,onboardingHref:e.onboarding._links.onboard.href,isOffline:!0,gatewayHasRecommendedPaymentMethods:!1})})})]})},e.id),_=({gateways:e,setGateways:s})=>(0,d.jsx)(l.q6,{className:"woocommerce-list",items:e,setItems:s,children:e.map(((s,t)=>(0,d.jsx)(g,{gateway:s,className:"woocommerce-list__item"+(t===e.length-1?" is-last":"")},s.id)))}),w=()=>{const{offlinePaymentGateways:e,isFetching:s}=(0,i.useSelect)((e=>{const s=e(n.paymentSettingsStore);return{offlinePaymentGateways:s.getOfflinePaymentGateways(),isFetching:s.isFetching()}}),[]),{updateProviderOrdering:t}=(0,i.useDispatch)(n.paymentSettingsStore),[c,m]=(0,a.useState)(null);return(0,a.useEffect)((()=>{m(null)}),[e]),(0,d.jsx)(d.Fragment,{children:s?(0,d.jsx)(o.i,{rows:3}):(0,d.jsx)(_,{gateways:c||e,setGateways:function(e){const s=e.map((e=>e._order)).sort(((e,s)=>e-s)),i={};e.forEach(((e,t)=>{i[e.id]=s[t]})),t(i),m(e)}})})},h=w}}]);