"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[592],{90700:(e,o,t)=>{t.d(o,{A:()=>s});var r=t(5573),c=t(39793);const s=(0,c.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)(r.<PERSON>,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})})},56537:(e,o,t)=>{t.d(o,{A:()=>s});var r=t(5573),c=t(39793);const s=(0,c.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)(r.<PERSON>,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})})},48214:(e,o,t)=>{t.d(o,{A:()=>s});var r=t(5573),c=t(39793);const s=(0,c.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(r.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})},73290:(e,o,t)=>{t.d(o,{A:()=>s});var r=t(5573),c=t(39793);const s=(0,c.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(r.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})})},38150:(e,o,t)=>{t.d(o,{A:()=>s});var r=t(5573),c=t(39793);const s=(0,c.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(r.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})})},20273:(e,o,t)=>{t.d(o,{A:()=>s});var r=t(5573),c=t(39793);const s=(0,c.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(r.Path,{fillRule:"evenodd",d:"M6.5 8a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zM8 5a3 3 0 100 6 3 3 0 000-6zm6.5 11a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zm1.5-3a3 3 0 100 6 3 3 0 000-6zM5.47 17.41a.75.75 0 001.06 1.06L18.47 6.53a.75.75 0 10-1.06-1.06L5.47 17.41z",clipRule:"evenodd"})})},79745:(e,o,t)=>{t.d(o,{$k:()=>c,Q_:()=>n,eN:()=>r,x6:()=>s});const r=["image"],c=60,s=20,n=200},38966:(e,o,t)=>{t.d(o,{ex:()=>s,ot:()=>c});var r=t(52619);const c="entrepreneur-signup";(0,r.addFilter)("woocommerce_admin_persisted_queries","woocommerce_admin_customize_your_store",(e=>(e.push("ref"),e)));const s=()=>new URLSearchParams(window.location.search).get("ref")===c},30737:(e,o,t)=>{t.d(o,{G5:()=>b,QL:()=>x,ow:()=>w,wY:()=>h,k8:()=>g,bp:()=>y});var r=t(27723),c=t(4921),s=t(56427),n=t(96476),a=t(36849),i=t(98846),l=t(39793);const m=({siteUrl:e})=>(0,l.jsx)("iframe",{className:"preview-iframe",src:e,title:"Preview",tabIndex:-1});var u=t(56109),d=t(42859),p=t(47884);const _=({bannerTitle:e,bannerText:o,bannerClass:t,showAIDisclaimer:n,buttonIsLink:m,bannerButtonOnClick:u,bannerButtonText:d,secondaryButton:p,previewBanner:_,children:h})=>(0,l.jsxs)("div",{className:(0,c.A)("woocommerce-customize-store-banner",t),children:[(0,l.jsxs)("div",{className:"woocommerce-customize-store-banner-content",children:[(0,l.jsxs)("div",{className:"banner-actions",children:[(0,l.jsx)("h1",{children:e}),(0,l.jsx)("p",{children:o}),d&&(0,l.jsx)(s.Button,{onClick:()=>u&&u(),variant:m?"link":"primary",children:d}),p,n&&(0,l.jsx)("p",{className:"ai-disclaimer",children:(0,a.A)({mixedString:(0,r.__)("Powered by experimental AI. {{link}}Learn more{{/link}}","woocommerce"),components:{link:(0,l.jsx)(i.Link,{href:"https://automattic.com/ai-guidelines",target:"_blank",type:"external"})}})})]}),h]}),_]}),h=()=>(0,l.jsx)(_,{bannerTitle:(0,r.__)("Looking to design your store using AI?","woocommerce"),bannerText:(0,r.__)("Unfortunately, the [AI Store designer] isn't available right now as we can't detect your network. Please check your internet connection.","woocommerce"),bannerClass:"offline-banner",bannerButtonOnClick:()=>{},showAIDisclaimer:!0}),w=({sendEvent:e})=>(0,l.jsx)(_,{bannerTitle:(0,r.__)("Looking to design your store using AI?","woocommerce"),bannerText:(0,r.__)("It looks like you're using Jetpack's offline mode — switch to online mode to start designing with AI.","woocommerce"),bannerClass:"offline-banner",buttonIsLink:!1,bannerButtonOnClick:()=>{e({type:"JETPACK_OFFLINE_HOWTO"})},bannerButtonText:(0,r.__)("Find out how","woocommerce"),showAIDisclaimer:!0}),g=({redirectToCYSFlow:e})=>(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(_,{bannerTitle:(0,r.__)("Design your own","woocommerce"),bannerText:(0,r.__)("Quickly create a beautiful store using our built-in store designer. Choose your layout, select a style, and much more.","woocommerce"),bannerClass:"no-ai-banner",bannerButtonText:(0,r.__)("Start designing","woocommerce"),bannerButtonOnClick:()=>{e()},showAIDisclaimer:!1})}),x=()=>{const e=(0,u.Qk)("siteUrl")+"?cys-hide-admin-bar=1";return(0,l.jsx)(_,{bannerTitle:(0,r.__)("Customize your theme","woocommerce"),bannerText:(0,r.__)("Customize everything from the color palette and the fonts to the page layouts, making sure every detail aligns with your brand.","woocommerce"),bannerClass:"existing-no-ai-theme-banner",buttonIsLink:!1,bannerButtonOnClick:()=>{(0,p.s)("customize_your_store_intro_customize_click",{theme_type:"block"}),(0,d.$g)(window,(0,n.getNewPath)({customizing:!0},"/customize-store/assembler-hub",{}))},bannerButtonText:(0,r.__)("Customize your store","woocommerce"),showAIDisclaimer:!1,previewBanner:(0,l.jsx)(m,{siteUrl:e})})},b=()=>{const e=(0,u.Qk)("siteUrl")+"?cys-hide-admin-bar=1";return(0,l.jsx)(_,{bannerTitle:(0,r.__)("Customize your theme","woocommerce"),bannerText:(0,r.__)("Customize everything from the color palette and the fonts to the page layouts, making sure every detail aligns with your brand.","woocommerce"),bannerClass:"existing-no-ai-theme-banner",buttonIsLink:!1,bannerButtonOnClick:()=>{(0,p.s)("customize_your_store_intro_customize_click",{theme_type:"classic"}),(0,d.$g)(window,"customize.php?return=/wp-admin/themes.php")},bannerButtonText:(0,r.__)("Go to the Customizer","woocommerce"),showAIDisclaimer:!1,previewBanner:(0,l.jsx)(m,{siteUrl:e})})},y=()=>{const e=(0,u.Qk)("siteUrl")+"?cys-hide-admin-bar=1";return(0,l.jsx)(_,{bannerTitle:(0,r.__)("Customize your theme","woocommerce"),bannerText:(0,r.__)("Customize everything from the color palette and the fonts to the page layouts, making sure every detail aligns with your brand.","woocommerce"),bannerClass:"existing-no-ai-theme-banner",buttonIsLink:!1,bannerButtonOnClick:()=>{(0,p.s)("customize_your_store_intro_customize_click",{theme_type:"block"}),(0,d.$g)(window,`${u.kY}site-editor.php`)},bannerButtonText:(0,r.__)("Go to the Editor","woocommerce"),showAIDisclaimer:!1,previewBanner:(0,l.jsx)(m,{siteUrl:e})})}},81392:(e,o,t)=>{t.d(o,{z:()=>m});var r=t(56427),c=t(27723),s=t(98846),n=t(86087),a=t(56109),i=t(47884),l=t(39793);const m=({isNoAiFlow:e=!0,setIsModalOpen:o,redirectToCYSFlow:t})=>(0,l.jsxs)(r.Modal,{className:"woocommerce-customize-store__theme-switch-warning-modal",title:(0,c.__)("Are you sure you want to design a new theme?","woocommerce"),onRequestClose:()=>o(!1),shouldCloseOnClickOutside:!1,children:[(0,l.jsx)("p",{children:e?(0,c.__)("Your active theme will be changed and you could lose any changes you’ve made to it.","woocommerce"):(0,n.createInterpolateElement)((0,c.__)("The Store Designer will create a new store design for you, and you'll lose any changes you've made to your active theme. If you'd prefer to continue editing your theme, you can do so via the <EditorLink>Editor</EditorLink>.","woocommerce"),{EditorLink:(0,l.jsx)(s.Link,{onClick:()=>(window.open(`${a.kY}site-editor.php`,"_blank"),!1),href:""})})}),(0,l.jsxs)("div",{className:"woocommerce-customize-store__theme-switch-warning-modal-footer",children:[(0,l.jsx)(r.Button,{onClick:()=>{o(!1)},variant:"link",children:(0,c.__)("Cancel","woocommerce")}),(0,l.jsx)(r.Button,{onClick:()=>{o(!1),(0,i.s)("customize_your_store_agree_to_theme_switch_click"),t()},variant:"primary",children:(0,c.__)("Design a new theme","woocommerce")})]})]})},47884:(e,o,t)=>{t.d(o,{s:()=>n});var r=t(83306),c=t(38966),s=t(97687);const n=(e,o)=>{(0,s.E)()&&(0,c.ex)()?(0,r.recordEvent)(e,{...o,ref:c.ot}):(0,r.recordEvent)(e,o)}},42859:(e,o,t)=>{t.d(o,{$g:()=>d,AV:()=>l,Gy:()=>_,Hr:()=>p,Vu:()=>u,am:()=>h,d4:()=>a,j1:()=>i,q2:()=>m});var r=t(96476),c=t(86817),s=t(56109),n=t(79745);function a(e){return e.document!==e.parent.document&&null!==e.parent.document.body.querySelector(".woocommerce-customize-store__container")}function i(){window.parent.postMessage({type:"iframe-loaded"},(0,s.Qk)("homeUrl"))}function l(e){window.addEventListener("message",(o=>{"iframe-loaded"===o.data.type&&e()}))}function m(e){window.addEventListener("message",(o=>{"assemberBackButtonClicked"===o.data.type&&e()}))}function u(){const e=[(0,s.Qk)("homeUrl")];function o(o){if(e.includes(o.origin)&&o.data&&"string"==typeof o.data.type&&"string"==typeof o.data.url&&"navigate"===o.data.type)try{const t=(0,r.parseAdminUrl)(o.data.url);if(!e.some((e=>t.origin===e)))throw new Error(`Blocked navigation to untrusted URL: ${t.href}`);window.location.href=t.href}catch(e){(0,c.captureException)(e)}}return window.addEventListener("message",o,!1),function(){window.removeEventListener("message",o,!1)}}function d(e,o){try{if(a(e))e.parent.postMessage({type:"navigate",url:o},(0,s.Qk)("homeUrl"));else{const t=(0,r.parseAdminUrl)(o);e.location.href=t.href}}catch(e){(0,c.captureException)(e)}}function p(e){const o=e.contentDocument||e.contentWindow?.document;o?.addEventListener("click",(function(e){if(e.target){const o=e.target?.closest("a");o&&"_blank"===o.target?(e.preventDefault(),window.open(o.href,"_blank")):o&&(e.preventDefault(),window.location.href=o.href)}}))}const _=(e,o=n.$k)=>e.replaceAll(/<!-- wp:site-logo\s*(\{.*?\})?\s*\/-->/g,((e,t)=>{if(t){const e=JSON.parse(t);return e.width=o,`\x3c!-- wp:site-logo ${JSON.stringify(e)} /--\x3e`}return`\x3c!-- wp:site-logo {"width":${o}} /--\x3e`})),h=(e,o)=>e.map(((e,t,r)=>{const c=r[t+1];if(!c)return[e];const s=[e],n=(c.progress-e.progress)/o;for(let t=0;t<o;t++)s.push({...e,progress:e.progress+(t+1)*n});return s})).flat()},12974:(e,o,t)=>{t.d(o,{Ay:()=>n});var r=t(13240);const c=["a","b","em","i","strong","p","br"],s=["target","href","rel","name","download"],n=e=>({__html:(0,r.sanitize)(e,{ALLOWED_TAGS:c,ALLOWED_ATTR:s})})},25595:(e,o,t)=>{t.d(o,{A:()=>p});var r=t(4921),c=t(86087),s=t(73290),n=t(72744),a=t(20273),i=t(24148),l=t(48214),m=t(12974),u=t(39793);const d={info:s.A,check:n.A,percent:a.A};function p(e){const{id:o,description:t,children:s,icon:n,isDismissible:a=!0,variant:p="info",className:_,onClose:h,onLoad:w}=e,[g,x]=(0,c.useState)("true"!==localStorage.getItem(`wc-marketplaceNoticeClosed-${o}`));if((0,c.useEffect)((()=>{g&&"function"==typeof w&&w()}),[g]),!g)return null;const b=(0,r.A)("woocommerce-marketplace__notice",`woocommerce-marketplace__notice--${p}`,{"is-dismissible":a},_),y=d[n||"info"],v=(0,r.A)("woocommerce-marketplace__notice-icon",`woocommerce-marketplace__notice-icon--${p}`);return(0,u.jsxs)("div",{className:b,children:[n&&(0,u.jsx)("span",{className:v,children:(0,u.jsx)(i.A,{icon:y})}),(0,u.jsxs)("div",{className:"woocommerce-marketplace__notice-content",children:[(0,u.jsx)("p",{className:"woocommerce-marketplace__notice-description",dangerouslySetInnerHTML:(0,m.Ay)(t)}),s&&(0,u.jsx)("div",{className:"woocommerce-marketplace__notice-children",children:s})]}),a&&(0,u.jsx)("button",{className:"woocommerce-marketplace__notice-close","aria-label":"Close",onClick:()=>{x(!1),localStorage.setItem(`wc-marketplaceNoticeClosed-${o}`,"true"),"function"==typeof h&&h()},children:(0,u.jsx)(i.A,{icon:l.A})})]})}},55516:(e,o,t)=>{t.d(o,{A:()=>c});var r=t(39793);function c(){return(0,r.jsxs)("svg",{width:"72",height:"60",viewBox:"0 0 72 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsxs)("g",{clipPath:"url(#clip0_4074_10418)",children:[(0,r.jsx)("path",{d:"M68.5301 33.3144C68.0263 32.1006 66.3348 32.344 65.8443 31.1636C65.3538 29.9832 66.7251 28.9562 66.2213 27.7458C65.7175 26.5354 64.0259 26.7755 63.5355 25.5951C63.045 24.4147 64.4163 23.3877 63.9125 22.1773C63.4087 20.9669 61.7171 21.207 61.2267 20.0266C60.7362 18.8462 62.1075 17.8192 61.6037 16.6088C61.0999 15.395 59.4083 15.6385 58.9179 14.4581C58.4274 13.2777 59.7987 12.2507 59.2949 11.0403C58.7911 9.82652 57.0995 10.0699 56.6091 8.88955C56.1186 7.70915 57.4899 6.68214 56.9861 5.47174C56.4823 4.26134 54.7907 4.50142 54.3003 3.32102C53.8465 2.22733 55.0476 1.11696 54.8274 -0.00341797L0 22.5941C0.5038 23.8079 2.19537 23.5644 2.68582 24.7448C3.17627 25.9252 1.805 26.9522 2.3088 28.1626C2.8126 29.373 4.50417 29.133 4.99462 30.3134C5.48508 31.4937 4.11381 32.5208 4.61761 33.7312C5.12141 34.9416 6.81297 34.7015 7.30343 35.8819C7.79388 37.0623 6.42261 38.0893 6.92641 39.2997C7.43021 40.5134 9.12178 40.27 9.61223 41.4504C10.1027 42.6308 8.73142 43.6578 9.23522 44.8682C9.73902 46.0786 11.4306 45.8385 11.921 47.0189C12.4115 48.1993 11.0402 49.2263 11.544 50.4367C12.0478 51.6471 13.7394 51.4071 14.2298 52.5874C14.6836 53.6811 13.4825 54.7915 13.7027 55.9119L28.1928 49.9232L68.5368 33.3177L68.5301 33.3144Z",fill:"#720EEC"}),(0,r.jsx)("path",{d:"M13.696 55.912L28.1861 49.9234L52.3851 39.9634H7.46021C8.17086 40.4802 9.23852 40.5569 9.60886 41.4539C10.0993 42.6343 8.72805 43.6613 9.23185 44.8717C9.73565 46.0821 11.4272 45.842 11.9177 47.0224C12.4081 48.2028 11.0368 49.2298 11.5406 50.4402C12.0444 51.6506 13.736 51.4105 14.2265 52.5909C14.6802 53.6846 13.4791 54.795 13.6993 55.9154L13.696 55.912Z",fill:"#3C087E"}),(0,r.jsx)("path",{d:"M63.8523 41.9907C63.8523 37.4925 67.499 33.848 71.9998 33.848V23.988H17.873V33.848C22.3739 33.848 26.0206 37.4925 26.0206 41.9907C26.0206 46.4889 22.3739 50.1334 17.873 50.1334V59.9934H71.9998V50.1334C67.499 50.1334 63.8523 46.4889 63.8523 41.9907Z",fill:"#D1C1FF"}),(0,r.jsx)("path",{d:"M35.2527 37.676C35.2527 35.2051 37.0143 33.2878 39.6968 33.2878C42.3793 33.2878 44.1643 35.2051 44.1643 37.676C44.1643 40.1468 42.4026 42.0107 39.6968 42.0107C36.991 42.0107 35.2527 40.1201 35.2527 37.676ZM41.7954 37.676C41.7954 36.2288 40.9046 35.3385 39.6935 35.3385C38.4823 35.3385 37.6182 36.2288 37.6182 37.676C37.6182 39.1231 38.509 39.9601 39.6935 39.9601C40.8779 39.9601 41.7954 39.0664 41.7954 37.676ZM37.9852 51.0704L49.1789 33.5513H51.1774L39.9537 51.0704H37.9819H37.9852ZM44.8983 47.0524C44.8983 44.5849 46.6566 42.641 49.3391 42.641C52.0215 42.641 53.8065 44.5849 53.8065 47.0524C53.8065 49.5199 52.0182 51.3872 49.3391 51.3872C46.6599 51.3872 44.8983 49.4966 44.8983 47.0524ZM51.441 47.0524C51.441 45.6053 50.5468 44.715 49.3357 44.715C48.1246 44.715 47.2605 45.6053 47.2605 47.0524C47.2605 48.4996 48.1279 49.3365 49.3357 49.3365C50.5435 49.3365 51.441 48.4696 51.441 47.0524Z",fill:"#720EEC"})]}),(0,r.jsx)("defs",{children:(0,r.jsx)("clipPath",{id:"clip0_4074_10418",children:(0,r.jsx)("rect",{width:"72",height:"60",fill:"white"})})})]})}},62640:(e,o,t)=>{t.d(o,{A:()=>u});var r=t(56427),c=t(27723),s=t(86087),n=t(83306),a=t(12974),i=t(55516),l=t(39793);const m={percent:i.A},u=({promotion:e})=>{var o,t;const i=window.location.pathname+window.location.search,u=()=>JSON.parse(localStorage.getItem("wc-marketplaceDismissedPromos")||"[]"),[d,p]=(0,s.useState)(!u().includes(i));if((0,s.useEffect)((()=>{d&&(0,n.recordEvent)("marketplace_promotion_viewed",{path:i,format:"promo-card"})}),[d]),!d)return null;const _="promo-card"+(e.style?` ${e.style}`:""),h=(0,l.jsxs)("div",{className:"promo-content",children:[(0,l.jsx)("h2",{className:"promo-title",children:e.title?.en_US}),(0,l.jsx)("div",{className:"promo-text",dangerouslySetInnerHTML:(0,a.Ay)(e.content?.en_US)})]}),w=(0,l.jsxs)("div",{className:"promo-links",children:[(0,l.jsx)(r.Button,{className:"promo-cta",href:null!==(o=e.cta_link)&&void 0!==o?o:"",onClick:()=>((0,n.recordEvent)("marketplace_promotion_actioned",{path:i,target_uri:e.cta_link,format:"promo-card"}),!0),children:null!==(t=e.cta_label?.en_US)&&void 0!==t?t:""}),(0,l.jsx)(r.Button,{className:"promo-cta-link",onClick:()=>{p(!1),localStorage.setItem("wc-marketplaceDismissedPromos",JSON.stringify(u().concat(i))),(0,n.recordEvent)("marketplace_promotion_dismissed",{path:i,format:"promo-card"})},children:(0,c.__)("Dismiss","woocommerce")})]});function g(){if(e.icon&&Object.hasOwn(m,e.icon)){const o=m[e.icon];return o?(0,l.jsx)("div",{className:"promo-image",children:(0,s.createElement)(o)}):null}return null}return(0,l.jsx)("div",{className:_,children:"has-background"===e?.style?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"promo-content-links",children:[h,w]}),g()]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"promo-content-image",children:[h,g()]}),w]})})}},79492:(e,o,t)=>{t.d(o,{A:()=>i});var r=t(83306),c=t(56109),s=t(25595),n=t(62640),a=t(39793);const i=({format:e})=>{var o;if(!window?.wcMarketplace?.promotions||!Array.isArray(window?.wcMarketplace?.promotions))return null;const t=(null!==(o=window?.wcMarketplace?.promotions)&&void 0!==o?o:[]).filter((o=>o.format===e)),i=new URLSearchParams(window.location.search),l=i.get("page"),m=Date.now(),u=decodeURIComponent(i.get("path")||""),d=i.get("tab"),p=window.location.pathname+window.location.search,_=()=>{(0,r.recordEvent)("marketplace_promotion_viewed",{path:p,format:e})},h=()=>{(0,r.recordEvent)("marketplace_promotion_dismissed",{path:p,format:e})};return(0,a.jsx)(a.Fragment,{children:t.map(((e,o)=>{if(!e.pages)return null;if(!e.pages.some((e=>{if(e.pathname)return e.pathname===p;if(!e.path)return!1;const o=e=>e.startsWith("/")?e:`/${e}`,t=o(e.path),r=o(u);return e.page===l&&t===r&&(e.tab?d:!d)})))return null;const t=new Date(e.date_from_gmt).getTime(),r=new Date(e.date_to_gmt).getTime();return m<t||m>r?null:"promo-card"===e.format?(0,a.jsx)(n.A,{promotion:e},o):"notice"===e.format&&e?.content?(0,a.jsx)(s.A,{id:null!==(i=e.menu_item_id)&&void 0!==i?i:`promotion-${o}`,description:e.content[c.ne.userLocale]||e.content.en_US,variant:e.style?e.style:"info",icon:e?.icon||"",isDismissible:e.is_dismissible||!1,onLoad:_,onClose:h},o):null;var i}))})}},60110:(e,o,t)=>{t.r(o),t.d(o,{default:()=>ho});var r=t(86087),c=t(56109),s=t(11488),n=t(39793);const a=(i="wc_iam_settings",{setWithExpiry:e=>{const o=new Date,t={data:e,expiry:new Date(o.getTime()+864e5).toISOString()};localStorage.setItem(i,JSON.stringify(t))},getWithExpiry:()=>{const e=localStorage.getItem(i);if(!e)return null;const o=JSON.parse(e);return new Date>new Date(o.expiry)?(localStorage.removeItem(i),null):o.data}});var i;const l=(0,r.createContext)({isLoading:!1,setIsLoading:()=>{},selectedTab:"",setSelectedTab:()=>{},isProductInstalled:()=>!1,addInstalledProduct:()=>{},searchResultsCount:{extensions:0,themes:0,"business-services":0},setSearchResultsCount:()=>{},iamSettings:{}});function m(e){const[o,t]=(0,r.useState)(!0),[i,m]=(0,r.useState)(""),[u,d]=(0,r.useState)({}),[p,_]=(0,r.useState)([]),[h,w]=(0,r.useState)({extensions:0,themes:0,"business-services":0}),g=(0,r.useCallback)((e=>{w((o=>({...o,...e})))}),[]);(0,r.useEffect)((()=>{const e=a.getWithExpiry();if(e)return void d(e);const o=`${s.H2}${s.rx}`;fetch(o).then((e=>{if(!e.ok)throw new Error(`Network response was not ok: ${e.statusText}`);return e.json()})).then((e=>{d(e),a.setWithExpiry(e)})).catch((e=>{console.error("Failed to fetch IAM settings:",e),d({})}))}),[]),(0,r.useEffect)((()=>{const e=(0,c.Qk)("wccomHelper",{}),o=e?.installedProducts;_(o)}),[]);const x={isLoading:o,setIsLoading:t,selectedTab:i,setSelectedTab:m,isProductInstalled:function(e){return p.includes(e)},addInstalledProduct:function(e){_([...p,e])},searchResultsCount:h,setSearchResultsCount:g,iamSettings:u};return(0,n.jsx)(l.Provider,{value:x,children:e.children})}var u=t(27723);function d(){return(0,n.jsx)("h1",{className:"woocommerce-marketplace__header-title",children:(0,u.__)("The WooCommerce Marketplace","woocommerce")})}var p,_=t(10790),h=t(56427),w=t(4921),g=t(96476);const x=(0,c.Qk)("wccomHelper",{}),b=null!==(p=x?.wooUpdateCount)&&void 0!==p?p:0,y=(e,o,t)=>{const{selectedTab:r,setSelectedTab:c}=e,a=e=>{e!==r&&(c(e),((e,o)=>{const t=o.term?{term:o.term.trim()}:{};(0,g.navigateTo)({url:(0,g.getNewPath)({tab:e===s.mz?void 0:e},s.q$,t)})})(e,t))},i=[];for(const e in o)i.push(o[e]?.href?(0,n.jsx)("a",{className:(0,w.A)("woocommerce-marketplace__tab-button","components-button",`woocommerce-marketplace__tab-${e}`),href:o[e]?.href,children:o[e]?.title},e):(0,n.jsxs)(h.Button,{className:(0,w.A)("woocommerce-marketplace__tab-button",`woocommerce-marketplace__tab-${e}`,{"is-active":e===r}),onClick:()=>a(e),children:[o[e]?.title,o[e]?.showUpdateCount&&(0,n.jsx)("span",{className:(0,w.A)("woocommerce-marketplace__update-count",`woocommerce-marketplace__update-count-${e}`,{"is-active":e===r}),title:"my-subscriptions"===e?(0,u.sprintf)((0,u._n)("%d update available for your subscriptions","%d updates available for your subscriptions",o[e]?.updateCount,"woocommerce"),o[e]?.updateCount):(0,u.sprintf)((0,u._n)("%d matching item in this category","%d matching items in this category",o[e]?.updateCount,"woocommerce"),o[e]?.updateCount),"aria-label":"my-subscriptions"===e?(0,u.sprintf)((0,u._n)("%d update available for your subscriptions","%d updates available for your subscriptions",o[e]?.updateCount,"woocommerce"),o[e]?.updateCount):(0,u.sprintf)((0,u._n)("%d matching item in this category","%d matching items in this category",o[e]?.updateCount,"woocommerce"),o[e]?.updateCount),children:(0,n.jsxs)("span",{children:[" ",o[e]?.updateCount," "]})})]},e));return i},v=e=>{const{additionalClassNames:o}=e,t=(0,r.useContext)(l),{isLoading:c,setSelectedTab:a}=t,{searchResultsCount:i}=t,m=(0,g.useQuery)(),d=(0,r.useMemo)((()=>({discover:{name:"discover",title:(0,u.__)("Discover","woocommerce"),showUpdateCount:!1,updateCount:0},extensions:{name:"extensions",title:(0,u.__)("Extensions","woocommerce"),showUpdateCount:!!m.term&&!c,updateCount:i.extensions},themes:{name:"themes",title:(0,u.__)("Themes","woocommerce"),showUpdateCount:!!m.term&&!c,updateCount:i.themes},"business-services":{name:"business-services",title:(0,u.__)("Business services","woocommerce"),showUpdateCount:!!m.term&&!c,updateCount:i["business-services"]},"my-subscriptions":{name:"my-subscriptions",title:(0,u.__)("My subscriptions","woocommerce"),showUpdateCount:b>0,updateCount:b}})),[m,c,i]);return(0,r.useEffect)((()=>{m?.tab&&d[m.tab]?a(m.tab):Object.keys(m).length>0&&a(s.mz)}),[m,a,d]),(0,n.jsx)("nav",{className:(0,w.A)("woocommerce-marketplace__tabs",o||[]),children:y(t,d,m)})};var f=t(83306);const k=function(){const[e,o]=(0,r.useState)(""),t=(0,u.__)("Search Marketplace","woocommerce"),c=(0,g.useQuery)();(0,r.useEffect)((()=>{c.term?o(c.term):o("")}),[c.term]);const a=o=>{const t=c;return t.tab&&"my-subscriptions"!==t.tab||(t.tab="extensions"),t.term=void 0!==o?o:e.trim(),t.search="1",t.term||delete t.term,(0,g.navigateTo)({url:(0,g.getNewPath)(t,s.q$,{})}),[]};return(0,n.jsx)(h.SearchControl,{label:t,placeholder:t,value:e,onChange:o,onKeyUp:e=>{"Enter"===e.key&&a(),"Escape"===e.key&&o("")},onClose:()=>{o(""),a("")},onFocus:()=>{(0,f.recordEvent)("marketplace_search_start",{current_search_term:e,current_tab:c.tab||"discover"})},className:"woocommerce-marketplace__search"})};function j(){return(0,n.jsx)("div",{className:"woocommerce-marketplace__header-container",children:(0,n.jsxs)("header",{className:"woocommerce-marketplace__header",children:[(0,n.jsx)(d,{}),(0,n.jsx)(v,{additionalClassNames:["woocommerce-marketplace__header-tabs"]}),(0,n.jsx)(k,{}),(0,n.jsx)("div",{className:"woocommerce-marketplace__header-meta",children:(0,n.jsx)(_.A,{page:"wc-addons"})})]})})}var N=t(20195),C=t(15001),M=t(93832),T=t(18537),A=t(40314);const I=function(e){const{product:o}=e,{user:t,currentUserCan:c}=(0,A.useUser)(),{selectedTab:s,isProductInstalled:a}=(0,r.useContext)(l),i={USD:"$%s",AUD:"A$%s",CAD:"C$%s",EUR:"€%s",GBP:"£%s"},m=e=>i[e]||"%s";function d(){return 0===o.price?(0,u.__)("Free download","woocommerce"):"primary"===o.freemium_type?(0,u.__)("Free plan available","woocommerce"):(0,u.sprintf)(m(o.currency),o.price)}function p(){return"primary"===o.freemium_type?"":0!==o.price?function(){if(1===o.billingPeriodInterval||""===o.billingPeriod)switch(o.billingPeriod){case"day":return(0,u.__)("daily","woocommerce");case"week":return(0,u.__)("weekly","woocommerce");case"month":return(0,u.__)("monthly","woocommerce");case"year":case"":return(0,u.__)("annually","woocommerce");default:return""}let e;switch(o.billingPeriod){case"day":e=(0,u.__)("days","woocommerce");break;case"week":e=(0,u.__)("weeks","woocommerce");break;case"month":e=(0,u.__)("months","woocommerce");break;default:e=(0,u.__)("years","woocommerce")}return(0,u.sprintf)((0,u.__)("every %1$d %2$s","woocommerce"),o.billingPeriodInterval,e)}():""}return o.slug&&a(o.slug)?(0,n.jsx)("span",{className:"woocommerce-marketplace__product-card__installed-label",children:(0,u.__)("Installed","woocommerce")}):(_=o,!(t&&_&&c("install_plugins")&&_.isInstallable&&"theme"!==_.type&&"discover"!==s)||_.slug&&a(_.slug)?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-card__price",children:[(0,n.jsxs)("span",{className:"woocommerce-marketplace__product-card__price-label",children:[(0,n.jsx)("span",{className:"screen-reader-text",children:o.isOnSale?(0,u.sprintf)((0,u.__)("Sale Price %1$s %3$s, regular price %2$s %3$s","woocommerce"),d(),(0,u.sprintf)(m(o.currency),o.regularPrice),p()):0!==o.price&&"primary"!==o.freemium_type?(0,u.sprintf)((0,u.__)(" %1$s, %2$s ","woocommerce"),d(),p()):d()}),(0,n.jsx)("span",{"aria-hidden":!0,children:d()})]}),o.isOnSale&&(0,n.jsx)("span",{className:"woocommerce-marketplace__product-card__on-sale","aria-hidden":!0,children:(0,u.sprintf)(m(o.currency),o.regularPrice)}),(0,n.jsx)("span",{className:"woocommerce-marketplace__product-card__price-billing","aria-hidden":!0,children:p()})]}),(0,n.jsx)("div",{className:"woocommerce-marketplace__product-card__rating",children:null!==o.averageRating&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{className:"woocommerce-marketplace__product-card__rating-icon",children:(0,n.jsx)(h.Icon,{icon:"star-filled",size:16})}),(0,n.jsxs)("span",{className:"woocommerce-marketplace__product-card__rating-average",children:[(0,n.jsx)("span",{"aria-hidden":!0,children:o.averageRating}),(0,n.jsx)("span",{className:"screen-reader-text",children:(0,u.sprintf)((0,u.__)("%.1f stars","woocommerce"),o.averageRating)})]}),(0,n.jsxs)("span",{className:"woocommerce-marketplace__product-card__rating-count",children:[(0,n.jsxs)("span",{"aria-hidden":!0,children:["(",o.reviewsCount,")"]}),(0,n.jsx)("span",{className:"screen-reader-text",children:(0,u.sprintf)((0,u.__)("from %d reviews","woocommerce"),o.reviewsCount)})]})]})})]}):(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("span",{className:"woocommerce-marketplace__product-card__add-to-store",children:(0,n.jsx)(h.Button,{variant:"secondary",onClick:function(){(0,f.recordEvent)("marketplace_add_to_store_clicked",{product_id:o.id}),(0,g.navigateTo)({url:(0,g.getNewPath)({installProduct:o.id})})},children:(0,u.__)("Add to Store","woocommerce")})})}));var _};var S=t(95893),E=t(98846),L=t(13240);const D=["a","b","blockquote","br","button","cite","code","dd","div","dl","dt","em","h1","h2","h3","h4","h5","h6","hr","i","img","li","mark","ol","p","pre","small","span","strong","sub","sup","table","tbody","td","th","thead","tr","ul"],z=["alt","border","class","download","href","id","height","name","rel","role","sizes","src","srcset","style","target","title","width"],O={allowedTags:[...D,"path","svg","footer","header"],allowedAttributes:[...z,"d","fill","viewBox","xmlns"]};function U({productTitle:e,productVendor:o,productIcon:t,productId:c,triggerRef:s,onOpen:a,onClose:i}){const[l,m]=(0,r.useState)(!0),[d,p]=(0,r.useState)(null),[_,w]=(0,r.useState)(null),g=(0,r.useCallback)(((e="")=>{i&&i(e),setTimeout((()=>{s.current&&s.current.focus()}),100)}),[i,s]);(0,r.useEffect)((()=>{const e=e=>{const o=e.target.closest("a");if(o){const e=o.getAttribute("data-iam-tracks");e&&g(`marketplace_product_preview_modal_${e}_clicked`)}},o=document.querySelector(".woocommerce-marketplace__product-preview-modal__content");if(o)return o.addEventListener("click",e),()=>{o.removeEventListener("click",e)}}),[g]),(0,r.useEffect)((()=>{(async()=>{try{var e;const o=await(0,S.QC)(c),t=o?.data||o;if(!t?.html||!t?.css)throw new Error("Invalid preview data structure");const r=function(e,o={}){if(!e)return"";const t={ALLOWED_TAGS:o.allowedTags||D,ALLOWED_ATTR:o.allowedAttributes||z};return{__html:(0,L.sanitize)(e,t)}}(t.html,O),s=null!==(e=r?.__html)&&void 0!==e?e:"";p({html:s,css:t.css}),w(null)}catch(e){w((0,u.__)("Failed to load product preview.","woocommerce"))}finally{m(!1)}})(),a&&a()}),[a,c]);const x=(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-preview-modal__header",children:[t&&(0,n.jsx)("img",{className:"woocommerce-marketplace__product-preview-modal__icon",src:t,alt:e}),(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-preview-modal__header-content",children:[(0,n.jsx)("h2",{children:e}),o&&(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-preview-modal__vendor",children:[(0,n.jsx)("span",{children:(0,u.__)("By","woocommerce")})," ",o]})]})]});return(0,n.jsxs)(h.Modal,{onRequestClose:()=>g("marketplace_product_preview_modal_dismissed"),className:"woocommerce-marketplace__product-preview-modal",closeButtonLabel:(0,u.__)("Close product preview","woocommerce"),size:"large",focusOnMount:"firstElement",title:(0,u.__)("The WooCommerce Marketplace","woocommerce"),children:[x,(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-preview-modal__content",children:[l&&(0,n.jsx)("div",{className:"woocommerce-marketplace__product-preview-modal__loading",children:(0,n.jsx)(E.Spinner,{})}),_&&(0,n.jsx)("div",{className:"woocommerce-marketplace__product-preview-modal__error",children:_}),!l&&!_&&d&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{children:d.css}),(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:d.html}})]})]})]})}const P=function(e){var o;const{isLoading:t,cardType:c}=e,s="compact"===c,a=(0,g.useQuery)(),[i,m]=(0,r.useState)(!1),d=(0,r.useRef)(null),p=null!==(o=e.product)&&void 0!==o?o:{id:null,title:"",description:"",vendorName:"",vendorUrl:"",icon:"",label:null,primary_color:null,url:"",price:0,image:"",averageRating:null,reviewsCount:null,featuredImage:"",color:"",productCategory:"",billingPeriod:"",billingPeriodInterval:0,currency:"",isOnSale:!1,regularPrice:0,type:""},_=p.type===C.ch.businessService?p.type:e.type,x=_===C.ch.theme,b=_===C.ch.businessService,{iamSettings:y}=(0,r.useContext)(l),v="modal"===y?.product_previews&&!x&&!b,k=!s&&!t,j=!s&&t,N=!x&&!s,M=!x||s,A=x&&!s,E=(0,T.decodeEntities)(p.description);function L(){return"promoted"===p.label}function D(o,t){const{tracksData:r}=e;r.position&&(t.position=r.position),r.label&&(t.label=r.label),r.group&&(t.group=r.group),r.group_id&&(t.group_id=r.group_id),r.searchTerm&&(t.search_term=r.searchTerm),r.category&&(t.category=r.category),t.tab=a.tab||"discover",(0,f.queueRecordEvent)(o,t)}const z=(0,n.jsx)("span",{className:"screen-reader-text",children:(0,u.__)("Opens in a new tab","woocommerce")}),O=e=>p?.vendorName&&p?.vendorUrl?(0,n.jsxs)("a",{href:p.vendorUrl,rel:"noopener noreferrer",target:"_blank",onClick:()=>{D(e,{product:p.title,vendor:p.vendorName,product_type:_})},children:[p.vendorName,z]}):p?.vendorName||null,P=O("marketplace_product_card_vendor_clicked"),B=(0,w.A)("woocommerce-marketplace__product-card",`woocommerce-marketplace__product-card--${_}`,{"is-loading":t,"is-small":e.small,"is-sponsored":L(),"is-compact":s}),Q=()=>(0,n.jsxs)("a",{ref:d,className:"woocommerce-marketplace__product-card__link",href:a.ref?(0,S.A5)(p.url,[["utm_content",a.ref]]):p.url,rel:"noopener noreferrer",target:"_blank",onClick:e=>{v?(e.preventDefault(),D("marketplace_product_card_clicked",{product_id:p.id,product_name:p.title,vendor:p.vendorName,product_type:_}),v&&m(!0)):D("marketplace_product_card_clicked",{product_id:p.id,product_name:p.title,vendor:p.vendorName,product_type:_})},children:[t?" ":p.title,z]}),R=()=>{const e=s?p.icon:p.featuredImage,o=s?96:288;return(0,n.jsxs)("div",{className:"woocommerce-marketplace__business-card",children:[(0,n.jsx)("div",{className:"woocommerce-marketplace__business-card__header",style:{backgroundColor:p.color},children:(0,n.jsx)("img",{src:`${e||p.featuredImage}?h=${o}`,alt:""})}),(0,n.jsxs)("div",{className:"woocommerce-marketplace__business-card__content",children:[(0,n.jsxs)("div",{className:"woocommerce-marketplace__business-card__main-content",children:[(0,n.jsx)("h2",{children:(0,n.jsx)(Q,{})}),(0,n.jsx)("p",{className:"woocommerce-marketplace__product-card__description",children:E})]}),(0,n.jsx)("div",{className:"woocommerce-marketplace__business-card__badge",children:(0,n.jsx)("span",{children:p.productCategory})})]})]})},Y=b?null:(0,n.jsxs)("footer",{className:"woocommerce-marketplace__product-card__footer",children:[t&&(0,n.jsx)("div",{className:"woocommerce-marketplace__product-card__price"}),!t&&e.product&&(0,n.jsx)(I,{product:e.product})]}),$=(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-card__content",children:[A&&(0,n.jsx)("div",{className:"woocommerce-marketplace__product-card__image",children:!t&&(0,n.jsx)("img",{className:"woocommerce-marketplace__product-card__image-inner",src:p.image,alt:p.title})}),(0,n.jsx)("div",{className:"woocommerce-marketplace__product-card__header",children:(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-card__details",children:[M&&(0,n.jsxs)(n.Fragment,{children:[t&&(0,n.jsx)("div",{className:"woocommerce-marketplace__product-card__icon"}),!t&&p.icon&&(0,n.jsx)("img",{className:"woocommerce-marketplace__product-card__icon",src:p.icon||p.image,alt:p.title})]}),(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-card__meta",children:[(0,n.jsx)("h2",{className:"woocommerce-marketplace__product-card__title",children:(0,n.jsx)(Q,{})}),j&&(0,n.jsx)("p",{className:"woocommerce-marketplace__product-card__vendor-details",children:(0,n.jsx)("span",{className:"woocommerce-marketplace__product-card__vendor"})}),k&&(0,n.jsxs)("p",{className:"woocommerce-marketplace__product-card__vendor-details",children:[P&&(0,n.jsxs)("span",{className:"woocommerce-marketplace__product-card__vendor",children:[(0,n.jsx)("span",{children:(0,u.__)("By ","woocommerce")}),P]}),P&&L()&&(0,n.jsx)("span",{"aria-hidden":"true",className:"woocommerce-marketplace__product-card__vendor-details__separator",children:"·"}),L()&&(0,n.jsx)("span",{className:"woocommerce-marketplace__product-card__sponsored-label",children:(0,u.__)("Sponsored","woocommerce")})]}),s&&Y]})]})}),N&&(0,n.jsx)("p",{className:"woocommerce-marketplace__product-card__description",children:!t&&E}),!s&&Y]}),W=()=>t?b?(0,n.jsx)(R,{}):$:(0,n.jsx)("div",{className:"woocommerce-marketplace__product-card-wrapper",children:b?(0,n.jsx)(R,{}):$});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h.Card,{className:B,id:`product-${p.id}`,tabIndex:-1,"aria-hidden":t,style:L()&&p.primary_color?{background:`linear-gradient(${p.primary_color} 0, ${p.primary_color} 5px, white 5px, white)`}:{},children:(0,n.jsx)(W,{})}),v&&i&&p&&(0,n.jsx)(U,{productTitle:p.title,productVendor:O("marketplace_product_preview_vendor_clicked"),productIcon:p.icon||"",onOpen:()=>{D("marketplace_product_preview_modal_opened",{product_id:p.id,product_name:p.title,vendor:p.vendorName,product_type:_})},onClose:e=>{D(e||"marketplace_product_preview_modal_dismissed",{product_id:p.id,product_name:p.title,vendor:p.vendorName,product_type:_}),m(!1)},productId:p.id,triggerRef:d})]})};var B=t(30737);function Q(e){const o=(0,c.Qk)("wccomHelper",{}),[t,s]=(0,r.useState)(e.products),[a,i]=(0,r.useState)(1),l=()=>{const e=window.innerWidth;i(e>=1920?4:e>=1024?3:e>=769?2:1)};(0,r.useEffect)((()=>(l(),window.addEventListener("resize",l),()=>window.removeEventListener("resize",l))),[]),(0,r.useEffect)((()=>{if(!e.productGroup)return void s(e.products);if(e.cardType===C.WX.compact)return void s(e.products);if(e.products.length<a)return void s(e.products);let o=Math.floor(e.products.length/a);1===a&&(o=Math.min(o,4)),2===a&&(o=Math.min(o,2)),s(e.products.slice(0,o*a))}),[a,e.products,e.productGroup,e.cardType]);const m=2*a-1,u=(0,w.A)("woocommerce-marketplace__product-list-content",e.className);return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("div",{className:u,children:t.map(((t,s)=>(0,n.jsxs)(r.Fragment,{children:[(0,n.jsx)(P,{type:e.type,cardType:e.cardType,product:{id:t.id,slug:t.slug,title:t.title,image:t.image,type:t.type,freemium_type:t.freemium_type,icon:t.icon,label:t.label,primary_color:t.primary_color,vendorName:t.vendorName,vendorUrl:t.vendorUrl?(0,S.A5)(t.vendorUrl,[["utm_source","extensionsscreen"],["utm_medium","product"],["utm_campaign","wcaddons"],["utm_content","devpartner"]]):"",price:t.price,url:(0,S.A5)(t.url,Object.entries({...o.inAppPurchaseURLParams,...void 0!==e.productGroup?{utm_group:e.productGroup}:{}})),averageRating:t.averageRating,reviewsCount:t.reviewsCount,description:t.description,isInstallable:t.isInstallable,color:t.color,featuredImage:t.featuredImage,productCategory:t.productCategory,billingPeriod:t.billingPeriod,billingPeriodInterval:t.billingPeriodInterval,currency:t.currency,isOnSale:t.isOnSale,regularPrice:t.regularPrice},tracksData:{position:s+1,...t.label&&{label:t.label},...e.productGroup&&{group_id:e.productGroup},...e.group&&{group:e.group},...e.searchTerm&&{searchTerm:e.searchTerm},...e.category&&{category:e.category}}}),s===m&&"theme"===e.type&&(0,n.jsx)(B.k8,{redirectToCYSFlow:()=>{const e=(0,M.addQueryArgs)(`${c.kY}admin.php`,{page:"wc-admin",path:"/customize-store/design"});window.location.href=e}})]},t.id)))})})}function R(e){const{title:o,description:t,groupURL:r,groupURLText:c,groupURLType:s}=e,a=""===o,i=(0,w.A)("woocommerce-marketplace__product-list-header",{"is-loading":a});return(0,n.jsxs)("div",{className:i,"aria-hidden":a,children:[(0,n.jsx)("h2",{className:"woocommerce-marketplace__product-list-title",children:o}),t&&(0,n.jsx)("p",{className:"woocommerce-marketplace__product-list-description",children:t}),null!==r&&(0,n.jsx)("span",{className:"woocommerce-marketplace__product-list-link",children:(0,n.jsx)(E.Link,{href:r,type:s,target:"external"===s?"_blank":void 0,onClick:()=>{(0,f.recordEvent)("marketplace_see_more_clicked",{group_title:o,group_url:r})},children:null!=c?c:(0,u.__)("See more","woocommerce")})})]})}function Y(e){const{title:o,description:t,products:r,groupURL:c,type:s,productGroup:a,groupURLText:i,groupURLType:l,cardType:m}=e;return(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-list",children:[(0,n.jsx)(R,{title:o,groupURL:c,groupURLText:i,description:t,groupURLType:l}),(0,n.jsx)(Q,{group:o,products:r,type:s,cardType:m,productGroup:a})]})}function $(e){const{hasTitle:o,type:t}=e,r=e.placeholderCount||12;return(0,n.jsxs)("div",{className:"woocommerce-marketplace__product-list",children:[!1!==o&&(0,n.jsx)(R,{title:"",groupURL:null,description:"",groupURLText:null,groupURLType:void 0}),(0,n.jsx)("div",{className:"woocommerce-marketplace__product-list-content",children:[...Array(r)].map(((e,o)=>(0,n.jsx)(P,{isLoading:!0,type:t,tracksData:{}},o)))})]})}function W(){var e;const o=(0,c.Qk)("wccomHelper",{});return null!==(e=o?.isConnected)&&void 0!==e&&e}function F(e){if(""===e.category)return;const o=e.view||"discover",t=e.search_term||null,r=e.product_type||null,c=e.category||null,s={...o&&{view:o},...t&&{search_term:t},...r&&{product_type:r},...c&&{category:c},wccom_connected:W()};o&&["extensions","themes","business-services"].includes(o)&&!c&&(s.category="_all"),(0,f.recordEvent)("marketplace_view",s)}function G(){const[e,o]=(0,r.useState)([]),t=(0,r.useContext)(l),{isLoading:c,setIsLoading:s}=t;if((0,r.useEffect)((()=>{s(!0),(0,S.b8)().then((e=>Array.isArray(e)?e:[])).then((e=>{o(e),function(e){const o=e.flatMap((e=>e.items)).map((e=>e.id));(0,f.recordEvent)("marketplace_discover_viewed",{view:"discover",product_ids:o}),F({view:"discover"})}(e)})).finally((()=>{s(!1)}))}),[]),c)return(0,n.jsx)("div",{className:"woocommerce-marketplace__discover",children:(0,n.jsx)($,{placeholderCount:9,type:C.ch.extension})});const a=e.flatMap((e=>e));return(0,n.jsx)("div",{className:"woocommerce-marketplace__discover",children:a.map((e=>{var o;return(0,n.jsx)(Y,{productGroup:e.id,title:e.title,description:e.description,products:e.items,groupURL:e.url,groupURLText:e.url_text,groupURLType:e.url_type,type:e.itemType,cardType:null!==(o=e.cardType)&&void 0!==o?o:C.WX.regular},e.id)}))})}var H=t(47143),V=t(29491);function Z(e){const o=""===e.label,t=(0,w.A)("woocommerce-marketplace__category-item-button",{"woocommerce-marketplace__category-item-button--selected":e.selected,"is-loading":o});return(0,n.jsx)("button",{className:t,onClick:function(e){const o=e.currentTarget.value;o&&(0,g.navigateTo)({url:(0,g.getNewPath)({category:o})})},value:e.slug,"aria-hidden":o,children:e.label})}var q=t(24148),K=t(56537),J=t(90700);function X(e){function o(o){const t=o.currentTarget.value;t&&(e.onClick(),(0,g.navigateTo)({url:(0,g.getNewPath)({category:t})}))}return(0,n.jsx)("ul",{className:"woocommerce-marketplace__category-dropdown-list",children:e.categories.map((t=>(0,n.jsx)("li",{className:"woocommerce-marketplace__category-dropdown-item",children:(0,n.jsx)("button",{className:(0,w.A)("woocommerce-marketplace__category-dropdown-item-button",{"woocommerce-marketplace__category-dropdown-item-button--selected":t.slug===e.selected?.slug}),value:t.slug,onClick:o,children:t.label})},t.slug)))})}function ee(e){return(0,n.jsx)(h.Dropdown,{renderToggle:({isOpen:o,onToggle:t})=>(0,n.jsxs)("button",{onClick:()=>{o||(0,f.recordEvent)("marketplace_category_dropdown_opened",{type:e.type}),t()},className:e.buttonClassName,"aria-label":(0,u.__)("Toggle category dropdown","woocommerce"),children:[e.label,(0,n.jsx)(q.A,{icon:o?K.A:J.A,size:e.arrowIconSize})]}),className:e.className,renderContent:({onToggle:o})=>(0,n.jsx)(X,{categories:e.categories,selected:e.selected,onClick:o}),contentClassName:e.contentClassName})}const oe={[C.ch.extension]:"_all",[C.ch.theme]:"themes",[C.ch.businessService]:"business-services"};function te(e){const[o,t]=(0,r.useState)(),[c,s]=(0,r.useState)(!1),[a,i]=(0,r.useState)([]),[l,m]=(0,r.useState)(!1),[d,p]=(0,r.useState)("start"),_=(0,r.useRef)(null),w=(0,r.useRef)(null),x=(0,g.useQuery)();function b(){if(_.current&&_.current.parentElement?.scrollWidth){const e=_.current.scrollWidth>_.current.parentElement.scrollWidth;m(e)}}(0,r.useEffect)((()=>{s(!0),(0,S.jE)(e.type).then((e=>{const o=e.map((e=>({...e,selected:!1}))).filter((e=>"_featured"!==e.slug));i(o)})).catch((()=>{i([])})).finally((()=>{s(!1)}))}),[e.type,i]),(0,r.useEffect)((()=>{let o=oe[e.type];x.category&&(o=x.category);const r=a.find((e=>e.slug===o));r&&t(r)}),[x.category,e.type,a]),(0,r.useEffect)((()=>{w.current&&w.current.scrollIntoView({block:"nearest",inline:"center"})}),[o]);const y=(0,V.useDebounce)(b,300),v=(0,V.useDebounce)((function(){const e=_.current;if(!e)return;const{scrollLeft:o,scrollWidth:t,clientWidth:r}=e;o<10?p("start"):o+r<t?p("middle"):o+r===t&&p("end")}),100);function f(e){_.current&&_.current.scrollTo({left:_.current.scrollLeft+e,behavior:"smooth"})}return(0,r.useEffect)((()=>{window.addEventListener("resize",y);const e=_.current;return e&&e.addEventListener("scroll",v),()=>{window.removeEventListener("resize",y),e&&e.removeEventListener("scroll",v)}}),[y,v]),(0,r.useEffect)((()=>{b()}),[a]),c?(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("ul",{className:"woocommerce-marketplace__category-selector",children:[...Array(5)].map(((e,o)=>(0,n.jsx)("li",{className:"woocommerce-marketplace__category-item",children:(0,n.jsx)(Z,{slug:"",label:"",selected:!1})},o)))})}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("ul",{className:"woocommerce-marketplace__category-selector","aria-label":"Categories",ref:_,children:a.map((e=>(0,n.jsx)("li",{className:"woocommerce-marketplace__category-item",ref:e.slug===o?.slug?w:null,children:(0,n.jsx)(Z,{...e,selected:e.slug===o?.slug,"aria-current":e.slug===o?.slug})},e.slug)))}),(0,n.jsx)("div",{className:"woocommerce-marketplace__category-selector--full-width",children:(0,n.jsx)(ee,{type:e.type,label:function(){const e=(0,u.__)("All Categories","woocommerce");return o?"All"===o.label?e:o.label:e}(),categories:a,buttonClassName:"woocommerce-marketplace__category-dropdown-button",className:"woocommerce-marketplace__category-dropdown",contentClassName:"woocommerce-marketplace__category-dropdown-content",selected:o})}),l&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{onClick:function(){f(-200)},className:"woocommerce-marketplace__category-navigation-button woocommerce-marketplace__category-navigation-button--prev",hidden:"start"===d,"aria-label":"Scroll to previous categories",tabIndex:-1,children:(0,n.jsx)(h.Icon,{icon:"arrow-left-alt2"})}),(0,n.jsx)("button",{onClick:function(){f(200)},className:"woocommerce-marketplace__category-navigation-button woocommerce-marketplace__category-navigation-button--next",hidden:"end"===d,"aria-label":"Scroll to next categories",tabIndex:-1,children:(0,n.jsx)(h.Icon,{icon:"arrow-right-alt2"})})]})]})}function re(e){const[o,t]=(0,r.useState)(),[c,s]=(0,r.useState)(!1),a={[C.If.all]:["most-popular","popular-themes","business-services"],[C.If.theme]:["popular-themes"],[C.If.extension]:["most-popular"],[C.If.businessService]:["business-services"]};return(0,r.useEffect)((()=>{s(!0),(0,S.b8)().then((o=>{const r=a[e.type];if(!r)return;const c=o.filter((e=>r.includes(e.id))).map((e=>({...e,items:e.items.slice(0,4)})));c&&t(c)})).catch((()=>{t(void 0)})).finally((()=>{s(!1)}))}),[]),(0,n.jsxs)("div",{className:"woocommerce-marketplace__no-results",children:[function(){if(e.type===C.If.all)return(0,n.jsx)(n.Fragment,{});let o=C.ch.extension;return e.type===C.If.theme&&(o=C.ch.theme),e.type===C.If.businessService&&(o=C.ch.businessService),(0,n.jsx)(te,{type:o})}(),(0,n.jsxs)("div",{className:"woocommerce-marketplace__no-results__content",children:[(0,n.jsx)("h2",{className:"woocommerce-marketplace__no-results__heading",children:e.showHeading?e.heading:""}),(0,n.jsx)("p",{className:"woocommerce-marketplace__no-results__description",children:(0,u.__)("Try searching again using a different term, or take a look at our recommendations below.","woocommerce")})]}),(0,n.jsx)("div",{className:"woocommerce-marketplace__no-results__product-groups",children:c?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)($,{type:C.ch.extension,placeholderCount:4}),(0,n.jsx)($,{type:C.ch.theme,placeholderCount:4}),(0,n.jsx)($,{type:C.ch.businessService,placeholderCount:4})]}):o&&0!==o.length?(0,n.jsx)(n.Fragment,{children:o.map((e=>{return(0,n.jsx)(Y,{title:(o=e.id,"popular-themes"===o?(0,u.__)("Our favorite themes","woocommerce"):"business-services"===o?(0,u.__)("Services to help your business grow","woocommerce"):(0,u.__)("Most popular extensions","woocommerce")),description:e.description,products:e.items,groupURL:e.url,productGroup:e.id,type:e.itemType,groupURLText:e.url_text,groupURLType:e.url_type},e.id);var o}))}):(0,n.jsx)(n.Fragment,{})})]})}var ce=t(81392);const se={[C.ch.extension]:{label:(0,u.__)("extensions","woocommerce"),singularLabel:(0,u.__)("extension","woocommerce")},[C.ch.theme]:{label:(0,u.__)("themes","woocommerce"),singularLabel:(0,u.__)("theme","woocommerce")},[C.ch.businessService]:{label:(0,u.__)("business services","woocommerce"),singularLabel:(0,u.__)("business service","woocommerce")}};function ne(e){var o,t;const s=(0,r.useContext)(l),{isLoading:a}=s,i=se[e.type].label,m=(0,g.useQuery)(),d=m?.category,p=(0,H.useSelect)((e=>e("core").getCurrentTheme()),[]),_="twentytwentyfour"===p?.stylesheet,[x,b]=(0,r.useState)(!1),y=(0,M.addQueryArgs)(`${c.kY}admin.php`,{page:"wc-admin",path:"/customize-store/design"}),v=(0,M.addQueryArgs)(`${c.kY}admin.php`,{page:"wc-admin",path:"/customize-store/assembler-hub"}),f=(0,H.useSelect)((e=>e(A.onboardingStore).getTask("customize-store")),[]),k=null!==(o=e.showAllButton)&&void 0!==o&&o,j=null!==(t=e.products)&&void 0!==t?t:[],N="business services"===i?"business-services":i,T="woocommerce-marketplace__search-",I=(0,w.A)(T+N),S=(0,w.A)("woocommerce-marketplace__view-all-button",T+"button-"+N);if(a)return(0,n.jsxs)(n.Fragment,{children:[e.categorySelector&&(0,n.jsx)(te,{type:e.type}),(0,n.jsx)($,{hasTitle:!1,type:e.type})]});if(0===j.length){let o=C.If.all;switch(e.type){case C.ch.extension:o=C.If.extension;break;case C.ch.theme:o=C.If.theme;break;case C.ch.businessService:o=C.If.businessService}return(0,n.jsx)(re,{type:o,showHeading:!1})}const E=(0,w.A)(k?"woocommerce-marketplace__product-list-content--collapsed":"");return(0,n.jsxs)("div",{className:I,children:[(0,n.jsxs)("nav",{className:"woocommerce-marketplace__sub-header",children:[(0,n.jsx)("div",{className:"woocommerce-marketplace__sub-header__categories",children:e.categorySelector&&(0,n.jsx)(te,{type:e.type})}),"theme"===e.type&&(0,n.jsx)(h.Button,{className:"woocommerce-marketplace__customize-your-store-button",variant:"secondary",text:(0,u.__)("Design your own","woocommerce"),onClick:()=>{_?window.location.href=f?.isComplete?v:y:b(!0)}})]}),x&&(0,n.jsx)(ce.z,{setIsModalOpen:b,redirectToCYSFlow:()=>{window.location.href=y}}),(0,n.jsx)(Q,{products:j,type:e.type,className:E,searchTerm:e.searchTerm,category:d}),"theme"===e.type&&(0,n.jsxs)("div",{className:"woocommerce-marketplace__browse-wp-theme-directory",children:[(0,n.jsx)("b",{children:(0,u.__)("Didn’t find a theme you like?","woocommerce")}),(0,r.createInterpolateElement)((0,u.__)(" Browse the <a>WordPress.org theme directory</a> to discover more.","woocommerce"),{a:(0,n.jsx)("a",{href:c.kY+"theme-install.php?search=e-commerce"})})]}),k&&(0,n.jsx)(h.Button,{className:S,variant:"secondary",text:(0,u.__)("View all","woocommerce"),onClick:()=>{return o=e.type,void(0,g.navigateTo)({url:(0,g.getNewPath)({section:o})});var o}})]})}var ae=t(6513),ie=t(1455),le=t.n(ie),me=t(29248);const ue={subscriptions:[],setSubscriptions:()=>{},loadSubscriptions:()=>Promise.resolve(),refreshSubscriptions:()=>Promise.resolve(),isLoading:!0,setIsLoading:()=>{}},de=(0,r.createContext)(ue);function pe(e){const[o,t]=(0,r.useState)([]),[c,s]=(0,r.useState)(!0),a=e=>(!0===e&&s(!0),(0,S.hP)().then((e=>{t(e)})).finally((()=>{e&&s(!1)}))),i=e=>(e&&s(!0),(0,S.wu)().then((e=>{t(e)})).catch((e=>{throw e})).finally((()=>{e&&s(!1)})));(0,r.useEffect)((()=>{new URLSearchParams(window.location.search).get("install")?i(!0).catch((e=>{(0,S.Mp)("woocommerce-marketplace-refresh-subscriptions",(0,u.sprintf)((0,u.__)("Error refreshing subscriptions: %s","woocommerce"),e.message),me.T.Error)})):a(!0).catch((e=>{(0,S.Mp)("woocommerce-marketplace-load-subscriptions",(0,u.sprintf)((0,u.__)("Error loading subscriptions: %s","woocommerce"),e.message),me.T.Error)}))}),[]);const l={subscriptions:o,setSubscriptions:t,loadSubscriptions:a,refreshSubscriptions:i,isLoading:c,setIsLoading:s};return(0,n.jsx)(de.Provider,{value:l,children:e.children})}const _e=[{key:"name",label:(0,u.__)("Name","woocommerce")},{key:"expiry",label:(0,u.__)("Expires/Renews on","woocommerce")},{key:"subscription",label:(0,u.__)("Subscription","woocommerce")},{key:"version",label:(0,u.__)("Version","woocommerce")}];function he(e){if(e.isLoading)return(0,n.jsx)(E.TablePlaceholder,{caption:(0,u.__)("Loading your subscriptions","woocommerce"),headers:e.headers});const o=e.headers.map((e=>({...e,cellClassName:"woocommerce-marketplace__my-subscriptions__table__header--"+e.key})));return(0,n.jsx)(E.Table,{className:"woocommerce-marketplace__my-subscriptions__table",headers:o,rows:e.rows})}function we(e){const o=[..._e,{key:"actions",label:(0,u.__)("Actions","woocommerce")}];if(!(e.isLoading||e.rows&&0!==e.rows.length)){const e=(0,g.getNewPath)({},s.q$,{}),o=(0,r.createInterpolateElement)((0,u.__)("No extensions or themes installed. <a>Browse the Marketplace</a>","woocommerce"),{a:(0,n.jsx)("a",{href:e})});return(0,n.jsx)(E.EmptyTable,{numberOfRows:4,children:o})}return(0,n.jsx)(he,{rows:e.rows,isLoading:e.isLoading,headers:o})}function ge(e){const o=[..._e,{key:"actions",label:(0,u.__)("Actions","woocommerce")}];return(0,n.jsx)(he,{rows:e.rows,isLoading:e.isLoading,headers:o})}var xe=t(38443),be=t(28180);let ye=function(e){return e.Warning="warning",e.Error="error",e.Info="info",e}({});const ve=["maxed_out","invalid_product_key","invalid_product"];function fe(e,o){(0,f.recordEvent)("marketplace_product_connect_error_action_clicked",{action:e,error_code:o})}var ke=t(12974);function je(e){var o;const[t,c]=(0,r.useState)(!1),[a,i]=(0,r.useState)(!1),{loadSubscriptions:l}=(0,r.useContext)(de),m=()=>{c(!0),i(!1),l(!1).then((()=>{(0,S.Mp)(e.subscription.product_key,(0,u.sprintf)((0,u.__)("%s successfully connected.","woocommerce"),e.subscription.product_name),me.T.Success),c(!1),e.onClose&&e.onClose()})).catch((()=>{c(!1)}))},d=()=>{(0,f.recordEvent)("marketplace_product_connect_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id}),c(!0),i(!1),(0,S.fj)(e.subscription.product_key),(0,S.e2)(e.subscription).then((()=>{if(e.subscription.local.installed&&!e.subscription.local.active&&"plugin"===e.subscription.local.type)return c(!1),void i(!0);m()})).catch((o=>{const t=o,r=(0,u.sprintf)((0,u.__)("%s couldn’t be connected.","woocommerce"),e.subscription.product_name),n=function(e,o){const t=e?.data?.code||"";if("maxed_out"===t){var r;const t=e?.data?.data?.sites_list||[],c=Number(null!==(r=e?.data?.data?.total_domains)&&void 0!==r?r:t.length);if(c>=2){const e=t[0]||"",r=t[1]||"";if(2===c)return o+" "+(0,u.sprintf)((0,u.__)("This subscription is maxed out as it's connected to %1$s and %2$s.","woocommerce"),e,r);const s=c-2;return o+" "+(0,u.sprintf)((0,u._n)("This subscription is maxed out as it's connected to %1$s, %2$s, and %3$d other site.","This subscription is maxed out as it's connected to %1$s, %2$s, and %3$d other sites.",s,"woocommerce"),e,r,s)}}if("invalid_product_key"===t)return o+" "+(0,u.__)("The product key is invalid. Please contact support for assistance.","woocommerce");if("invalid_product"===t)return o+" "+(0,u.__)("We are unable to activate the subscription at this time. Please try again later.","woocommerce");if(ve.includes(t)){const t=e?.data?.message||"";return t?o+" "+t:o}return o}(t,r),a=function(e){const o=e?.data?.code||"";return"maxed_out"===o?{label:(0,u.__)("Manage subscriptions","woocommerce"),onClick:()=>{fe("manage_subscriptions",o),window.location.assign(s.t0)}}:"invalid_product_key"===o?{label:(0,u.__)("Contact support","woocommerce"),onClick:()=>{fe("contact_support",o),window.location.assign(s.a$)}}:null}(t),i=a?[a]:[{label:(0,u.__)("Try again","woocommerce"),onClick:()=>{fe("try_again",t.data?.code||""),d()}}];(0,S.Mp)(e.subscription.product_key,n,me.T.Error,{actions:i}),c(!1),e.onClose&&e.onClose()}))};return(0,n.jsxs)(n.Fragment,{children:[a?(0,n.jsxs)(h.Modal,{title:(0,u.__)("Activate the Plugin","woocommerce"),onRequestClose:()=>m(),focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[(0,n.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:(0,n.jsx)("span",{dangerouslySetInnerHTML:(0,ke.Ay)((0,u.sprintf)((0,u.__)("<b>%s</b> is installed but not activated on this store. Would you like to activate it now?","woocommerce"),e.subscription.product_name))})}),(0,n.jsxs)(h.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:[(0,n.jsx)(h.Button,{onClick:()=>m(),variant:"tertiary",className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("No","woocommerce")}),(0,n.jsx)(h.Button,{onClick:()=>{c(!0),(0,S.CW)(e.subscription).then((()=>{m()})).catch((()=>{(0,S.Mp)(e.subscription.product_key,(0,u.sprintf)((0,u.__)("%s is connected to WooCommerce.com but failed to activate the local plugin.","woocommerce"),e.subscription.product_name),me.T.Error)})),i(!1)},variant:"primary",children:(0,u.__)("Yes","woocommerce")})]})]}):null,(0,n.jsx)(h.Button,{onClick:d,variant:null!==(o=e.variant)&&void 0!==o?o:"secondary",isBusy:t,disabled:t,children:(0,u.__)("Connect","woocommerce")})]})}const Ne={installingProducts:[]},Ce=(0,H.createReduxStore)("woocommerce-admin/installing",{reducer(e=Ne,o){switch(o.type){case"START_INSTALLING":return{...e,installingProducts:[...e.installingProducts,o.productKey]};case"STOP_INSTALLING":return{...e,installingProducts:[...e.installingProducts.filter((e=>e!==o.productKey))]}}return e},actions:{startInstalling:e=>({type:"START_INSTALLING",productKey:e}),stopInstalling:e=>({type:"STOP_INSTALLING",productKey:e})},selectors:{isInstalling:(e,o)=>!!e&&e.installingProducts.includes(o)}});function Me(e){var o;const{loadSubscriptions:t}=(0,r.useContext)(de),c=(0,H.useSelect)((o=>o(Ce).isInstalling(e.subscription.product_key)),[e.subscription.product_key]),s=()=>{(0,H.dispatch)(Ce).stopInstalling(e.subscription.product_key)},a=o=>{t(!1).then((()=>{let t=(0,u.sprintf)((0,u.__)("%s couldn’t be installed.","woocommerce"),e.subscription.product_name);!1===o?.success&&o?.data.message&&(t+=" "+o.data.message),(0,S.Mp)(e.subscription.product_key,t,me.T.Error,{actions:[{label:(0,u.__)("Download and install manually","woocommerce"),url:"https://woocommerce.com/my-account/downloads/",onClick:()=>{}}]}),s(),e.onError&&e.onError()})),(0,f.recordEvent)("marketplace_product_install_failed",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_current_version:e.subscription.version,error_message:o?.data?.message})};return(0,n.jsx)(h.Button,{variant:null!==(o=e.variant)&&void 0!==o?o:"link",isBusy:c,disabled:c,onClick:()=>{(0,f.recordEvent)("marketplace_product_install_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_current_version:e.subscription.version}),(0,H.dispatch)(Ce).startInstalling(e.subscription.product_key),(0,S.fj)(e.subscription.product_key),e.subscription.is_installable?(0,S.dV)(e.subscription).then((()=>{t(!1).then((()=>{(0,S.Mp)(e.subscription.product_key,(0,u.sprintf)((0,u.__)("%s successfully installed.","woocommerce"),e.subscription.product_name),me.T.Success),s()})),(0,f.recordEvent)("marketplace_product_installed",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_current_version:e.subscription.version}),e.onSuccess&&e.onSuccess()})).catch(a):(0,S.Xs)(e.subscription).then((o=>{if((0,f.recordEvent)("marketplace_product_install_url",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_current_version:e.subscription.version,product_install_url:o}),s(),!o)throw new Error;window.open(o,"_self")})).catch(a)},children:(0,u.__)("Install","woocommerce")})}function Te(e){var o;return(0,n.jsx)(h.Button,{href:(0,S.LK)(e.subscription),variant:null!==(o=e.variant)&&void 0!==o?o:"secondary",onClick:function(){(0,f.queueRecordEvent)("marketplace_renew_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id})},children:(0,u.__)("Renew","woocommerce")})}function Ae(e){var o;return(0,n.jsx)(h.Button,{href:(0,S.SI)(e.subscription),variant:null!==(o=e.variant)&&void 0!==o?o:"secondary",onClick:function(){(0,f.queueRecordEvent)("marketplace_auto_renew_button_clicked",{order_id:e.subscription.order_id,product_id:e.subscription.product_id})},children:(0,u.__)("Renew","woocommerce")})}function Ie(e){var o;return(0,n.jsx)(h.Button,{href:(0,S.wX)(e.subscription),variant:null!==(o=e.variant)&&void 0!==o?o:"secondary",onClick:function(){(0,f.queueRecordEvent)("marketplace_subscribe_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id})},children:(0,u.__)("Subscribe","woocommerce")})}function Se(e){return(0,n.jsxs)(h.Modal,{title:(0,u.__)("Connect to update","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[(0,n.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:(0,u.sprintf)((0,u.__)("Version %s is available. To enable this update you need to connect your subscription to this store.","woocommerce"),e.subscription.version)}),(0,n.jsxs)(h.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:[(0,n.jsx)(h.Button,{variant:"tertiary",onClick:e.onClose,className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("Cancel","woocommerce")}),(0,n.jsx)(je,{subscription:e.subscription,onClose:e.onClose,variant:"primary"})]})]})}function Ee(e){return(0,n.jsxs)(h.Modal,{title:(0,u.__)("Renew to update","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[(0,n.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:(0,u.sprintf)((0,u.__)("Version %s is available. To enable this update you need to renew your subscription.","woocommerce"),e.subscription.version)}),(0,n.jsxs)(h.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:[(0,n.jsx)(h.Button,{variant:"tertiary",onClick:e.onClose,className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("Cancel","woocommerce")}),(0,n.jsx)(Te,{subscription:e.subscription,variant:"primary"})]})]})}function Le(e){return(0,n.jsxs)(h.Modal,{title:(0,u.__)("Subscribe to update","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[(0,n.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:(0,u.sprintf)((0,u.__)("Version %s is available. To enable this update you need to purchase a subscription.","woocommerce"),e.subscription.version)}),(0,n.jsxs)(h.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:[(0,n.jsx)(h.Button,{variant:"tertiary",onClick:e.onClose,className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("Cancel","woocommerce")}),(0,n.jsx)(Ie,{subscription:e.subscription,variant:"primary"})]})]})}function De(e){const o=(0,c.Qk)("wccomHelper",{});return o?.wooUpdateManagerInstalled?o?.wooUpdateManagerActive?null:(0,n.jsxs)(h.Modal,{title:(0,u.__)("Access your updates","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[(0,n.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:(0,n.jsx)("span",{dangerouslySetInnerHTML:(0,ke.Ay)((0,u.sprintf)((0,u.__)("Version %s is available. To access this update, please <b>activate the WooCommerce.com Update Manager</b> extension.","woocommerce"),e.subscription.version))})}),(0,n.jsxs)(h.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:[(0,n.jsx)(h.Button,{onClick:e.onClose,variant:"link",children:(0,u.__)("Cancel","woocommerce")}),(0,n.jsx)(h.Button,{href:s.dL,variant:"primary",children:(0,u.__)("Activate","woocommerce")})]})]}):(0,n.jsxs)(h.Modal,{title:(0,u.__)("Access your updates","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[(0,n.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:(0,n.jsx)("span",{dangerouslySetInnerHTML:(0,ke.Ay)((0,u.sprintf)((0,u.__)("Version %s is available. To access this update, please first <b>install the WooCommerce.com Update Manager</b> extension. Alternatively, you can download and install it manually.","woocommerce"),e.subscription.version))})}),(0,n.jsxs)(h.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:[(0,n.jsx)(h.Button,{href:s.K_,variant:"secondary",children:(0,u.__)("Download","woocommerce")}),(0,n.jsx)(h.Button,{href:o?.wooUpdateManagerInstallUrl,variant:"primary",children:(0,u.__)("Install","woocommerce")})]})]})}function ze(e){const[o,t]=(0,r.useState)(!1),[c,s]=(0,r.useState)(!1),{loadSubscriptions:a}=(0,r.useContext)(de),i=e.subscription.active&&e.subscription.local&&e.subscription.local.slug&&e.subscription.local.path&&e.wooUpdateManagerActive;return(0,n.jsxs)(n.Fragment,{children:[o?""===e.subscription.product_key?(0,n.jsx)(Le,{onClose:()=>t(!1),subscription:e.subscription}):e.subscription.expired?(0,n.jsx)(Ee,{subscription:e.subscription,onClose:()=>t(!1)}):e.subscription.active?e.wooUpdateManagerActive?null:(0,n.jsx)(De,{subscription:e.subscription,onClose:()=>t(!1)}):(0,n.jsx)(Se,{subscription:e.subscription,onClose:()=>t(!1)}):null,(0,n.jsx)(h.Button,{variant:"link",className:"woocommerce-marketplace__my-subscriptions-update",onClick:function o(){(0,f.recordEvent)("marketplace_product_update_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_installed_version:e.subscription.local.installed,product_current_version:e.subscription.version}),i?((0,S.fj)(e.subscription.product_key),window.wp.updates?(s(!0),(0,S.vc)(e.subscription).then((()=>{a(!1).then((()=>{(0,S.Mp)(e.subscription.product_key,(0,u.sprintf)((0,u.__)("%s updated successfully.","woocommerce"),e.subscription.product_name),me.T.Success),s(!1)})),(0,f.recordEvent)("marketplace_product_updated",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_installed_version:e.subscription.local.installed,product_current_version:e.subscription.version})})).catch((()=>{(0,S.Mp)(e.subscription.product_key,(0,u.sprintf)((0,u.__)("%s couldn’t be updated.","woocommerce"),e.subscription.product_name),me.T.Error,{actions:[{label:(0,u.__)("Try again","woocommerce"),onClick:o,url:""}]}),s(!1),(0,f.recordEvent)("marketplace_product_update_failed",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_installed_version:e.subscription.local.installed,product_current_version:e.subscription.version})}))):(0,S.Mp)(e.subscription.product_key,(0,u.sprintf)((0,u.__)("%s couldn’t be updated.","woocommerce"),e.subscription.product_name),me.T.Error,{actions:[{label:(0,u.__)("Reload page and try again","woocommerce"),onClick:()=>{window.location.reload()},url:""}]})):t(!0)},isBusy:c,disabled:c,label:(0,u.sprintf)((0,u.__)("Update to %s","woocommerce"),e.subscription.version),showTooltip:!0,tooltipPosition:"top center",children:c?(0,u.__)("Updating","woocommerce"):(0,u.__)("Update","woocommerce")})]})}function Oe(e){const[o,t]=(0,r.useState)(!1),[c,s]=(0,r.useState)(!1),a=(0,r.useRef)(null);(0,r.useEffect)((()=>()=>{a.current&&clearTimeout(a.current)}),[]);const i=()=>{e.explanationOnHover&&(a.current&&clearTimeout(a.current),t(!0))},l=()=>{e.explanationOnHover&&(a.current&&clearTimeout(a.current),a.current=setTimeout((()=>{t(!1)}),350))};return(0,n.jsxs)("button",{onClick:()=>s(!c),onMouseOver:i,onFocus:i,onMouseOut:l,onBlur:l,className:(0,w.A)("woocommerce-marketplace__my-subscriptions__product-status",`woocommerce-marketplace__my-subscriptions__product-status--${e.level}`),children:[e.text,""!==e.explanation&&(c||e.explanationOnHover&&o)&&(0,n.jsx)(h.Popover,{className:"woocommerce-marketplace__my-subscriptions__popover",position:"top center",focusOnMount:!1,onMouseOver:i,onMouseOut:l,onFocus:i,onBlur:l,children:e.explanation})]})}(0,H.register)(Ce);var Ue=t(38150);function Pe(e){const o=[{title:(0,u.__)("Manage in Plugins","woocommerce"),onClick:()=>{window.location.href=c.kY+"plugins.php"}}];return e.subscription.is_shared||o.unshift({title:(0,u.__)("Manage on WooCommerce.com","woocommerce"),onClick:()=>{window.open("https://woocommerce.com/my-account/my-subscriptions","_blank")}}),e.subscription.documentation_url&&o.unshift({title:(0,u.__)("View documentation","woocommerce"),onClick:()=>{window.open(e.subscription.documentation_url,"_blank")}}),(0,n.jsx)(h.DropdownMenu,{icon:Ue.A,label:(0,u.__)("Actions","woocommerce"),controls:o})}function Be(e){return(0,n.jsx)("span",{className:"woocommerce-marketplace__my-subscriptions-version",children:e.span})}function Qe(e,o){const t=(0,c.Qk)("wccomHelper",{});return e.local.version===e.version?(0,n.jsx)(Be,{span:e.local.version}):e.local.version&&e.version&&"installed"===o?(0,n.jsx)(ze,{subscription:e,wooUpdateManagerActive:t?.wooUpdateManagerActive}):e.version?(0,n.jsx)(Be,{span:e.version}):e.local.version?(0,n.jsx)(Be,{span:e.local.version}):""}function Re(e){return(0,S.A5)(e,[["utm_source","subscriptionsscreen"],["utm_medium","product"],["utm_campaign","wcaddons"],["utm_content","product-name"]])}function Ye(e){let o=(0,n.jsx)(q.A,{icon:be.A,size:40});return e.product_icon&&(o=(0,n.jsx)("img",{src:e.product_icon,alt:(0,u.sprintf)((0,u.__)("%s icon","woocommerce"),e.product_name)})),{display:(0,n.jsxs)("div",{className:"woocommerce-marketplace__my-subscriptions__product",children:[(0,n.jsx)("a",{href:Re(e.product_url),target:"_blank",rel:"noreferrer",children:(0,n.jsx)("span",{className:"woocommerce-marketplace__my-subscriptions__product-icon",children:o})}),(0,n.jsx)("a",{href:Re(e.product_url),className:"woocommerce-marketplace__my-subscriptions__product-name",target:"_blank",rel:"noreferrer",children:e.product_name}),(0,n.jsx)("span",{className:"woocommerce-marketplace__my-subscriptions__product-statuses",children:e.is_shared&&(0,n.jsx)(Oe,{text:(0,u.__)("Shared with you","woocommerce"),level:ye.Info,explanation:(0,r.createInterpolateElement)((0,u.sprintf)((0,u.__)("This subscription was shared by <email>%s</email>. <link>Learn more</link>.","woocommerce"),e.owner_email),{email:(0,n.jsx)("strong",{style:{overflowWrap:"anywhere"},children:"email"}),link:(0,n.jsx)("a",{href:s.$Y,rel:"nofollow noopener noreferrer",children:"Learn more"})})})})]}),value:e.product_name}}function $e(e){const o=e.expires;if(!0===e.local.installed&&""===e.product_key)return{display:"",value:""};let t=(0,u.__)("Never expires","woocommerce");return o&&(t=(0,xe.gmdateI18n)("j M, Y",new Date(1e3*o))),{display:(0,n.jsx)("span",{className:"woocommerce-marketplace__my-subscriptions__expiry-date",children:t}),value:o}}function We(e,o){return{display:function(){const t=function(e,o){return""===e.product_key?{text:(0,u.__)("No subscription","woocommerce"),level:ye.Error,explanation:(0,r.createInterpolateElement)((0,u.__)("To receive updates and support, please <purchase>purchase</purchase> a subscription or use a subscription from another account by <sharing>sharing</sharing> or <transferring>transferring</transferring>.","woocommerce"),{purchase:(0,n.jsx)("a",{href:(0,S.wX)(e),rel:"nofollow noopener noreferrer",children:"renew"}),sharing:(0,n.jsx)("a",{href:s.$Y,rel:"nofollow noopener noreferrer",children:"sharing"}),transferring:(0,n.jsx)("a",{href:s.UD,rel:"nofollow noopener noreferrer",children:"sharing"})})}:e.expired?{text:(0,u.__)("Expired","woocommerce"),level:ye.Error,explanation:(0,r.createInterpolateElement)((0,u.__)("To receive updates and support, please <renew>renew</renew> this subscription or use a subscription from another account by <sharing>sharing</sharing> or <transferring>transferring</transferring>.","woocommerce"),{renew:(0,n.jsx)("a",{href:(0,S.LK)(e),rel:"nofollow noopener noreferrer",children:"renew"}),sharing:(0,n.jsx)("a",{href:s.$Y,rel:"nofollow noopener noreferrer",children:"sharing"}),transferring:(0,n.jsx)("a",{href:s.UD,rel:"nofollow noopener noreferrer",children:"sharing"})})}:e.expiring&&!e.autorenew?{text:(0,u.__)("Expires soon","woocommerce"),level:ye.Error,explanation:(0,r.createInterpolateElement)((0,u.__)("To receive updates and support, please <renew>renew</renew> this subscription before it expires or use a subscription from another account by <sharing>sharing</sharing> or <transferring>transferring</transferring>.","woocommerce"),{renew:(0,n.jsx)("a",{href:(0,S.SI)(e),rel:"nofollow noopener noreferrer",children:"renew"}),sharing:(0,n.jsx)("a",{href:s.$Y,rel:"nofollow noopener noreferrer",children:"sharing"}),transferring:(0,n.jsx)("a",{href:s.UD,rel:"nofollow noopener noreferrer",children:"sharing"})})}:!("installed"!==o||!e.local.installed||e.active)&&{text:(0,u.__)("Not connected","woocommerce"),level:ye.Warning,explanation:(0,u.__)("To receive updates and support, please connect your subscription to this store.","woocommerce")}}(e,o);var c;if(t)return(0,n.jsx)(Oe,{text:t.text,level:t.level,explanation:null!==(c=t.explanation)&&void 0!==c?c:"",explanationOnHover:!0});let a;return a=e.lifetime?(0,u.__)("Lifetime","woocommerce"):e.autorenew?(0,u.__)("Active","woocommerce"):(0,u.__)("Cancelled","woocommerce"),a}()}}function Fe(e,o){return{display:Qe(e,o)}}function Ge(e){let o=null;return""===e.product_key?o=(0,n.jsx)(Ie,{subscription:e}):e.expired&&!e.lifetime?o=(0,n.jsx)(Te,{subscription:e}):!1===e.local.installed&&!1===e.subscription_installed?o=(0,n.jsx)(Me,{subscription:e}):!1===e.active&&!0===e.subscription_available?o=(0,n.jsx)(je,{subscription:e,variant:"link"}):e.autorenew||e.lifetime||(o=(0,n.jsx)(Ae,{subscription:e})),{display:(0,n.jsxs)("div",{className:"woocommerce-marketplace__my-subscriptions__actions",children:[o,(0,n.jsx)(Pe,{subscription:e})]})}}function He(e,o){return[Ye(e),$e(e),We(e,o),Fe(e,o),Ge(e)]}const Ve="data:image/svg+xml;base64,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",Ze="woocommerce-marketplace-refresh-subscriptions";function qe(){const{refreshSubscriptions:e}=(0,r.useContext)(de),[o,t]=(0,r.useState)(!1);return(0,n.jsxs)(h.Button,{className:"woocommerce-marketplace__refresh-subscriptions",onClick:()=>{o||((0,S.fj)(Ze),t(!0),e().then((()=>{(0,S.Mp)(Ze,(0,u.__)("Subscriptions refreshed.","woocommerce"),me.T.Success)})).catch((e=>{(0,S.Mp)(Ze,(0,u.sprintf)((0,u.__)("Error refreshing subscriptions: %s","woocommerce"),e.data.message),me.T.Error)})).finally((()=>{t(!1)})))},isBusy:o,children:[(0,n.jsx)("img",{src:Ve,alt:(0,u.__)("Refresh subscriptions","woocommerce"),className:"woocommerce-marketplace__refresh-subscriptions-icon"}),(0,u.__)("Refresh","woocommerce")]})}const Ke="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9ImFsZXJ0Ij4KPHBhdGggaWQ9IlZlY3RvciIgZD0iTTEyIDIwQzE2LjQxODMgMjAgMjAgMTYuNDE4MyAyMCAxMkMyMCA3LjU4MTcyIDE2LjQxODMgNCAxMiA0QzcuNTgxNzIgNCA0IDcuNTgxNzIgNCAxMkM0IDE2LjQxODMgNy41ODE3MiAyMCAxMiAyMFoiIHN0cm9rZT0iI0NDMTgxOCIgc3Ryb2tlLXdpZHRoPSIxLjUiLz4KPHBhdGggaWQ9IlZlY3Rvcl8yIiBkPSJNMTMgN0gxMVYxM0gxM1Y3WiIgZmlsbD0iI0NDMTgxOCIvPgo8cGF0aCBpZD0iVmVjdG9yXzMiIGQ9Ik0xMyAxNUgxMVYxN0gxM1YxNVoiIGZpbGw9IiNDQzE4MTgiLz4KPC9nPgo8L3N2Zz4K";var Je=t(65287);function Xe(){const e=(0,H.useSelect)((e=>e(Je.E).notices()),[]),o=e=>e.options?.actions?e.options?.actions.map((e=>({...e,variant:"link",className:"is-link"}))):[],t=[];for(const r of e)t.push((0,n.jsxs)(h.Notice,{className:"woocommerce-marketplace__notice--error",status:r.status,onRemove:()=>(0,S.fj)(r.productKey),actions:o(r),children:[(0,n.jsx)("img",{src:Ke,alt:"",width:24,height:24}),r.message]},r.productKey));return(0,n.jsx)(n.Fragment,{children:t})}function eo(e){var o;const t=new URL((0,S.E4)());return e.install&&t.searchParams.set("install",e.install),(0,n.jsx)(h.Button,{href:t.href,variant:null!==(o=e.variant)&&void 0!==o?o:"secondary",children:(0,u.__)("Connect Account","woocommerce")})}function oo(){const e=(0,g.useQuery)(),o=e?.install,t=(0,c.Qk)("wccomHelper",{}),a=!!t?.isConnected,[i,l]=(0,r.useState)(!1),[m,d]=(0,r.useState)(!1),{subscriptions:p,isLoading:_}=(0,r.useContext)(de),w=p.find((e=>e.product_key===o)),x=(0,r.useCallback)((()=>{(0,g.navigateTo)({url:(0,g.getNewPath)({...e,install:void 0},s.q$,{})})}),[e]);(0,r.useEffect)((()=>{_||(o&&a&&!_&&!w?((0,S.Mp)(o,(0,u.sprintf)((0,u.__)("Could not find subscription with product key %s.","woocommerce"),o),me.T.Error),x()):l(!!o))}),[a,_,o,x,w]),(0,r.useEffect)((()=>{w&&w.local.installed&&d(!0)}),[w]);const b=()=>{x(),l(!1)};return i?(0,n.jsxs)(h.Modal,{title:m?(0,u.__)("You are ready to go!","woocommerce"):(0,u.__)("Add to store","woocommerce"),onRequestClose:b,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal has-size-medium",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[(()=>{if(!a)return(0,n.jsx)(h.Notice,{status:"warning",isDismissible:!1,children:(0,u.__)("In order to install a product, you need to first connect your account.","woocommerce")});if(w){const e=m?(0,u.__)("Keep the momentum going and start setting up your extension.","woocommerce"):(0,u.__)("Would you like to install this extension?","woocommerce");return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:e}),(0,n.jsx)(P,{product:(0,S.w5)(w),small:!0,tracksData:{position:1,group:"subscriptions",label:"install"}})]})}})(),(()=>{const e=[];return m?(e.push((0,n.jsx)(h.Button,{variant:"secondary",href:w?.documentation_url,target:"_blank",className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("View docs","woocommerce")},"docs")),e.push((0,n.jsx)(h.Button,{variant:"primary",href:s.dL,className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("View in Plugins","woocommerce")},"plugin-list"))):(e.push((0,n.jsx)(h.Button,{variant:"tertiary",onClick:b,className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("Cancel","woocommerce")},"cancel")),a?w&&e.push((0,n.jsx)(Me,{subscription:w,variant:"primary",onError:b},"install")):e.push((0,n.jsx)(eo,{variant:"primary",install:o},"connect"))),(0,n.jsx)(h.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:e})})()]}):null}var to=t(25595);function ro(){const{subscriptions:e,isLoading:o}=(0,r.useContext)(de),t=(0,c.Qk)("wccomHelper",{}),s=(0,r.createInterpolateElement)((0,u.__)("WooCommerce.com extensions and themes installed on this store. To see all your subscriptions go to <a>your account<custom_icon /></a> on WooCommerce.com.","woocommerce"),{a:(0,n.jsx)("a",{href:"https://woocommerce.com/my-account/my-subscriptions",target:"_blank",rel:"nofollow noopener noreferrer",children:"your account"}),custom_icon:(0,n.jsx)(q.A,{icon:ae.A,size:12})}),a=e.filter((e=>e.subscription_installed)),i=e.filter((e=>!e.subscription_installed&&t?.wooUpdateManagerPluginSlug!==e.product_slug&&!e.maxed));if(!t?.isConnected){const e=(0,u.__)("Connect your WooCommerce.com account to get product updates, manage your subscriptions from your store admin, and get streamlined support.","woocommerce"),o=()=>{const e={notice_id:"woo-disconnect-notice",dismiss_notice_nonce:t?.dismissNoticeNonce||""};le()({path:"/wc-admin/notice/dismiss",method:"POST",data:e}),localStorage.setItem("wc-marketplaceNoticeClosed-woo-disconnect-notice","false")};return(0,n.jsxs)(n.Fragment,{children:[t?.disconnected_notice&&(0,n.jsx)(to.A,{id:"woo-disconnect-notice",description:t?.disconnected_notice,isDismissible:!0,variant:"info",onClose:o}),(0,n.jsxs)("div",{className:"woocommerce-marketplace__my-subscriptions--connect",children:[(0,n.jsx)(oo,{}),(0,n.jsx)("div",{className:"woocommerce-marketplace__my-subscriptions__icon"}),(0,n.jsx)("h2",{className:"woocommerce-marketplace__my-subscriptions__header",children:(0,u.__)("Connect your WooCommerce.com account","woocommerce")}),(0,n.jsx)("p",{className:"woocommerce-marketplace__my-subscriptions__description",children:e}),(0,n.jsx)(h.Button,{href:(0,S.E4)(),variant:"primary",children:(0,u.__)("Connect","woocommerce")})]})]})}return(0,n.jsxs)(n.Fragment,{children:[t?.connected_notice&&(0,n.jsx)(to.A,{id:"woo-connect-notice",description:t?.connected_notice,isDismissible:!0,variant:"success",onClose:()=>{const e={notice_id:"woo-connect-notice",dismiss_notice_nonce:t?.dismissNoticeNonce||""};le()({path:"/wc-admin/notice/dismiss",method:"POST",data:e}),localStorage.setItem("wc-marketplaceNoticeClosed-woo-connect-notice","false")}}),!t?.has_host_plan_orders&&t?.connection_url_notice&&(0,n.jsx)(to.A,{id:"woo-connection-url-notice",description:t?.connection_url_notice,isDismissible:!1,variant:"error",children:(0,n.jsx)(h.Button,{href:(0,S.E4)("wc-admin",!0),variant:"secondary",children:(0,u.__)("Reconnect","woocommerce")})}),(0,n.jsxs)("div",{className:"woocommerce-marketplace__my-subscriptions",children:[(0,n.jsx)(oo,{}),(0,n.jsx)("section",{className:"woocommerce-marketplace__my-subscriptions__notices",children:(0,n.jsx)(Xe,{})}),(0,n.jsxs)("section",{className:"woocommerce-marketplace__my-subscriptions-section woocommerce-marketplace__my-subscriptions__installed",children:[(0,n.jsxs)("header",{className:"woocommerce-marketplace__my-subscriptions__header",children:[(0,n.jsxs)("div",{className:"woocommerce-marketplace__my-subscriptions__header-content",children:[(0,n.jsx)("h2",{className:"woocommerce-marketplace__my-subscriptions__heading",children:(0,u.__)("Installed on this store","woocommerce")}),(0,n.jsx)("p",{className:"woocommerce-marketplace__my-subscriptions__table-description",children:s})]}),(0,n.jsx)("div",{className:"woocommerce-marketplace__my-subscriptions__header-refresh",children:(0,n.jsx)(qe,{})})]}),(0,n.jsx)("div",{className:"woocommerce-marketplace__my-subscriptions__table-wrapper",children:(0,n.jsx)(we,{isLoading:o,rows:a.map((e=>He(e,"installed")))})})]}),i.length>0&&(0,n.jsxs)("section",{className:"woocommerce-marketplace__my-subscriptions-section woocommerce-marketplace__my-subscriptions__available",children:[(0,n.jsx)("h2",{className:"woocommerce-marketplace__my-subscriptions__heading",children:(0,u.__)("Available to use","woocommerce")}),(0,n.jsx)("p",{className:"woocommerce-marketplace__my-subscriptions__table-description",children:(0,u.__)("WooCommerce.com subscriptions you haven't used yet.","woocommerce")}),(0,n.jsx)("div",{className:"woocommerce-marketplace__my-subscriptions__table-wrapper",children:(0,n.jsx)(ge,{isLoading:o,rows:i.map((e=>He(e,"available")))})})]})]})]})}var co=function(e){return e[e.notConnected=0]="notConnected",e[e.notInstalled=1]="notInstalled",e[e.installing=2]="installing",e[e.installedCanActivate=3]="installedCanActivate",e[e.installedCannotActivate=4]="installedCannotActivate",e[e.installFailed=5]="installFailed",e[e.activating=6]="activating",e[e.activated=7]="activated",e[e.activationFailed=8]="activationFailed",e}(co||{});const so=function(e){const[o,t]=(0,r.useState)(co.notInstalled),[a,i]=(0,r.useState)(),[m,d]=(0,r.useState)(),[p,_]=(0,r.useState)(),[w,x]=(0,r.useState)(),[b,y]=(0,r.useState)(),[v,k]=(0,r.useState)(!1),[j,N]=(0,r.useState)(),{addInstalledProduct:C}=(0,r.useContext)(l),M=(0,g.useQuery)();function T(){t(co.notInstalled),N(void 0),(0,g.navigateTo)({url:(0,g.getNewPath)({...M,install:void 0,installProduct:void 0},s.q$,{})})}return(0,r.useEffect)((()=>{const e=(0,c.Qk)("wccomHelper",{});d(e?.installedProducts),_(e?.isConnected)}),[]),(0,r.useEffect)((()=>{if(k(!1),!M.installProduct)return;const o=parseInt(M.installProduct,10),r=e.products.find((e=>e.id===o));if(r){if(m&&m.find((e=>e===r.slug)))return;p?t(co.notInstalled):(t(co.notConnected),N({status:"warning",message:(0,u.__)("In order to install a product, you need to first connect your account.","woocommerce")})),k(!0),i(r)}}),[M,e.products,m,p]),a&&v?(0,n.jsxs)(h.Modal,{title:o===co.activated?(0,u.__)("You are ready to go!","woocommerce"):(0,u.__)("Add to Store","woocommerce"),onRequestClose:T,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal has-size-medium",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[j&&(0,n.jsx)(h.Notice,{status:j.status,isDismissible:!1,children:j.message}),(0,n.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:o===co.notConnected?"":o===co.installedCanActivate||o===co.activating?(0,u.__)("Extension successfully installed. Would you like to activate it?","woocommerce"):o===co.installedCannotActivate?(0,u.__)("Extension successfully installed but we can't activate it at the moment. Please visit the plugins page to see more.","woocommerce"):o===co.activated?(0,u.__)("Keep the momentum going and start setting up your extension.","woocommerce"):(0,u.__)("Would you like to install this extension?","woocommerce")}),a&&(0,n.jsx)(P,{product:a,small:!0,tracksData:{position:1,group:"install-flow",label:"install"}}),(0,n.jsxs)(h.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:[o===co.activated?b?(0,n.jsx)(h.Button,{variant:"tertiary",href:b,className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("View Docs","woocommerce")},"docs"):(0,n.jsx)(n.Fragment,{}):(0,n.jsx)(h.Button,{variant:"tertiary",onClick:T,className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("Cancel","woocommerce")},"cancel"),o===co.notConnected?(0,n.jsx)(eo,{variant:"primary"},"connect"):o===co.installedCanActivate||o===co.activating?(0,n.jsx)(h.Button,{variant:"primary",onClick:function(){w&&(t(co.activating),(0,f.recordEvent)("marketplace_activate_new_product_clicked",{product_id:a?a.id:0}),fetch(w).then((()=>{t(co.activated)})).catch((()=>{t(co.activationFailed),N({status:"error",message:(0,u.__)("Activation failed. Please try again from the plugins page.","woocommerce")})})))},isBusy:o===co.activating,disabled:o===co.activating,children:(0,u.__)("Activate","woocommerce")},"activate"):o===co.activated||o===co.installedCannotActivate||o===co.activationFailed?(0,n.jsx)(h.Button,{variant:"primary",href:s.dL,className:"woocommerce-marketplace__header-account-modal-button",children:(0,u.__)("View in Plugins","woocommerce")},"plugin-list"):(0,n.jsx)(h.Button,{variant:"primary",onClick:function(){var e;a&&a.id&&((0,f.recordEvent)("marketplace_install_new_product_clicked",{product_id:a.id}),t(co.installing),(e=a.id,le()({path:"/wc/v3/marketplace/create-order",method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({product_id:e})})).then((e=>{var o,r;if(!e.success)throw e;return(0,H.dispatch)(Ce).startInstalling(String(null!==(o=a.id)&&void 0!==o?o:"")),y(e.data.documentation_url),a.slug&&C(null!==(r=a.slug)&&void 0!==r?r:""),(0,S.UN)(e.data.product_type,e.data.zip_slug).then((e=>{var o;(0,H.dispatch)(Ce).stopInstalling(String(null!==(o=a.id)&&void 0!==o?o:"")),e.data.activateUrl?(x(e.data.activateUrl),t(co.installedCanActivate)):t(co.installedCannotActivate)}))})).catch((e=>{var o;e.data.redirect_location?(N({status:"warning",message:(0,u.__)("We need your address to complete installing this product. We will redirect you to WooCommerce.com checkout. Afterwards, you will be able to install the product.","woocommerce")}),setTimeout((()=>{window.location.href=e.data.redirect_location}),5e3)):(t(co.installFailed),N({status:"error",message:null!==(o=e.data.message)&&void 0!==o?o:(0,u.__)("An error occurred. Please try again later.","woocommerce")}))})))},isBusy:o===co.installing,disabled:o===co.installing||o===co.installFailed,children:(0,u.__)("Install","woocommerce")},"install")]})]}):(0,n.jsx)(n.Fragment,{})};var no=t(79492);function ao(){const e="woo-connect-notice-marketplace-dismissed",o=(0,c.Qk)("wccomHelper",{}),t=o?.woocomConnectNoticeType||"none",r=(0,u.__)("Your store","woocommerce"),s=o?.storeName||r,a=s!==r?`<strong>${s}</strong>`:s;if("none"===t)return null;const i=localStorage.getItem(e),l=new Date(i||""),m=new Date;m.setMonth(m.getMonth()-1),(null===i||isNaN(l.valueOf())||m.valueOf()>l.valueOf())&&(localStorage.removeItem("wc-marketplaceNoticeClosed-woo-connect-notice"),localStorage.removeItem(e));const d={long:(0,u.sprintf)((0,u.__)("%s might be at risk because it’s running outdated WooCommerce plugins and is not yet connected to a WooCommerce.com account. Please complete the connection to get updates and streamlined support.","woocommerce"),a),short:(0,u.sprintf)((0,u.__)("%s is not yet connected to a WooCommerce.com account. Please complete the connection to get updates and streamlined support.","woocommerce"),a)}[t],p=(0,S.A5)((0,S.E4)(),[["utm_source","pu"],["utm_campaign","pu_in_apps_screen_connect"]]);return(0,n.jsxs)(to.A,{id:"woo-connect-notice",description:d,isDismissible:!0,variant:"warning",className:"woocommerce-marketplace__connect-notice",onClose:()=>{localStorage.setItem(e,(new Date).toString()),(0,f.recordEvent)("woo_connect_notice_in_marketplace_dismissed")},onLoad:()=>{(0,f.recordEvent)("woo_connect_notice_in_marketplace_shown")},children:[(0,n.jsx)(h.Button,{href:p,variant:"primary",onClick:()=>((0,f.recordEvent)("woo_connect_notice_in_marketplace_clicked"),!0),children:(0,u.__)("Connect your store","woocommerce")}),(0,n.jsx)(h.Button,{href:"https://woocommerce.com/document/managing-woocommerce-com-subscriptions/#connect-your-site-woocommercecom-account",target:"_blank",variant:"tertiary",onClick:()=>((0,f.recordEvent)("woo_connect_notice_learn_more_clicked"),!0),children:(0,u.__)("Learn more","woocommerce")})]})}function io(e){const o=(0,c.Qk)("wccomHelper",{});if(!o?.isConnected)return null;const{selectedTab:t}=e;return"my-subscriptions"!==t&&o?.mySubscriptionsTabLoaded?null:o?.wooUpdateManagerActive||o?.wooUpdateManagerInstalled?o?.wooUpdateManagerInstalled&&!o?.wooUpdateManagerActive?(0,n.jsx)("section",{className:"woocommerce-marketplace__woo-update-manager-plugin__notices",children:(0,n.jsxs)(h.Notice,{status:"error",isDismissible:!1,children:[(0,n.jsx)("span",{dangerouslySetInnerHTML:(0,ke.Ay)((0,u.__)("Activate the <b>WooCommerce.com Update Manager</b> to continue receiving the updates and streamlined support included in your WooCommerce.com subscriptions.","woocommerce"))}),(0,n.jsx)("div",{className:"components-notice__buttons",children:(0,n.jsx)(h.Button,{href:s.dL,variant:"secondary",children:(0,u.__)("Activate","woocommerce")})})]})}):null:(0,n.jsx)("section",{className:"woocommerce-marketplace__woo-update-manager-plugin__notices",children:(0,n.jsxs)(h.Notice,{status:"error",isDismissible:!1,children:[(0,n.jsx)("span",{dangerouslySetInnerHTML:(0,ke.Ay)((0,u.__)("Please install the <b>WooCommerce.com Update Manager</b> to continue receiving the updates and streamlined support included in your WooCommerce.com subscriptions.<br/>Alternatively, you can download and install it manually.","woocommerce"))}),(0,n.jsxs)("div",{className:"components-notice__buttons",children:[(0,n.jsx)(h.Button,{href:o?.wooUpdateManagerInstallUrl,variant:"secondary",children:(0,u.__)("Install","woocommerce")}),(0,n.jsx)(h.Button,{href:s.K_,variant:"link",children:(0,u.__)("Download","woocommerce")})]})]})})}function lo(e){const{type:o}=e,t=(0,c.Qk)("wccomHelper",{}),r={"woo-subscription-expired-notice":{shown:"woo_subscription_expired_notice_in_marketplace_shown",clicked:"woo_subscription_expired_notice_in_marketplace_clicked",dismissed:"woo_subscription_expired_notice_in_marketplace_dismissed"},"woo-subscription-expiring-notice":{shown:"woo_subscription_expiring_notice_in_marketplace_shown",clicked:"woo_subscription_expiring_notice_in_marketplace_clicked",dismissed:"woo_subscription_expiring_notice_in_marketplace_dismissed"},"woo-subscription-missing-notice":{shown:"woo_subscription_missing_notice_in_marketplace_shown",clicked:"woo_subscription_missing_notice_in_marketplace_clicked",dismissed:"woo_subscription_missing_notice_in_marketplace_dismissed"}};let s=null,a="";const i=t?.dismissNoticeNonce||"";if("expired"===o)s=t?.subscription_expired_notice||{},a="woo-subscription-expired-notice";else if("expiring"===o)s=t?.subscription_expiring_notice||{},a="woo-subscription-expiring-notice";else{if("missing"!==o)return null;s=t?.subscription_missing_notice||{},a="woo-subscription-missing-notice"}return t.isConnected&&s?.description?(0,n.jsx)(to.A,{id:a,description:s.description,isDismissible:!0,variant:"error",onClose:()=>{(0,f.recordEvent)(r[a].dismissed);const e={notice_id:a,dismiss_notice_nonce:i};le()({path:"/wc-admin/notice/dismiss",method:"POST",data:e})},onLoad:function(){(0,f.recordEvent)(r[a].shown)},children:(0,n.jsx)(h.Button,{href:s.button_link,variant:"secondary",onClick:function(){(0,f.recordEvent)(r[a].clicked)},children:s.button_text})}):null}function mo(e){const{onLoadMore:o,isBusy:t,disabled:r}=e;return t&&(0,N.speak)((0,u.__)("Loading more products","woocommerce")),(0,n.jsx)(h.Button,{className:"woocommerce-marketplace__load-more",variant:"secondary",onClick:function(){(0,f.queueRecordEvent)("marketplace_load_more_button_clicked",{}),o()},isBusy:t,disabled:r,children:(0,u.__)("Load more","woocommerce")})}function uo(){const e=(0,r.useContext)(l),[o,t]=(0,r.useState)([]),[s,a]=(0,r.useState)([]),[i,m]=(0,r.useState)(1),[d,p]=(0,r.useState)(1),[_,h]=(0,r.useState)(1),[w,x]=(0,r.useState)(1),[b,y]=(0,r.useState)(1),[v,k]=(0,r.useState)(0),[j,M]=(0,r.useState)(!1),{isLoading:T,setIsLoading:A,selectedTab:I,setSearchResultsCount:E}=e,L=(0,g.useQuery)(),D=e=>{(0,N.speak)((0,u.sprintf)((0,u.__)("%d products found","woocommerce"),e))},z=(0,r.useCallback)((()=>{M(!0);const e=new URLSearchParams,o=new AbortController;L.category&&"_all"!==L.category&&e.append("category",L.category),"themes"!==L.tab&&"business-services"!==L.tab||e.append("category",L.tab),L.term&&e.append("term",L.term);const r=(0,c.Qk)("wccomHelper",!1);return r.storeCountry&&e.append("country",r.storeCountry),e.append("page",(i+1).toString()),(0,S.Hu)(e,o.signal).then((e=>{t((o=>{const t=Array.isArray(o[0])?o.flat():o,r=e.products.filter((e=>!t.some((o=>o.id===e.id))));var c;return r.length>0&&k(null!==(c=r[0].id)&&void 0!==c?c:0),[...t,...r]})),(0,N.speak)((0,u.__)("More products loaded","woocommerce")),m((e=>e+1))})).catch((()=>{(0,N.speak)((0,u.__)("Error loading more products","woocommerce"))})).finally((()=>{M(!1)})),()=>{o.abort()}}),[i,L.category,L.term,L.tab,M]);return(0,r.useEffect)((()=>{if(i>1)return;const e=[{category:"extensions",type:C.ch.extension},{category:"themes",type:C.ch.theme},{category:"business-services",type:C.ch.businessService}],o=e.map((()=>new AbortController));A(!0),t([]);const r=e=>{if(L.search){(0,f.recordEvent)("marketplace_search_finish",{tab:L.tab,total_products:e,category:L.category||"_all",search_term:L.term||""});const o=new URL(window.location.href);o.searchParams.delete("search"),window.history.replaceState(null,"",o.pathname+o.search)}};if(L.category&&"_all"!==L.category){const e=new URLSearchParams;e.append("category",L.category),L.term&&e.append("term",L.term);const s=(0,c.Qk)("wccomHelper",!1);s.storeCountry&&e.append("country",s.storeCountry),(0,S.Hu)(e,o[0].signal).then((e=>{t(e.products),p(e.totalPages),E({[L.tab]:e.totalProducts}),D(e.totalProducts),r(e.totalProducts)})).catch((()=>{t([])})).finally((()=>{A(!1)}))}else Promise.all(e.map((({category:e,type:t},r)=>{const s=new URLSearchParams;"extensions"!==e&&s.append("category",e),L.term&&s.append("term",L.term);const n=(0,c.Qk)("wccomHelper",!1);return n.storeCountry&&s.append("country",n.storeCountry),(0,S.Hu)(s,o[r].signal).then((e=>{const o=((e,o)=>e.map((e=>({...e,type:o}))))(e.products,t);return{products:o,totalPages:e.totalPages,totalProducts:e.totalProducts,type:t}}))}))).then((e=>{const o=e.flatMap((e=>e.products));t(o);const c={extensions:e.find((e=>"extension"===e.type))?.totalProducts,themes:e.find((e=>"theme"===e.type))?.totalProducts,"business-services":e.find((e=>"business-service"===e.type))?.totalProducts};E(c),e.forEach((e=>{switch(e.type){case C.ch.extension:h(e.totalPages);break;case C.ch.theme:x(e.totalPages);break;case C.ch.businessService:y(e.totalPages)}}));const s=L.tab;r(c[s]),D(e.reduce(((e,o)=>e+o.totalProducts),0))})).catch((()=>{t([])})).finally((()=>{A(!1)}));return()=>{o.forEach((e=>{e.abort()}))}}),[L.tab,L.term,L.category,A,E,i]),(0,r.useEffect)((()=>{let e;switch(I){case"extensions":e=o.filter((e=>e.type===C.ch.extension));break;case"themes":e=o.filter((e=>e.type===C.ch.theme));break;case"business-services":e=o.filter((e=>e.type===C.ch.businessService));break;default:e=[]}a(e)}),[I,o]),(0,r.useEffect)((()=>{const e={view:L?.tab,search_term:L?.term,product_type:L?.section,category:L?.category};F(e),function(e){if(e.product_type)return;let o="extensions_view";const t=e.view||"_featured",r=e.search_term||null,c=e.category||null,s={...t&&{section:t},...r&&{search_term:r},version:"2",wccom_connected:W()};switch(t){case"extensions":s.section=c||"_all";break;case"themes":s.section="themes";break;case"my-subscriptions":o="subscriptions_view",s.section="helper"}(0,f.recordEvent)(o,s)}(e)}),[L?.tab,L?.term,L?.section,L?.category]),(0,r.useEffect)((()=>{m(1),k(0)}),[I,L?.category,L?.term]),(0,r.useEffect)((()=>{v&&setTimeout((()=>{const e=document.getElementById(`product-${v}`);e&&e.focus()}),0)}),[v]),(0,n.jsxs)("div",{className:"woocommerce-marketplace__content",children:[(0,n.jsx)(no.A,{format:"promo-card"}),(0,n.jsx)(no.A,{format:"notice"}),(0,n.jsx)(so,{products:s}),"business-services"!==I&&"my-subscriptions"!==I&&(0,n.jsx)(ao,{}),"business-services"!==I&&(0,n.jsx)(io,{selectedTab:I}),"business-services"!==I&&(0,n.jsx)(lo,{type:"expired"}),"business-services"!==I&&(0,n.jsx)(lo,{type:"expiring"}),(()=>{switch(I){case"extensions":case"themes":case"business-services":return(0,n.jsx)(ne,{products:s,categorySelector:!0,type:(0,S.YW)(I),searchTerm:L.term});case"discover":return(0,n.jsx)(G,{});case"my-subscriptions":return(0,n.jsx)(pe,{children:(0,n.jsx)(ro,{})});default:return(0,n.jsx)(n.Fragment,{})}})(),!T&&(()=>{if(L.category&&"_all"!==L.category)return i<d;switch(I){case"extensions":return i<_;case"themes":return i<w;case"business-services":return i<b;default:return!1}})()&&(0,n.jsx)(mo,{onLoadMore:z,isBusy:j,disabled:j})]})}var po=t(22480);function _o(){const{selectedTab:e}=(0,r.useContext)(l),o="woocommerce-marketplace"+(e?" woocommerce-marketplace--"+e:"");return(0,n.jsxs)("div",{className:o,children:[(0,n.jsx)(j,{}),(0,n.jsx)(uo,{}),(0,n.jsx)(po.Ay,{})]})}function ho(){return(0,n.jsx)(m,{children:(0,n.jsx)(_o,{})})}},97687:(e,o,t)=>{t.d(o,{E:()=>r});const r=()=>void 0!==window.wcCalypsoBridge&&window.wcCalypsoBridge.isWooExpress}}]);