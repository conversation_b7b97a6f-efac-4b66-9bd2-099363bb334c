/*! For license information please see index.js.LICENSE.txt */
(()=>{"use strict";var e={58039:(e,t,o)=>{o.d(t,{H:()=>n});const n="email-editor/editor"},76597:e=>{var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===o}(e)}(e)},o="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?l((o=e,Array.isArray(o)?[]:{}),e,t):e;var o}function r(e,t,o){return e.concat(t).map((function(e){return n(e,o)}))}function s(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function i(e,t){try{return t in e}catch(e){return!1}}function l(e,o,a){(a=a||{}).arrayMerge=a.arrayMerge||r,a.isMergeableObject=a.isMergeableObject||t,a.cloneUnlessOtherwiseSpecified=n;var c=Array.isArray(o);return c===Array.isArray(e)?c?a.arrayMerge(e,o,a):function(e,t,o){var r={};return o.isMergeableObject(e)&&s(e).forEach((function(t){r[t]=n(e[t],o)})),s(t).forEach((function(s){(function(e,t){return i(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,s)||(i(e,s)&&o.isMergeableObject(t[s])?r[s]=function(e,t){if(!t.customMerge)return l;var o=t.customMerge(e);return"function"==typeof o?o:l}(s,o)(e[s],t[s],o):r[s]=n(t[s],o))})),r}(e,o,a):n(o,a)}l.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,o){return l(e,o,t)}),{})};var a=l;e.exports=a},94931:(e,t,o)=>{var n=o(51609),r=Symbol.for("react.element"),s=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,o){var n,s={},c=null,d=null;for(n in void 0!==o&&(c=""+o),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(d=t.ref),t)i.call(t,n)&&!a.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===s[n]&&(s[n]=t[n]);return{$$typeof:r,type:e,key:c,ref:d,props:s,_owner:l.current}}t.Fragment=s,t.jsx=c,t.jsxs=c},39793:(e,t,o)=>{e.exports=o(94931)},51609:e=>{e.exports=window.React},56427:e=>{e.exports=window.wp.components},3582:e=>{e.exports=window.wp.coreData},47143:e=>{e.exports=window.wp.data},86087:e=>{e.exports=window.wp.element},52619:e=>{e.exports=window.wp.hooks},27723:e=>{e.exports=window.wp.i18n},5573:e=>{e.exports=window.wp.primitives},4921:(e,t,o)=>{function n(e){var t,o,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(o=n(e[t]))&&(r&&(r+=" "),r+=o)}else for(o in e)e[o]&&(r&&(r+=" "),r+=o);return r}o.d(t,{A:()=>r});const r=function(){for(var e,t,o=0,r="",s=arguments.length;o<s;o++)(e=arguments[o])&&(t=n(e))&&(r&&(r+=" "),r+=t);return r}}},t={};function o(n){var r=t[n];if(void 0!==r)return r.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,o),s.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};o.r(n),o.d(n,{ExperimentalEmailEditor:()=>Sn,initializeEditor:()=>Cn,storeName:()=>z.H});var r={};o.r(r),o.d(r,{requestSendingNewsletterPreview:()=>_o,setContentValidation:()=>yo,setEmailPost:()=>po,setIsFetchingPersonalizationTags:()=>go,setPersonalizationTagsList:()=>ho,setTemplateToPost:()=>uo,togglePreviewModal:()=>co,updateSendPreviewEmail:()=>mo});var s={};o.r(s),o.d(s,{canUserEditGlobalEmailStyles:()=>Fo,canUserEditTemplates:()=>Bo,getBlockPatternsForEmailTemplate:()=>No,getContentValidation:()=>Yo,getCurrentTemplate:()=>Ho,getCurrentTemplateContent:()=>zo,getEditedEmailContent:()=>To,getEditedPostTemplate:()=>Mo,getEmailPostId:()=>Ro,getEmailPostType:()=>Oo,getEmailTemplates:()=>Lo,getGlobalEmailStylesPost:()=>Ao,getGlobalStylesPostId:()=>Zo,getInitialEditorSettings:()=>Vo,getPaletteColors:()=>Do,getPersonalizationTagsList:()=>Wo,getPersonalizationTagsState:()=>$o,getPreviewState:()=>Go,getSentEmailEditorPosts:()=>Po,getStyles:()=>Uo,getTheme:()=>qo,getUrls:()=>Jo,hasEdits:()=>So,hasEmptyContent:()=>Co,isEmailSent:()=>Eo,isFeatureActive:()=>ko});var i={};o.r(i),o.d(i,{getPersonalizationTagsList:()=>Ko});var l=o(39793),a=o(47143),c=o(86087),d=o(52619);window.wp.formatLibrary;const m=window.wp.blocks;function p(){try{return(0,m.getBlockTypes)().filter((e=>!0===e.supports?.email)).map((e=>e.name))}catch(e){return console.error("Failed to get allowed block names:",e),[]}}const u=window.wp.blockLibrary,_=window.wp.blockEditor,g=window.wp.compose,h=(0,g.createHigherOrderComponent)((e=>function(t){return"core/columns"!==t.name?(0,l.jsx)(e,{...t}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(e,{...t}),(0,l.jsx)(_.InspectorControls,{children:(0,l.jsx)("style",{children:"\n      .components-panel__body .components-toggle-control .components-form-toggle { opacity: 0.3; }\n      .components-panel__body .components-toggle-control .components-form-toggle__input { pointer-events: none; }\n      .components-panel__body .components-toggle-control label { pointer-events: none; }\n    "})})]})}),"columnsEditCallback"),y=["core/column","core/columns"];var x=o(27723);function w({layoutClassNames:e}){const t=(0,_.useBlockProps)({className:e});return(0,l.jsxs)("div",{...t,children:[(0,l.jsx)("p",{children:(0,x.__)("This is the Content block.","woocommerce")}),(0,l.jsx)("p",{children:(0,x.__)("It will display all the blocks in the email content, which might be only simple text paragraphs. You can enrich your message with images, incorporate data through tables, explore different layout designs with columns, or use any other block type.","woocommerce")})]})}const f=(0,g.createHigherOrderComponent)((e=>function(t){return"core/image"!==t.name?(0,l.jsx)(e,{...t}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(e,{...t}),(0,l.jsx)(_.InspectorControls,{children:(0,l.jsx)("style",{children:"\n        .components-tools-panel .components-toggle-control { display: none; }\n      "})})]})}),"imageEditCallback"),b=window.wp.richText;var v=o(56427);const j=(e,t)=>{const o=e.current.ownerDocument.defaultView.getSelection();if(!o.rangeCount)return{start:0,end:0};const n=o.getRangeAt(0);if(null===o.anchorNode.previousSibling)return{start:o.anchorOffset,end:o.anchorOffset+n.toString().length};const r=(0,b.create)({html:t});let s=o.anchorNode.previousSibling;s=function(e){let t=e;for(;t&&t?.children?.length>0;)t=t.children[0];return t}(s);const i=function(e,t){let o=null;for(const[n,r]of t.entries())if(r)for(const t of r)t?.attributes&&e.tagName.toLowerCase()===t.tagName?.toLowerCase()&&e.getAttribute("data-link-href")===t?.attributes["data-link-href"]&&(o=n);return o}(s,r.formats);if(null!==i)return{start:i+o.anchorOffset+1,end:i+o.anchorOffset+n.toString().length};const l=function(e,t){for(const[o,n]of t.entries()){if(!n)continue;const{attributes:t}=n;if(e.getAttribute("data-rich-text-comment")===t["data-rich-text-comment"])return o}return null}(s,r.replacements);return null!==l?{start:l+o.anchorOffset+1,end:l+o.anchorOffset+n.toString().length}:{start:r.text.length,end:r.text.length+n.toString().length}},k=(e,t)=>(t.forEach((t=>{if(!e.includes(t.token.slice(0,t.token.length-1)))return;const o=t.token.substring(1,t.token.length-1).replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),n=new RegExp(`(?<!\x3c!--)(?<!["'])\\[(${o}(\\s[^\\]]*)?)\\](?!--\x3e)`,"g");e=e.replace(n,(e=>`\x3c!--${e}--\x3e`))})),e),S=({groupedTags:e,activeCategory:t,onCategorySelect:o})=>{const n=e=>e===t?"woocommerce-personalization-tags-modal-menu-item-active":"";return(0,l.jsxs)(v.MenuGroup,{className:"woocommerce-personalization-tags-modal-menu",children:[(0,l.jsx)(v.MenuItem,{onClick:()=>o(null),className:n(null),children:(0,x.__)("All","woocommerce")}),(0,l.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true",role:"presentation","data-testid":"woocommerce-personalization-tags-modal-menu-separator"}),Object.keys(e).map(((e,t,r)=>(0,l.jsxs)(c.Fragment,{children:[(0,l.jsx)(v.MenuItem,{onClick:()=>o(e),className:n(e),children:e}),t<r.length-1&&(0,l.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true",role:"presentation","data-testid":"woocommerce-personalization-tags-modal-menu-separator"})]},e)))]})},C=({groupedTags:e,activeCategory:t,onInsert:o,canInsertLink:n,closeCallback:r,openLinkModal:s})=>{const{updateBlockAttributes:i}=(0,a.useDispatch)(_.store),c=(0,a.useSelect)((e=>e(_.store).getSelectedBlockClientId())),d=(0,a.useSelect)((e=>e(_.store).getBlock(c))),m=["core/button"].includes(d?.name),p=null===t?Object.entries(e):[[t,e[t]||[]]];return(0,l.jsx)(l.Fragment,{children:p.map((([e,t])=>(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"woocommerce-personalization-tags-modal-category",children:e}),(0,l.jsx)("div",{className:"woocommerce-personalization-tags-modal-category-group",children:t.map((t=>{const a=/\burl\b/.test(t.token);return(0,l.jsxs)("div",{className:"woocommerce-personalization-tags-modal-category-group-item",children:[(0,l.jsxs)("div",{className:"woocommerce-personalization-tags-modal-item-text",children:[(0,l.jsx)("strong",{children:t.name}),t.valueToInsert]}),(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-end"},children:[(0,l.jsx)(v.Button,{variant:"link",onClick:()=>{o&&o(t.valueToInsert,!1)},children:(0,x.__)("Insert","woocommerce")}),m&&a&&(0,l.jsx)(v.Button,{variant:"link",onClick:()=>{i(c,{url:t.valueToInsert}),r()},children:(0,x.__)("Set as URL","woocommerce")}),e===(0,x.__)("Link","woocommerce")&&n&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(v.Button,{variant:"link",onClick:()=>{r(),s(t)},children:(0,x.__)("Insert as link","woocommerce")})})]})]},t.token)}))})]},e)))})},E=({onInsert:e,isOpened:t,closeCallback:o,tag:n})=>{const[r,s]=(0,c.useState)((0,x.__)("Link","woocommerce"));return t?(0,l.jsxs)(v.Modal,{size:"small",title:(0,x.__)("Insert Link","woocommerce"),onRequestClose:o,className:"woocommerce-personalization-tags-modal",children:[(0,l.jsx)(v.TextControl,{label:(0,x.__)("Link Text","woocommerce"),value:r,onChange:s}),(0,l.jsx)(v.Button,{isPrimary:!0,onClick:()=>{e&&e(n.token,r)},children:(0,x.__)("Insert","woocommerce")})]}):null},T=window.lodash,P=()=>(0,d.applyFilters)("woocommerce_email_editor_events_tracking_enabled",!1),N="email_editor_events",B=new EventTarget,I=(e,t={})=>{if(!P())return;const o={name:`${N}_${e}`,..."object"!=typeof t?{data:t}:t};B.dispatchEvent(new CustomEvent(N,{detail:o}))},M=function(){const e={};return(t,o={})=>{if(!P())return;const n=`${t}_${JSON.stringify(o).length}`;e[n]||(I(t,o),e[n]=!0)}}(),H=(0,T.debounce)(I,700);var z=o(58039);const F=({onInsert:e,isOpened:t,closeCallback:o,canInsertLink:n=!1,openedBy:r=""})=>{const[s,i]=(0,c.useState)(null),[d,m]=(0,c.useState)(""),[p,u]=(0,c.useState)(null),[_,g]=(0,c.useState)(!1),h=(0,a.useSelect)((e=>e(z.H).getPersonalizationTagsList()),[]);if(_)return(0,l.jsx)(E,{onInsert:(t,o)=>{e(t,o),g(!1)},isOpened:_,closeCallback:()=>g(!1),tag:p});if(!t)return null;M("personalization_tags_modal_opened",{openedBy:r});const y=h.reduce(((e,t)=>{const{category:o,name:n,token:r}=t;return(!d||n.toLowerCase().includes(d.toLowerCase())||r.toLowerCase().includes(d.toLowerCase()))&&(e[o]||(e[o]=[]),e[o].push(t)),e}),{});return(0,l.jsxs)(v.Modal,{size:"medium",title:(0,x.__)("Personalization Tags","woocommerce"),onRequestClose:()=>{o(),I("personalization_tags_modal_closed",{openedBy:r})},className:"woocommerce-personalization-tags-modal",children:[(0,l.jsxs)("p",{children:[(0,x.__)("Insert personalization tags to dynamically fill in information and personalize your emails.","woocommerce")," ",(0,l.jsx)(v.ExternalLink,{href:"https://kb.mailpoet.com/article/435-a-guide-to-personalisation-tags-for-tailored-newsletters#list",onClick:()=>I("personalization_tags_modal_learn_more_link_clicked",{openedBy:r}),children:(0,x.__)("Learn more","woocommerce")})]}),(0,l.jsx)(v.SearchControl,{onChange:e=>{m(e),M("personalization_tags_modal_search_control_input_updated",{openedBy:r})},value:d}),(0,l.jsx)(S,{groupedTags:y,activeCategory:s,onCategorySelect:e=>{i(e),I("personalization_tags_modal_category_menu_clicked",{category:e,openedBy:r})}}),(0,l.jsx)(C,{groupedTags:y,activeCategory:s,onInsert:t=>{e(t),I("personalization_tags_modal_tag_insert_button_clicked",{insertedTag:t,activeCategory:s,openedBy:r})},closeCallback:o,canInsertLink:n,openLinkModal:e=>{u(e),g(!0)}})]})},A=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,c.useState)(!1),[r,s]=(0,c.useState)(null),[i,a]=(0,c.useState)(""),[d,m]=(0,c.useState)("");return(0,c.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("span[data-rich-text-comment]");if(t){const e=t.innerText.replace(/^\[|\]$/g,"");m(e),a(e),s(t),n(!0)}};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,l.jsx)(l.Fragment,{children:o&&r&&(0,l.jsx)(v.Popover,{position:"bottom right",onClose:()=>n(!1),anchor:r,className:"woocommerce-personalization-tag-popover",children:(0,l.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,l.jsx)(v.TextControl,{label:(0,x.__)("Personalization Tag","woocommerce"),value:i,onChange:e=>a(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),(0,l.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,l.jsx)(v.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,x.__)("Cancel","woocommerce")}),(0,l.jsx)(v.Button,{isPrimary:!0,onClick:()=>{t(d,i),n(!1)},children:(0,x.__)("Update","woocommerce")})]})]})})})},L=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,c.useState)(!1),[r,s]=(0,c.useState)(null),[i,d]=(0,c.useState)(""),[m,p]=(0,c.useState)(""),u=(0,a.useSelect)((e=>e(z.H).getPersonalizationTagsList()),[]);return(0,c.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("a[data-link-href]");t&&(s(t),p(t.getAttribute("data-link-href")||""),d(t.textContent||""),n(!0))};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,l.jsx)(l.Fragment,{children:o&&r&&(0,l.jsx)(v.Popover,{position:"bottom left",onClose:()=>n(!1),anchor:r,className:"woocommerce-personalization-tag-popover",children:(0,l.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,l.jsx)(v.TextControl,{label:(0,x.__)("Link Text","woocommerce"),value:i,onChange:e=>d(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,autoComplete:"off"}),(0,l.jsx)(v.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,x.__)("Link tag","woocommerce"),value:m,onChange:e=>{p(e)},options:u.filter((e=>e.category===(0,x.__)("Link","woocommerce"))).map((e=>({label:e.name,value:e.token})))}),(0,l.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,l.jsx)(v.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,x.__)("Cancel","woocommerce")}),(0,l.jsx)(v.Button,{isPrimary:!0,onClick:()=>{n(!1),t(r,m,i)},children:(0,x.__)("Update link","woocommerce")})]})]})})})},R=window.wp.editor;function O({contentRef:e}){const[t,o]=(0,c.useState)(!1),n=(0,a.useSelect)((e=>e("core/block-editor").getSelectedBlockClientId())),{updateBlockAttributes:r}=(0,a.useDispatch)("core/block-editor"),s=(0,a.useSelect)((e=>e("core/block-editor").getBlockAttributes(n))),i="text"in s?"text":"content",d=s?.[i]?.originalHTML||s?.[i]||"",m=(0,c.useCallback)(((t,o)=>{let{start:s,end:l}=j(e,d),a="";if(o){let e=(0,b.create)({html:d});e=(0,b.insert)(e,o,s,l),l=s+o.length,e=(0,b.applyFormat)(e,{type:"woocommerce-email-editor/link-shortcode",attributes:{"data-link-href":t,contenteditable:"false",style:"text-decoration: underline;"}},s,l),a=(0,b.toHTMLString)({value:e})}else{let e=(0,b.create)({html:d});e=(0,b.insert)(e,(0,b.create)({html:`\x3c!--${t}--\x3e&nbsp;`}),s,l),a=(0,b.toHTMLString)({value:e})}r(n,{[i]:a})}),[d,i,e,n,r]);return(0,a.useSelect)((e=>{const t=e(z.H);if(!t)return!1;const o=t.getEmailPostId(),n=t.getEmailPostType(),r=e(R.store).getCurrentPostId(),s=e(R.store).getCurrentPostType();if(String(r)===String(o)&&String(s)===String(n))return!0;if("wp_template"===s){const e=t.getCurrentTemplate();if(!e)return!1;const o=t.getEmailTemplates();return!!o&&o.some((t=>t.id===e.id&&t.post_types?.includes(n)))}return!1}),[])?(0,l.jsx)(_.BlockControls,{children:(0,l.jsxs)(v.ToolbarGroup,{children:[(0,l.jsx)(v.ToolbarButton,{icon:"shortcode",title:(0,x.__)("Personalization Tags","woocommerce"),onClick:()=>{o(!0),I("block_controls_personalization_tags_button_clicked")}}),(0,l.jsx)(A,{contentRef:e,onUpdate:(e,t)=>{const o=d.replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);r(n,{[i]:o})}}),(0,l.jsx)(L,{contentRef:e,onUpdate:(e,t,o)=>{const s=e.getAttribute("data-link-href").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),i=new RegExp(`<a([^>]*?)data-link-href="${s}"([^>]*?)>${e.textContent}</a>`,"gi"),l=d.replace(i,((e,n,r)=>`<a${n}data-link-href="${t}"${r}>${o}</a>`));r(n,{content:l})}}),(0,l.jsx)(F,{isOpened:t,onInsert:(e,t)=>{m(e,t),o(!1)},closeCallback:()=>o(!1),canInsertLink:!0,openedBy:"block-controls"})]})}):null}const V=(0,g.createHigherOrderComponent)((e=>t=>{const{attributes:o,setAttributes:n,name:r}=t,{content:s}=o,i=(0,a.useSelect)((e=>e(z.H).getPersonalizationTagsList()),[]),d=(0,c.useCallback)((()=>s?k(s,i):""),[s,i]),m=(0,c.useCallback)((e=>{if(void 0!==e.content){const t=k(e.content,i);n({...e,content:t})}else n(e)}),[i,n]);return"core/paragraph"===r||"core/heading"===r||"core/list-item"===r?(0,l.jsx)(e,{...t,attributes:{...o,content:d()},setAttributes:m}):(0,l.jsx)(e,{...t})}),"personalizationTagsLiveContentUpdate"),D=window.wp.domReady;var G=o.n(D);const $=["core/social-links"];function W(){(0,m.getBlockTypes)().forEach((e=>{const t=e.name;if($.includes(t))return;const o=(0,a.select)(m.store).getBlockStyles(t);Array.isArray(o)&&0!==o?.length&&o.forEach((e=>{(0,m.unregisterBlockStyle)(t,e.name)}))}))}const U=e=>t=>{const{setAttributes:o}=t,n=(0,c.useCallback)((e=>{e?.url&&e.url?.startsWith("http://[")&&(e.url=e.url.replace("http://[","[")),o(e)}),[o]);return(0,l.jsx)(e,{...t,setAttributes:n})},q=["behance","bluesky","chain","discord","facebook","feed","github","gravatar","instagram","linkedin","mail","mastodon","medium","patreon","pinterest","reddit","spotify","telegram","threads","tiktok","tumblr","twitch","twitter","vimeo","wordpress","whatsapp","x","youtube"],Z=e=>t=>{if("core/social-links"!==t.name)return(0,l.jsx)(e,{...t});const o=`\n\t\t.block-editor-tools-panel-color-gradient-settings__item:has([title="${(0,x.__)("Icon color")}"]) {\n\t\t\tdisplay: none !important;\n\t\t}\n\t\t.block-editor-tools-panel-color-gradient-settings__item:nth-child(2 of .block-editor-tools-panel-color-gradient-settings__item){\n\t\t\tborder-top:1px solid #ddd;\n\t\t\tborder-top-left-radius:2px;\n\t\t\tborder-top-right-radius:2px;\n\t\t}\n\t\t`;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(e,{...t}),(0,l.jsx)(_.InspectorControls,{group:"color",children:(0,l.jsx)("style",{children:o})})]})};const J=window.wp.privateApis,{unlock:Y}=(0,J.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-site"),{ColorPanel:K}=Y(_.privateApis),{useGlobalStylesOutputWithConfig:X}=Y(_.privateApis),{Editor:Q,FullscreenMode:ee,ViewMoreMenuGroup:te,BackButton:oe}=Y(R.privateApis),{registerEntityAction:ne,unregisterEntityAction:re}=Y((0,a.dispatch)(R.store));var se=o(3582);const ie=window.wp.notices;var le=o(5573);const ae=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})}),ce=window.wp.htmlEntities;function de(e){return"string"==typeof e.title?(0,ce.decodeEntities)(e.title):e.title&&"rendered"in e.title?(0,ce.decodeEntities)(e.title.rendered):e.title&&"raw"in e.title?(0,ce.decodeEntities)(e.title.raw):""}function me(e,t){return t?e.length>1?(0,x.sprintf)((0,x._n)("Are you sure you want to permanently delete %d item?","Are you sure you want to permanently delete %d items?",e.length,"woocommerce"),e.length):(0,x.sprintf)((0,x.__)('Are you sure you want to permanently delete "%s"?',"woocommerce"),(0,ce.decodeEntities)(de(e[0]))):e.length>1?(0,x.sprintf)((0,x._n)("Are you sure you want to move %d item to the trash ?","Are you sure you want to move %d items to the trash ?",e.length,"woocommerce"),e.length):(0,x.sprintf)((0,x.__)('Are you sure you want to move "%s" to the trash?',"woocommerce"),de(e[0]))}const pe=e=>{re("postType",e,"move-to-trash"),ne("postType",e,(()=>{const e=(0,d.applyFilters)("woocommerce_email_editor_trash_modal_should_permanently_delete",!1);return{id:"trash-email-post",label:e?(0,x.__)("Permanently delete","woocommerce"):(0,x.__)("Move to trash","woocommerce"),supportsBulk:!0,icon:ae,isEligible(e){if("wp_template"===e.type||"wp_template_part"===e.type||"wp_block"===e.type)return!1;const{permissions:t}=e;return t?.delete},hideModalHeader:!0,modalFocusOnMount:"firstContentElement",RenderModal:({items:t,closeModal:o,onActionPerformed:n})=>{const[r,s]=(0,c.useState)(!1),{createSuccessNotice:i,createErrorNotice:d}=(0,a.useDispatch)(ie.store),{deleteEntityRecord:m}=(0,a.useDispatch)(se.store),{urls:p}=(0,a.useSelect)((e=>({urls:e(z.H).getUrls()})),[]);return(0,l.jsxs)(v.__experimentalVStack,{spacing:"5",children:[(0,l.jsx)(v.__experimentalText,{children:me(t,e)}),(0,l.jsxs)(v.__experimentalHStack,{justify:"right",children:[(0,l.jsx)(v.Button,{variant:"tertiary",onClick:()=>{o?.(),I("trash_modal_cancel_button_clicked")},disabled:r,__next40pxDefaultSize:!0,children:(0,x.__)("Cancel","woocommerce")}),(0,l.jsx)(v.Button,{variant:"primary",onClick:async()=>{I("trash_modal_move_to_trash_button_clicked"),s(!0);const r=await Promise.allSettled(t.map((t=>m("postType",t.type,t.id,{force:e},{throwOnError:!0}))));if(r.every((({status:e})=>"fulfilled"===e))){let o;o=1===r.length?e?(0,x.sprintf)((0,x.__)('"%s" permanently deleted.',"woocommerce"),de(t[0])):(0,x.sprintf)((0,x.__)('"%s" moved to the trash.',"woocommerce"),de(t[0])):e?(0,x.__)("The items were permanently deleted.","woocommerce"):(0,x.sprintf)((0,x._n)("%s item moved to the trash.","%s items moved to the trash.",t.length,"woocommerce"),t.length),i(o,{type:"snackbar",id:"trash-email-post-action"}),n?.(t),p?.listings&&(window.location.href=p.listings)}else{let e;if(1===r.length){const t=r[0];e=t.reason?.message?t.reason.message:(0,x.__)("An error occurred while performing the action.","woocommerce")}else{const t=new Set,o=r.filter((({status:e})=>"rejected"===e));for(const e of o){const o=e;o.reason?.message&&t.add(o.reason.message)}e=0===t.size?(0,x.__)("An error occurred while performing the action.","woocommerce"):1===t.size?(0,x.sprintf)((0,x.__)("An error occurred while performing the action: %s","woocommerce"),[...t][0]):(0,x.sprintf)((0,x.__)("Some errors occurred while performing the action: %s","woocommerce"),[...t].join(","))}I("trash_modal_move_to_trash_error",{errorMessage:e}),d(e,{type:"snackbar"})}s(!1),o?.()},isBusy:r,disabled:r,__next40pxDefaultSize:!0,children:e?(0,x.__)("Delete permanently","woocommerce"):(0,x.__)("Move to trash","woocommerce")})]})]})}}})())};function ue(){(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/filter-set-url-attribute",U),(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/deactivate-stack-on-mobile",h),(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/hide-expand-on-click",f),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/deactivate-image-filter",((e,t)=>"core/image"===t?{...e,supports:{...e.supports,filter:{duetone:!1}}}:e)),(0,b.unregisterFormatType)("core/image"),(0,b.unregisterFormatType)("core/code"),(0,b.unregisterFormatType)("core/language"),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-columns-layout",((e,t)=>y.includes(t)?{...e,supports:{...e.supports,layout:!1,background:{backgroundImage:!0}}}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-group-variations",((e,t)=>"core/group"===t?{...e,variations:e.variations.filter((e=>"group"===e.name)),supports:{...e.supports,layout:!1}}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-buttons",((e,t)=>"core/buttons"===t?{...e,supports:{...e.supports,layout:!1,__experimentalEmailFlexLayout:!0}}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-post-content",((e,t)=>{return"core/post-content"===t?{...e,edit:(o=e.edit,function(e){const{postId:t,postType:n}=e.context,{__unstableLayoutClassNames:r}=e;return t&&n?(0,l.jsx)(o,{...e}):(0,l.jsx)(w,{layoutClassNames:r})})}:e;var o})),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-quote",((e,t)=>"core/quote"===t?{...e,styles:[],supports:{...e.supports,align:[]}}:e)),(0,b.registerFormatType)("woocommerce-email-editor/shortcode",{name:"woocommerce-email-editor/shortcode",title:(0,x.__)("Personalization Tags","woocommerce"),className:"woocommerce-email-editor-personalization-tags",tagName:"span",attributes:{},edit:O}),(0,b.registerFormatType)("woocommerce-email-editor/link-shortcode",{name:"woocommerce-email-editor/link-shortcode",title:(0,x.__)("Personalization Tags Link","woocommerce"),className:"woocommerce-email-editor-personalization-tags-link",tagName:"a",attributes:{"data-link-href":"data-link-href",contenteditable:"contenteditable",style:"style"},edit:null}),(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-live-content-update",V),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/block-support",(e=>e.supports?.shadow?{...e,supports:{...e.supports,shadow:!1}}:e)),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-social-link-variations",((e,t)=>"core/social-link"===t?{...e,variations:e.variations.filter((e=>q.includes(e.name))),supports:{...e.supports,layout:!1}}:e)),(0,m.registerBlockVariation)("core/social-links",{name:"social-links-default",title:"Social Icons",attributes:{openInNewTab:!0,showLabels:!1,align:"center",className:"is-style-logos-only"},isDefault:!0,innerBlocks:[{name:"core/social-link",attributes:{service:"wordpress",url:"https://wordpress.org"}},{name:"core/social-link",attributes:{service:"facebook",url:"https://www.facebook.com/WordPress/"}},{name:"core/social-link",attributes:{service:"x",url:"https://x.com/WordPress"}}]}),(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/disable-social-links-icon-color",Z),(0,d.addAction)("core.registerPostTypeSchema","woocommerce-email-editor/modify-move-to-trash-action",(e=>{pe(e)})),(0,d.addAction)("core.registerPostTypeActions","woocommerce-email-editor/modify-move-to-trash-action",(e=>{pe(e)})),(0,m.registerBlockVariation)("core/site-logo",{name:"site-logo-default",title:"Site Logo",attributes:{align:"center",width:120},isDefault:!0}),(0,u.registerCoreBlocks)(),G()(W)}var _e=o(4921);const ge=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M9 9v6h11V9H9zM4 20h1.5V4H4v16z"})}),he=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M12.5 15v5H11v-5H4V9h7V4h1.5v5h7v6h-7Z"})}),ye=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M4 15h11V9H4v6zM18.5 4v16H20V4h-1.5z"})}),xe="__experimentalEmailFlexLayout";function we(e){return(0,m.hasBlockSupport)(e,xe)}function fe({justificationValue:e,onChange:t,isToolbar:o=!1}){const n=[{value:"left",icon:ge,label:(0,x.__)("Justify items left","woocommerce")},{value:"center",icon:he,label:(0,x.__)("Justify items center","woocommerce")},{value:"right",icon:ye,label:(0,x.__)("Justify items right","woocommerce")}];if(o){const o=n.map((e=>e.value));return(0,l.jsx)(_.JustifyContentControl,{value:e,onChange:t,allowedControls:o,popoverProps:{placement:"bottom-start"}})}return(0,l.jsx)(v.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,label:(0,x.__)("Justification","woocommerce"),value:e,onChange:t,className:"block-editor-hooks__flex-layout-justification-controls",children:n.map((({value:e,icon:t,label:o})=>(0,l.jsx)(v.__experimentalToggleGroupControlOptionIcon,{value:e,icon:t,label:o},e)))})}function be({setAttributes:e,attributes:t,name:o}){if(!(0,m.getBlockSupport)(o,xe,{}))return null;const{justifyContent:n="left"}=t.layout||{},r=o=>{e({layout:{...t.layout,justifyContent:o}})},s=()=>{const{justifyContent:o,...n}=t.layout||{};e({layout:n})};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(_.InspectorControls,{children:(0,l.jsx)(v.__experimentalToolsPanel,{label:(0,x.__)("Layout","woocommerce"),resetAll:s,children:(0,l.jsx)(v.__experimentalToolsPanelItem,{isShownByDefault:!0,onDeselect:s,hasValue:()=>t.layout?.justifyContent||!1,label:(0,x.__)("Justification","woocommerce"),children:(0,l.jsx)(v.Flex,{children:(0,l.jsx)(v.FlexItem,{children:(0,l.jsx)(fe,{justificationValue:n,onChange:r})})})})})}),(0,l.jsx)(_.BlockControls,{group:"block",__experimentalShareWithChildBlocks:!0,children:(0,l.jsx)(fe,{justificationValue:n,onChange:r,isToolbar:!0})})]})}function ve(e){return we(e.name)?{...e,attributes:{...e.attributes,layout:{type:"object"}}}:e}const je=(0,g.createHigherOrderComponent)((e=>t=>[we(t.name)&&(0,l.jsx)(be,{...t},"layout"),(0,l.jsx)(e,{...t},"edit")]),"withLayoutControls");function ke({block:e,props:t}){const{attributes:o}=t,{layout:n}=o,r=`is-content-justification-${n?.justifyContent||"left"}`,s=(0,_e.A)(r,"is-layout-email-flex is-layout-flex");return(0,l.jsx)(e,{...t,className:s})}const Se=(0,g.createHigherOrderComponent)((e=>function(t){return we(t.name)?(0,l.jsx)(ke,{block:e,props:t}):(0,l.jsx)(e,{...t})}),"withLayoutStyles"),Ce=window.wp.commands;var Ee=o(76597),Te=o.n(Ee);function Pe(){const{globalStylePost:e}=(0,a.useSelect)((e=>({globalStylePost:e(z.H).getGlobalEmailStylesPost()||null})),[]),t=(0,c.useCallback)((t=>{e&&(0,a.dispatch)(se.store).editEntityRecord("postType","wp_global_styles",e.id,{styles:t.styles,settings:t.settings})}),[e]);return{userTheme:{settings:e?.settings,styles:e?.styles},updateUserTheme:t}}function Ne(e){if("string"!=typeof e)return null;const t=e.match(/^var:preset\|([a-zA-Z0-9-]+)\|([a-zA-Z0-9-]+)$/);return t?`--wp--preset--${t[1]}--${t[2]}`:null}function Be(e){const t=Ne(e);return t?`var(${t})`:e}function Ie(e){const t=Ne(e);if(!t)return e;const o=document.querySelector(":root");return o&&getComputedStyle(o).getPropertyValue(t).trim()||e}const Me=[];function He(){const{userTheme:e}=Pe(),{editorTheme:t,layout:o,deviceType:n,editorSettingsStyles:r}=(0,a.useSelect)((e=>{const{getEditorSettings:t,getDeviceType:o}=e(R.store),n=t();return{editorTheme:e(z.H).getTheme(),layout:n.__experimentalFeatures?.layout,deviceType:o(),editorSettingsStyles:n.styles}}),[]),s=(0,c.useMemo)((()=>Te().all([{},t||{},e||{}])),[t,e]),[i]=X(s);let l="";o&&"Mobile"!==n&&(l=`display:flow-root; width:${o?.contentSize}; margin: 0 auto;box-sizing: border-box;`);const d=s.styles?.spacing?.padding;return d&&(l+=`padding-left:${Be(d.left)};`,l+=`padding-right:${Be(d.right)};`),[(0,c.useMemo)((()=>[...null!=i?i:[],{css:`.is-root-container{ ${l} }`},...null!=r?r:[]]),[i,r,l])||Me]}const ze=[];function Fe(e,t){return e.map((e=>"core/post-content"===e.name?{...e,name:"core/group",innerBlocks:t}:e.innerBlocks?.length?{...e,innerBlocks:Fe(e.innerBlocks,t)}:e))}const Ae={};function Le(e=""){const{templates:t,patterns:o,emailPosts:n,hasEmailPosts:r}=(0,a.useSelect)((t=>{const o="swap"!==e?t(z.H).getSentEmailEditorPosts():void 0;return{templates:t(z.H).getEmailTemplates(),patterns:t(z.H).getBlockPatternsForEmailTemplate(),emailPosts:o,hasEmailPosts:!(!o||!o?.length)}}),[e]),s=(0,c.useMemo)((()=>{let n=[];const r=e&&(0,m.parse)(e);if(n=r?[{blocks:r}]:o,!n||!t)return ze;const s=[];return t?.filter((e=>"email-general"!==e.slug))?.forEach((e=>{n?.forEach((t=>{let o=(0,m.parse)(e.content?.raw);o=Fe(o,t.blocks),s.push({id:e.id,slug:e.slug,previewContentParsed:o,emailParsed:t.blocks,template:e,category:"basic",type:e.type,displayName:t.title?`${e.title.rendered} - ${t.title}`:e.title.rendered})}))})),s}),[t,o,e]),i=(0,c.useMemo)((()=>n?.map((e=>{const t=(0,d.applyFilters)("woocommerce_email_editor_preferred_template_title","",e),{postTemplateContent:o}=function(e,t=[]){const o=e.template,n={postTemplateContent:null};if(!o)return n;if(Ae[o])return Ae[o];const r=t.find((e=>e.slug===o));if(!r)return n;const s={postTemplateContent:r?.template};return Ae[o]=s,s}(e,s),n=(0,m.parse)(e.content?.raw);let r=n;o?.content?.raw&&(r=Fe((0,m.parse)(o?.content?.raw),n));const i={...e,title:{raw:e.title.raw,rendered:t||e.title.rendered}};return{id:e.id,slug:e.slug,previewContentParsed:r,emailParsed:n,category:"recent",type:e.type,displayName:i.title.rendered,template:i}}))),[n,s]);return[s||ze,i||ze,r]}const Re=(0,c.forwardRef)((function({icon:e,size:t=24,...o},n){return(0,c.cloneElement)(e,{width:t,height:t,...o,ref:n})})),Oe=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})}),Ve=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})}),De=(0,window.wp.priorityQueue.createQueue)();function Ge({children:e,placeholder:t}){const[o,n]=(0,c.useState)(!1);return(0,c.useEffect)((()=>{const e={};return De.add(e,(()=>{(0,c.flushSync)((()=>{n(!0)}))})),()=>{De.cancel(e)}}),[]),o?e:t}function $e(){return(0,l.jsxs)("div",{className:"block-editor-inserter__no-results",children:[(0,l.jsx)(Re,{className:"block-editor-inserter__no-results-icon",icon:Oe}),(0,l.jsx)("p",{children:(0,x.__)("No recent templates.","woocommerce")}),(0,l.jsx)("p",{children:(0,x.__)("Your recent creations will appear here as soon as you begin.","woocommerce")})]})}const We=(0,c.memo)((function({templates:e,onTemplateSelection:t,selectedCategory:o}){const{layout:n}=(0,a.useSelect)((e=>{const{getEditorSettings:t}=e(R.store);return{layout:t().__experimentalFeatures.layout}})),[r]=He(),s=r.reduce(((e,t)=>{var o;return e+(null!==(o=t.css)&&void 0!==o?o:"")}),"")+`.is-root-container { width: ${n.contentSize}; margin: 0 auto; }`;return"recent"===o&&0===e.length?(0,l.jsx)($e,{}):(0,l.jsx)("div",{className:"block-editor-block-patterns-list",role:"listbox",children:e.map((e=>(0,l.jsx)("div",{className:"block-editor-block-patterns-list__list-item email-editor-pattern__list-item",children:(0,l.jsx)("div",{className:"block-editor-block-patterns-list__item",role:"button",tabIndex:0,onClick:()=>{t(e)},onKeyPress:o=>{"Enter"!==o.key&&" "!==o.key||t(e)},children:(0,l.jsxs)(Ge,{placeholder:(0,l.jsx)("p",{children:(0,x.__)("rendering template","woocommerce")}),children:[(0,l.jsx)(_.BlockPreview,{blocks:e.previewContentParsed,viewportWidth:900,minHeight:300,additionalStyles:[{css:s}]}),(0,l.jsx)(v.__experimentalHStack,{className:"block-editor-patterns__pattern-details",children:(0,l.jsx)("h4",{className:"block-editor-block-patterns-list__item-title",children:e.displayName})})]})})},`${e.slug}_${e.displayName}_${e.id}`)))})}),((e,t)=>e.templates.length===t.templates.length&&e.selectedCategory===t.selectedCategory));function Ue({templates:e,onTemplateSelection:t,selectedCategory:o}){const n=(0,c.useMemo)((()=>e.filter((e=>e.category===o))),[o,e]);return(0,l.jsxs)("div",{className:"block-editor-block-patterns-explorer__list",children:["recent"===o&&(0,l.jsx)("div",{className:"email-editor-recent-templates-info",children:(0,l.jsxs)(v.__experimentalHStack,{spacing:1,expanded:!1,justify:"start",children:[(0,l.jsx)(Re,{icon:Ve}),(0,l.jsx)("p",{children:(0,x.__)("Templates created on the legacy editor will not appear here.","woocommerce")})]})}),(0,l.jsx)(We,{templates:n,onTemplateSelection:t,selectedCategory:o})]})}function qe({selectedCategory:e,templateCategories:t,onClickCategory:o}){const n="block-editor-block-patterns-explorer__sidebar";return(0,l.jsx)("div",{className:n,children:(0,l.jsx)("div",{className:`${n}__categories-list`,children:t.map((({name:t,label:r})=>(0,l.jsx)(v.Button,{label:r,className:`${n}__categories-list__item`,isPressed:e===t,onClick:()=>{o(t)},children:r},t)))})})}const Ze=[{name:"recent",label:"Recent"},{name:"basic",label:"Basic"}],Je=(0,c.memo)((function({hasEmailPosts:e,templates:t,handleTemplateSelection:o,templateSelectMode:n}){const[r,s]=(0,c.useState)(Ze[1].name),i="swap"===n,a=Ze.filter((({name:e})=>"recent"!==e||!i));return(0,c.useEffect)((()=>{setTimeout((()=>{e&&!i&&s(Ze[0].name)}),1e3)}),[e,i]),(0,l.jsxs)("div",{className:"block-editor-block-patterns-explorer",children:[(0,l.jsx)(qe,{templateCategories:a,selectedCategory:r,onClickCategory:e=>{I("template_select_modal_category_change",{category:e}),s(e)}}),(0,l.jsx)(Ue,{templates:t,onTemplateSelection:o,selectedCategory:r})]})}));function Ye({onSelectCallback:e,closeCallback:t=null,previewContent:o="",postType:n}){const r=o?"swap":"new";M("template_select_modal_opened",{templateSelectMode:r});const[s,i,c]=Le(o),d=s?.length>0,m=t=>{const s=t.type===n,i=t.template;I("template_select_modal_template_selected",{templateSlug:t.slug,templateSelectMode:r,templateType:t.type}),o||(0,a.dispatch)(R.store).resetEditorBlocks(t.emailParsed),(0,a.dispatch)(z.H).setTemplateToPost(s?i.template:t.slug),e()},p=()=>{var e;const t=null!==(e=s[0])&&void 0!==e?e:null;t&&(I("template_select_modal_handle_close_without_template_selected"),m(t))};return(0,l.jsxs)(v.Modal,{title:"new"===r?(0,x.__)("Start with an email preset","woocommerce"):(0,x.__)("Select a template","woocommerce"),onRequestClose:()=>(I("template_select_modal_closed",{templateSelectMode:r}),t?t():p()),isFullScreen:!0,children:[(0,l.jsx)(Je,{hasEmailPosts:c,templates:[...s,...i],handleTemplateSelection:m,templateSelectMode:r}),(0,l.jsx)(v.Flex,{className:"email-editor-modal-footer",justify:"flex-end",children:(0,l.jsx)(v.FlexItem,{children:(0,l.jsx)(v.Button,{variant:"tertiary",className:"email-editor-start_from_scratch_button",onClick:()=>(I("template_select_modal_start_from_scratch_clicked"),p()),isBusy:!d,children:(0,x.__)("Start from scratch","woocommerce")})})})]})}function Ke(){const[e,t]=(0,c.useState)(!1),{emailContentIsEmpty:o,emailHasEdits:n,postType:r}=(0,a.useSelect)((e=>({emailContentIsEmpty:e(z.H).hasEmptyContent(),emailHasEdits:e(z.H).hasEdits(),postType:e(z.H).getEmailPostType()})),[]);return!o||n||e?null:(0,l.jsx)(Ye,{onSelectCallback:()=>t(!0),postType:r})}const Xe=(0,l.jsx)(le.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)(le.Path,{d:"M12 4c-4.4 0-8 3.6-8 8v.1c0 4.1 3.2 7.5 7.2 7.9h.8c4.4 0 8-3.6 8-8s-3.6-8-8-8zm0 15V5c3.9 0 7 3.1 7 7s-3.1 7-7 7z"})}),Qe=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M6.9 7L3 17.8h1.7l1-2.8h4.1l1 2.8h1.7L8.6 7H6.9zm-.7 6.6l1.5-4.3 1.5 4.3h-3zM21.6 17c-.1.1-.2.2-.3.2-.1.1-.2.1-.4.1s-.3-.1-.4-.2c-.1-.1-.1-.3-.1-.6V12c0-.5 0-1-.1-1.4-.1-.4-.3-.7-.5-1-.2-.2-.5-.4-.9-.5-.4 0-.8-.1-1.3-.1s-1 .1-1.4.2c-.4.1-.7.3-1 .4-.2.2-.4.3-.6.5-.1.2-.2.4-.2.7 0 .3.1.5.2.8.2.2.4.3.8.3.3 0 .6-.1.8-.3.2-.2.3-.4.3-.7 0-.3-.1-.5-.2-.7-.2-.2-.4-.3-.6-.4.2-.2.4-.3.7-.4.3-.1.6-.1.8-.1.3 0 .6 0 .8.1.2.1.4.3.5.5.1.2.2.5.2.9v1.1c0 .3-.1.5-.3.6-.2.2-.5.3-.9.4-.3.1-.7.3-1.1.4-.4.1-.8.3-1.1.5-.3.2-.6.4-.8.7-.2.3-.3.7-.3 1.2 0 .6.2 1.1.5 1.4.3.4.9.5 1.6.5.5 0 1-.1 1.4-.3.4-.2.8-.6 1.1-1.1 0 .4.1.7.3 1 .2.3.6.4 1.2.4.4 0 .7-.1.9-.2.2-.1.5-.3.7-.4h-.3zm-3-.9c-.2.4-.5.7-.8.8-.3.2-.6.2-.8.2-.4 0-.6-.1-.9-.3-.2-.2-.3-.6-.3-1.1 0-.5.1-.9.3-1.2s.5-.5.8-.7c.3-.2.7-.3 1-.5.3-.1.6-.3.7-.6v3.4z"})}),et=(0,l.jsx)(le.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)(le.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"})}),tt=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),ot={};function nt(e){return!Object.keys(e).some((t=>Object.keys(e[t]).length>0))}function rt(e){const t=e=>{if("object"==typeof e&&null!==e||void 0===e){if(Array.isArray(e)&&0===e.length)return;for(const o in e)if(e.hasOwnProperty(o)){const n=t(e[o]);void 0===n||nt(n)?delete e[o]:e[o]=n}}return e};return t(e)}const st=()=>{const{userTheme:e,updateUserTheme:t}=Pe(),o=(0,c.useMemo)((()=>e?rt(function(e){const t=e=>{if("object"==typeof e&&null!==e)for(const o in e)e.hasOwnProperty(o)&&(e[o]=t(e[o]));else if("string"==typeof e)return e.replace(/var\(--([a-z]+)--([a-z]+(?:--[a-z0-9]+(?:-[a-z0-9]+)*)*)--([a-z0-9-]+)\)/g,((e,t,o,n)=>`var:${o.split("--").concat(n).join("|")}`));return e};return t(e)}(e?.styles)):ot),[e]),{styles:n}=(0,a.useSelect)((e=>({styles:e(z.H).getStyles()}))),r=(0,c.useCallback)((o=>{const n={...e,styles:rt(o)};t(n)}),[t,e]),s=(0,c.useCallback)(((o,n)=>{const r=function(e,t,o){const n=Array.isArray(t)?[...t]:[t],r=Array.isArray(e)?[...e]:{...e},s=n.pop();let i=r;return n.forEach((e=>{const t=i[e];i[e]=Array.isArray(t)?[...t]:{...t},i=i[e]})),i[s]=o,r}(e,["styles",...o],n);t(r)}),[t,e]);return{styles:(0,c.useMemo)((()=>n?o?Te().all([n,o]):n:ot),[n,o]),userStyles:e?.styles,defaultStyles:n,updateStyleProp:s,updateStyles:r}},it=[],lt={start:{scale:1,opacity:1},hover:{scale:0,opacity:0}},at={hover:{opacity:1},start:{opacity:.5}},ct={hover:{scale:1,opacity:1},start:{scale:0,opacity:0}};function dt({label:e,isFocused:t,withHoverView:o}){const{colors:n}=(0,a.useSelect)((e=>({colors:e(z.H).getPaletteColors()})),[]),r=(0,c.useMemo)((()=>(n?.theme||it).concat(n?.default||it)),[n]),{styles:s}=st(),{backgroundColor:i,headingColor:d,highlightedColors:m}=(0,c.useMemo)((()=>{const e=Ie(s?.color?.background)||"white",t=Ie(s?.color?.text)||"black",o=Ie(s?.elements?.h1?.color?.text)||t,n=Ie(s?.elements?.link?.color?.text)||o,i=Ie(s?.elements?.button?.color?.background)||n,l=r.find((({color:e})=>e.toLowerCase()===t.toLowerCase())),a=r.find((({color:e})=>e.toLowerCase()===i.toLowerCase())),c=[...l?[l]:it,...a?[a]:it,...r].filter((({color:t},o,n)=>t.toLowerCase()!==e.toLowerCase()&&o===n.findIndex((e=>e.color.toLowerCase()===t.toLowerCase())))).slice(0,2);return{backgroundColor:e,headingColor:o,highlightedColors:c}}),[s,r]),p=s?.elements?.heading?.typography?.fontWeight||"inherit",u=s?.elements?.heading?.typography?.fontFamily||"inherit",[_,g]=(0,c.useState)(!1);return(0,l.jsx)("div",{onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:(0,l.jsxs)(v.__unstableMotion.div,{style:{height:152,width:"100%",background:i,cursor:o?"pointer":void 0},initial:"start",animate:(_||t)&&e?"hover":"start",children:[(0,l.jsx)(v.__unstableMotion.div,{variants:lt,style:{height:"100%",overflow:"hidden"},children:(0,l.jsxs)(v.__experimentalHStack,{spacing:10,justify:"center",style:{height:"100%",overflow:"hidden"},children:[(0,l.jsx)(v.__unstableMotion.div,{style:{fontFamily:u,fontSize:65,color:d,fontWeight:p},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:.3,type:"tween"},children:"Aa"}),(0,l.jsx)(v.__experimentalVStack,{spacing:4,children:m.map((({slug:e,color:t},o)=>(0,l.jsx)(v.__unstableMotion.div,{style:{height:32,width:32,background:t,borderRadius:16},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:1===o?.2:.1}},e)))})]})}),(0,l.jsx)(v.__unstableMotion.div,{variants:o&&at,style:{height:"100%",width:"100%",position:"absolute",top:0,overflow:"hidden",filter:"blur(60px)",opacity:.1},children:(0,l.jsx)(v.__experimentalHStack,{spacing:0,justify:"flex-start",style:{height:"100%",overflow:"hidden"},children:r.slice(0,4).map((({color:e})=>(0,l.jsx)("div",{style:{height:"100%",background:e,flexGrow:1}},e)))})}),(0,l.jsx)(v.__unstableMotion.div,{variants:ct,style:{height:"100%",width:"100%",overflow:"hidden",position:"absolute",top:0},children:(0,l.jsx)(v.__experimentalVStack,{spacing:3,justify:"center",style:{height:"100%",overflow:"hidden",padding:10,boxSizing:"border-box"},children:e&&(0,l.jsx)("div",{style:{fontSize:40,fontFamily:u,color:d,fontWeight:p,lineHeight:"1em",textAlign:"center"},children:e})})})]})})}function mt(){return(0,l.jsx)(v.Card,{size:"small",className:"edit-site-global-styles-screen-root",variant:"primary",children:(0,l.jsx)(v.CardBody,{children:(0,l.jsxs)(v.__experimentalVStack,{spacing:4,children:[(0,l.jsx)(v.Card,{children:(0,l.jsx)(v.CardMedia,{children:(0,l.jsx)(dt,{})})}),(0,l.jsxs)(v.__experimentalItemGroup,{children:[(0,l.jsx)(v.__experimentalNavigatorButton,{path:"/typography",onClick:()=>I("styles_sidebar_navigation_click",{path:"typography"}),children:(0,l.jsx)(v.__experimentalItem,{children:(0,l.jsxs)(v.__experimentalHStack,{justify:"flex-start",children:[(0,l.jsx)(v.Icon,{icon:Qe,size:24}),(0,l.jsx)(v.FlexItem,{children:(0,x.__)("Typography","woocommerce")})]})})}),(0,l.jsx)(v.__experimentalNavigatorButton,{path:"/colors",onClick:()=>I("styles_sidebar_navigation_click",{path:"colors"}),children:(0,l.jsx)(v.__experimentalItem,{children:(0,l.jsxs)(v.__experimentalHStack,{justify:"flex-start",children:[(0,l.jsx)(v.Icon,{icon:et,size:24}),(0,l.jsx)(v.FlexItem,{children:(0,x.__)("Colors","woocommerce")})]})})}),(0,l.jsx)(v.__experimentalNavigatorButton,{path:"/layout",onClick:()=>I("styles_sidebar_navigation_click",{path:"layout"}),children:(0,l.jsx)(v.__experimentalItem,{children:(0,l.jsxs)(v.__experimentalHStack,{justify:"flex-start",children:[(0,l.jsx)(v.Icon,{icon:tt,size:24}),(0,l.jsx)(v.FlexItem,{children:(0,x.__)("Layout","woocommerce")})]})})})]})]})})})}const pt={typography:{},color:{}},ut=(e,t,o="heading",n=!1)=>{switch(t){case"text":return{typography:e.typography,color:e.color};case"heading":return((e,t="heading",o=!1)=>o?Te().all([pt,e.elements.heading||{},e.elements[t]||{}]):{...pt,...e.elements.heading||{},...e.elements[t]||{}})(e,null!=o?o:"heading",n);default:return e.elements[t]||pt}};function _t({element:e,label:t}){const{styles:o}=st(),n=ut(o,e,null,!0),{fontFamily:r,fontStyle:s,fontWeight:i,letterSpacing:a,textDecoration:c,textTransform:d}=n.typography,m=n.color?.text||"inherit",p=n.color?.background||"#f0f0f0",u=(0,x.sprintf)((0,x.__)("Typography %s styles","woocommerce"),t);return(0,l.jsx)(v.__experimentalItem,{children:(0,l.jsx)(v.__experimentalNavigatorButton,{path:`/typography/${e}`,"aria-label":u,onClick:()=>I("styles_sidebar_screen_typography_button_click",{element:e,label:t,path:`typography/${e}`}),children:(0,l.jsxs)(v.__experimentalHStack,{justify:"flex-start",children:[(0,l.jsx)(v.FlexItem,{className:"edit-site-global-styles-screen-typography__indicator",style:{fontFamily:null!=r?r:"serif",background:p,color:m,fontStyle:null!=s?s:"normal",fontWeight:null!=i?i:"normal",letterSpacing:null!=a?a:"normal",textDecoration:null!=c?c:"link"===e?"underline":"none",textTransform:null!=d?d:"none"},children:"Aa"}),(0,l.jsx)(v.FlexItem,{children:t})]})})})}const gt=function(){return(0,l.jsx)(v.Card,{size:"small",variant:"primary",isBorderless:!0,children:(0,l.jsx)(v.CardBody,{children:(0,l.jsxs)(v.__experimentalVStack,{spacing:3,children:[(0,l.jsx)(v.__experimentalHeading,{level:3,className:"edit-site-global-styles-subtitle",children:(0,x.__)("Elements","woocommerce")}),(0,l.jsxs)(v.__experimentalItemGroup,{isBordered:!0,isSeparated:!0,size:"small",children:[(0,l.jsx)(_t,{element:"text",label:(0,x.__)("Text","woocommerce")}),(0,l.jsx)(_t,{element:"link",label:(0,x.__)("Links","woocommerce")}),(0,l.jsx)(_t,{element:"heading",label:(0,x.__)("Headings","woocommerce")}),(0,l.jsx)(_t,{element:"button",label:(0,x.__)("Buttons","woocommerce")})]})]})})})},ht=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})}),yt=v.Navigator||v.__experimentalNavigatorProvider;function xt({title:e,description:t,onBack:o}){return(0,l.jsxs)(v.__experimentalVStack,{spacing:0,children:[(0,l.jsx)(v.__experimentalView,{children:(0,l.jsx)(v.__experimentalSpacer,{marginBottom:0,paddingX:4,paddingY:3,children:(0,l.jsxs)(v.__experimentalHStack,{spacing:2,children:[(0,l.jsx)(yt.BackButton,{style:{minWidth:24,padding:0},icon:ht,size:"small","aria-label":(0,x.__)("Navigate to the previous view","woocommerce"),onClick:o}),(0,l.jsx)(v.__experimentalSpacer,{children:(0,l.jsx)(v.__experimentalHeading,{className:"woocommerce-email-editor-styles-header",level:2,size:13,children:e})})]})})}),t&&(0,l.jsx)("p",{className:"woocommerce-email-editor-styles-header-description",children:t})]})}v.Navigator||(yt.Screen=v.__experimentalNavigatorScreen,yt.BackButton=v.__experimentalNavigatorBackButton);const wt=xt;function ft(){return M("styles_sidebar_screen_typography_opened"),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(wt,{title:(0,x.__)("Typography","woocommerce"),description:(0,x.__)("Manage the typography settings for different elements.","woocommerce")}),(0,l.jsx)(gt,{})]})}const bt={fontFamily:!0,fontSize:!0,fontAppearance:!0,lineHeight:!0,letterSpacing:!1,textTransform:!1,textDecoration:!1,writingMode:!0,textColumns:!0},vt=function({element:e,headingLevel:t,defaultControls:o=bt}){const[n,r]=(0,_.useSettings)("typography.fontSizes","typography.fontFamilies"),s=r?.default||[],{styles:i,defaultStyles:a,updateStyleProp:d}=st(),m=ut(i,e,t),p=ut(a,e,t),{fontFamily:u,fontSize:g,fontStyle:h,fontWeight:y,lineHeight:w,letterSpacing:f,textDecoration:b,textTransform:j}=m.typography,{fontFamily:k,fontSize:S,fontStyle:C,fontWeight:E,lineHeight:T,letterSpacing:P,textDecoration:N,textTransform:B}=p.typography,M="heading"!==e||"heading"!==t,z=(0,c.useCallback)(((o,n)=>{d("heading"===e?["elements",t,...o]:"text"===e?[...o]:["elements",e,...o],n)}),[e,d,t]),F=t=>{z(["typography","letterSpacing"],t),H("styles_sidebar_screen_typography_element_panel_set_letter_spacing",{element:e,newValue:t,selectedDefaultLetterSpacing:t===P})},A=t=>{z(["typography","lineHeight"],t),H("styles_sidebar_screen_typography_element_panel_set_line_height",{element:e,newValue:t,selectedDefaultLineHeight:t===T})},L=o=>{z(["typography","fontSize"],o),H("styles_sidebar_screen_typography_element_panel_set_font_size",{element:e,headingLevel:t,newValue:o,selectedDefaultFontSize:o===S})},R=t=>{z(["typography","fontFamily"],t),H("styles_sidebar_screen_typography_element_panel_set_font_family",{element:e,newValue:t,selectedDefaultFontFamily:t===k})},O=t=>{z(["typography","textDecoration"],t),H("styles_sidebar_screen_typography_element_panel_set_text_decoration",{element:e,newValue:t,selectedDefaultTextDecoration:t===N})},V=t=>{z(["typography","textTransform"],t),H("styles_sidebar_screen_typography_element_panel_set_text_transform",{element:e,newValue:t,selectedDefaultTextTransform:t===B})},D=({fontStyle:t,fontWeight:o})=>{z(["typography","fontStyle"],t),z(["typography","fontWeight"],o),H("styles_sidebar_screen_typography_element_panel_set_font_appearance",{element:e,newFontStyle:t,newFontWeight:o,selectedDefaultFontStyle:t===C,selectedDefaultFontWeight:o===E})};return(0,l.jsxs)(v.__experimentalToolsPanel,{label:(0,x.__)("Typography","woocommerce"),resetAll:()=>{z(["typography"],{}),I("styles_sidebar_screen_typography_element_panel_reset_all_styles_selected",{element:e,headingLevel:t})},children:[(0,l.jsx)(v.__experimentalToolsPanelItem,{label:(0,x.__)("Font family","woocommerce"),hasValue:()=>u!==k,onDeselect:()=>R(void 0),isShownByDefault:o.fontFamily,children:(0,l.jsx)(_.__experimentalFontFamilyControl,{value:u,onChange:R,size:"__unstable-large",fontFamilies:s,__nextHasNoMarginBottom:!0})}),M&&(0,l.jsx)(v.__experimentalToolsPanelItem,{label:(0,x.__)("Font size","woocommerce"),hasValue:()=>g!==S,onDeselect:()=>L(void 0),isShownByDefault:o.fontSize,children:(0,l.jsx)(v.FontSizePicker,{value:g,onChange:L,fontSizes:n,disableCustomFontSizes:!1,withReset:!1,withSlider:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})}),(0,l.jsx)(v.__experimentalToolsPanelItem,{className:"single-column",label:(0,x.__)("Appearance","woocommerce"),hasValue:()=>y!==E||h!==C,onDeselect:()=>{D({fontStyle:void 0,fontWeight:void 0})},isShownByDefault:o.fontAppearance,children:(0,l.jsx)(_.__experimentalFontAppearanceControl,{value:{fontStyle:h,fontWeight:y},onChange:D,hasFontStyles:!0,hasFontWeights:!0,size:"__unstable-large"})}),(0,l.jsx)(v.__experimentalToolsPanelItem,{className:"single-column",label:(0,x.__)("Line height","woocommerce"),hasValue:()=>w!==T,onDeselect:()=>A(void 0),isShownByDefault:o.lineHeight,children:(0,l.jsx)(_.LineHeightControl,{__nextHasNoMarginBottom:!0,__unstableInputWidth:"auto",value:w,onChange:A,size:"__unstable-large"})}),(0,l.jsx)(v.__experimentalToolsPanelItem,{className:"single-column",label:(0,x.__)("Letter spacing","woocommerce"),hasValue:()=>f!==P,onDeselect:()=>F(void 0),isShownByDefault:o.letterSpacing,children:(0,l.jsx)(_.__experimentalLetterSpacingControl,{value:f,onChange:F,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,l.jsx)(v.__experimentalToolsPanelItem,{className:"single-column",label:(0,x.__)("Text decoration","woocommerce"),hasValue:()=>b!==N,onDeselect:()=>O(void 0),isShownByDefault:o.textDecoration,children:(0,l.jsx)(_.__experimentalTextDecorationControl,{value:b,onChange:O,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,l.jsx)(v.__experimentalToolsPanelItem,{label:(0,x.__)("Letter case","woocommerce"),hasValue:()=>j!==B,onDeselect:()=>V(B),isShownByDefault:o.textTransform,children:(0,l.jsx)(_.__experimentalTextTransformControl,{value:j,onChange:V,showNone:!0,isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})})]})};function jt({element:e,headingLevel:t}){const{styles:o}=st(),n=ut(o,e,t,!0),{fontFamily:r,fontSize:s,fontStyle:i,fontWeight:a,lineHeight:c,letterSpacing:d,textDecoration:m,textTransform:p}=n.typography,u=n.color?.text||"inherit",_=n.color?.background||"#f0f0f0",g="link"===e?{textDecoration:null!=m?m:"underline"}:{};return(0,l.jsx)("div",{className:"edit-site-typography-preview",style:{fontFamily:null!=r?r:"serif",background:_,color:u,lineHeight:c,fontSize:s,fontStyle:i,fontWeight:a,letterSpacing:d,textDecoration:m,textTransform:p,...g},children:"Aa"})}const kt={text:{title:(0,x.__)("Text","woocommerce"),description:(0,x.__)("Manage the fonts and typography used on text.","woocommerce"),defaultControls:bt},link:{title:(0,x.__)("Links","woocommerce"),description:(0,x.__)("Manage the fonts and typography used on links.","woocommerce"),defaultControls:{...bt,textDecoration:!0}},heading:{title:(0,x.__)("Headings","woocommerce"),description:(0,x.__)("Manage the fonts and typography used on headings.","woocommerce"),defaultControls:{...bt,textTransform:!0}},button:{title:(0,x.__)("Buttons","woocommerce"),description:(0,x.__)("Manage the fonts and typography used on buttons.","woocommerce"),defaultControls:bt}};function St({element:e}){M("styles_sidebar_screen_typography_element_opened",{element:e});const[t,o]=(0,c.useState)("heading");return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(wt,{title:kt[e].title,description:kt[e].description}),(0,l.jsx)(v.__experimentalSpacer,{marginX:4,children:(0,l.jsx)(jt,{element:e,headingLevel:t})}),"heading"===e&&(0,l.jsx)(v.__experimentalSpacer,{marginX:4,marginBottom:"1em",children:(0,l.jsxs)(v.__experimentalToggleGroupControl,{label:(0,x.__)("Select heading level","woocommerce"),hideLabelFromVision:!0,value:t,onChange:e=>{o(e.toString()),I("styles_sidebar_screen_typography_element_heading_level_selected",{value:e})},isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0,children:[(0,l.jsx)(v.__experimentalToggleGroupControlOption,{value:"heading",label:(0,x._x)("All","heading levels","woocommerce")}),(0,l.jsx)(v.__experimentalToggleGroupControlOption,{value:"h1",label:(0,x._x)("H1","Heading Level","woocommerce")}),(0,l.jsx)(v.__experimentalToggleGroupControlOption,{value:"h2",label:(0,x._x)("H2","Heading Level","woocommerce")}),(0,l.jsx)(v.__experimentalToggleGroupControlOption,{value:"h3",label:(0,x._x)("H3","Heading Level","woocommerce")}),(0,l.jsx)(v.__experimentalToggleGroupControlOption,{value:"h4",label:(0,x._x)("H4","Heading Level","woocommerce")}),(0,l.jsx)(v.__experimentalToggleGroupControlOption,{value:"h5",label:(0,x._x)("H5","Heading Level","woocommerce")}),(0,l.jsx)(v.__experimentalToggleGroupControlOption,{value:"h6",label:(0,x._x)("H6","Heading Level","woocommerce")})]})}),(0,l.jsx)(vt,{element:e,headingLevel:t,defaultControls:kt[e].defaultControls})]})}function Ct(){M("styles_sidebar_screen_colors_opened");const{userStyles:e,styles:t,updateStyles:o}=st(),n=(0,a.useSelect)((e=>e(z.H).getTheme()),[]);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(wt,{title:(0,x.__)("Colors","woocommerce"),description:(0,x.__)("Manage palettes and the default color of different global elements.","woocommerce")}),(0,l.jsx)(K,{value:e,inheritedValue:t,onChange:e=>{o(e),I("styles_sidebar_screen_colors_styles_updated")},settings:n?.settings,panelId:"colors"})]})}function Et(){const[e]=(0,_.useSettings)("spacing.units"),t=(0,v.__experimentalUseCustomUnits)({availableUnits:e}),{styles:o,defaultStyles:n,updateStyleProp:r}=st();return(0,l.jsxs)(v.__experimentalToolsPanel,{label:(0,x.__)("Dimensions","woocommerce"),resetAll:()=>{r(["spacing"],n.spacing),I("styles_sidebar_screen_layout_dimensions_reset_all_selected")},children:[(0,l.jsx)(v.__experimentalToolsPanelItem,{isShownByDefault:!0,hasValue:()=>!(0,T.isEqual)(o.spacing.padding,n.spacing.padding),label:(0,x.__)("Padding","woocommerce"),onDeselect:()=>{r(["spacing","padding"],n.spacing.padding),I("styles_sidebar_screen_layout_dimensions_padding_reset_clicked")},className:"tools-panel-item-spacing",children:(0,l.jsx)(_.__experimentalSpacingSizesControl,{allowReset:!0,values:o.spacing.padding,onChange:e=>{r(["spacing","padding"],e),H("styles_sidebar_screen_layout_dimensions_padding_updated",{value:e})},label:(0,x.__)("Padding","woocommerce"),sides:["horizontal","vertical","top","left","right","bottom"],units:t})}),(0,l.jsx)(v.__experimentalToolsPanelItem,{isShownByDefault:!0,label:(0,x.__)("Block spacing","woocommerce"),hasValue:()=>o.spacing.blockGap!==n.spacing.blockGap,onDeselect:()=>{r(["spacing","blockGap"],n.spacing.blockGap),I("styles_sidebar_screen_layout_dimensions_block_spacing_reset_clicked")},className:"tools-panel-item-spacing",children:(0,l.jsx)(_.__experimentalSpacingSizesControl,{label:(0,x.__)("Block spacing","woocommerce"),min:0,onChange:e=>{r(["spacing","blockGap"],e.top),H("styles_sidebar_screen_layout_dimensions_block_spacing_updated",{value:e})},showSideInLabel:!1,sides:["top"],values:{top:o.spacing.blockGap},allowReset:!0})})]})}function Tt(){return M("styles_sidebar_screen_layout_opened"),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(xt,{title:(0,x.__)("Layout","woocommerce")}),(0,l.jsx)(Et,{})]})}const Pt=(0,c.memo)((function(){const{userCanEditGlobalStyles:e}=(0,a.useSelect)((e=>{const{canEdit:t}=e(z.H).canUserEditGlobalEmailStyles();return{userCanEditGlobalStyles:t}}),[]);return e&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(R.PluginSidebarMoreMenuItem,{target:"email-styles-sidebar",icon:Xe,children:(0,x.__)("Email styles","woocommerce")}),(0,l.jsx)(R.PluginSidebar,{name:"email-styles-sidebar",icon:Xe,title:(0,x.__)("Styles","woocommerce"),className:"woocommerce-email-editor-styles-panel",header:(0,x.__)("Styles","woocommerce"),children:(0,l.jsxs)(yt,{initialPath:"/",children:[(0,l.jsx)(yt.Screen,{path:"/",children:(0,l.jsx)(mt,{})}),(0,l.jsx)(yt.Screen,{path:"/typography",children:(0,l.jsx)(ft,{})}),(0,l.jsx)(yt.Screen,{path:"/typography/text",children:(0,l.jsx)(St,{element:"text"})}),(0,l.jsx)(yt.Screen,{path:"/typography/link",children:(0,l.jsx)(St,{element:"link"})}),(0,l.jsx)(yt.Screen,{path:"/typography/heading",children:(0,l.jsx)(St,{element:"heading"})}),(0,l.jsx)(yt.Screen,{path:"/typography/button",children:(0,l.jsx)(St,{element:"button"})}),(0,l.jsx)(yt.Screen,{path:"/colors",children:(0,l.jsx)(Ct,{})}),(0,l.jsx)(yt.Screen,{path:"/layout",children:(0,l.jsx)(Tt,{})})]})})]})})),Nt=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),Bt=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),It=window.wp.keycodes,Mt=window.wp.url;var Ht;!function(e){e.SUCCESS="success",e.ERROR="error"}(Ht||(Ht={}));const zt=(0,c.memo)((function(){const e=(0,c.useRef)(null),{requestSendingNewsletterPreview:t,togglePreviewModal:o,updateSendPreviewEmail:n}=(0,a.useDispatch)(z.H),{toEmail:r,isSendingPreviewEmail:s,sendingPreviewStatus:i,isModalOpened:m,errorMessage:p,postType:u}=(0,a.useSelect)((e=>({...e(z.H).getPreviewState(),postType:e(z.H).getEmailPostType()})),[]),_=()=>{t(r)},g=(0,c.useMemo)((()=>(0,d.applyFilters)("woocommerce_email_editor_check_sending_method_configuration_link",`https://www.mailpoet.com/blog/mailpoet-smtp-plugin/?utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${u}`)),[u]),h=()=>{I("send_preview_email_modal_closed"),o(!1)};return(0,c.useEffect)((()=>{m&&(e.current?.focus(),I("send_preview_email_modal_opened"))}),[m]),m?(0,l.jsxs)(v.Modal,{className:"woocommerce-send-preview-email",title:(0,x.__)("Send a test email","woocommerce"),onRequestClose:h,focusOnMount:!1,children:[i===Ht.ERROR?(0,l.jsxs)("div",{className:"woocommerce-send-preview-modal-notice-error",children:[(0,l.jsx)("p",{children:(0,x.__)("Sorry, we were unable to send this email.","woocommerce")}),(0,l.jsx)("strong",{children:p&&(0,x.sprintf)((0,x.__)("Error: %s","woocommerce"),p)}),(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:g&&(0,c.createInterpolateElement)((0,x.__)("Please check your <link>sending method configuration</link> with your hosting provider.","woocommerce"),{link:(0,l.jsx)("a",{href:g,target:"_blank",rel:"noopener noreferrer",onClick:()=>I("send_preview_email_modal_check_sending_method_configuration_link_clicked")})})}),(0,l.jsx)("li",{children:(0,c.createInterpolateElement)((0,x.__)("Or, sign up for MailPoet Sending Service to easily send emails. <link>Sign up for free</link>","woocommerce"),{link:(0,l.jsx)("a",{href:`https://account.mailpoet.com/?s=1&g=1&utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${u}`,target:"_blank",rel:"noopener noreferrer",onClick:()=>I("send_preview_email_modal_sign_up_for_mailpoet_sending_service_link_clicked")},"sign-up-for-free")})})]})]}):null,(0,l.jsx)("p",{children:(0,x.__)("Send yourself a test email to test how your email would look like in different email apps.","woocommerce")}),(0,l.jsx)(v.TextControl,{label:(0,x.__)("Send to","woocommerce"),onChange:e=>{n(e),M("send_preview_email_modal_send_to_field_updated")},onKeyDown:e=>{const{keyCode:t}=e;t===It.ENTER&&(e.preventDefault(),_(),I("send_preview_email_modal_send_to_field_key_code_enter"))},className:"woocommerce-send-preview-email__send-to-field",value:r,type:"email",ref:e,required:!0,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0}),i===Ht.SUCCESS?(0,l.jsxs)("p",{className:"woocommerce-send-preview-modal-notice-success",children:[(0,l.jsx)(Re,{icon:Bt,style:{fill:"#4AB866"}}),(0,x.__)("Test email sent successfully!","woocommerce")]}):null,(0,l.jsxs)("div",{className:"woocommerce-send-preview-modal-footer",children:[(0,l.jsx)(v.Button,{variant:"tertiary",onClick:()=>{I("send_preview_email_modal_close_button_clicked"),h()},children:(0,x.__)("Cancel","woocommerce")}),(0,l.jsx)(v.Button,{variant:"primary",onClick:()=>{_(),I("send_preview_email_modal_send_test_email_button_clicked")},disabled:s||!(0,Mt.isEmail)(r),children:s?(0,x.__)("Sending…","woocommerce"):(0,x.__)("Send test email","woocommerce")})]})]}):null}));function Ft(){const{togglePreviewModal:e}=(0,a.useDispatch)(z.H);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(R.PluginPreviewMenuItem,{icon:Nt,onClick:()=>{I("header_preview_dropdown_send_test_email_selected"),e(!0)},children:(0,x.__)("Send a test email","woocommerce")}),(0,l.jsx)(zt,{})]})}const At=window.wp.preferences,Lt=()=>{const e=(0,g.useViewportMatch)("large");return(0,l.jsx)(l.Fragment,{children:e&&(0,l.jsx)(te,{children:(0,l.jsx)(At.PreferenceToggleMenuItem,{scope:z.H,name:"fullscreenMode",label:(0,x.__)("Fullscreen mode","woocommerce"),info:(0,x.__)("Show and hide the admin user interface","woocommerce"),messageActivated:(0,x.__)("Fullscreen mode activated.","woocommerce"),messageDeactivated:(0,x.__)("Fullscreen mode deactivated.","woocommerce"),shortcut:It.displayShortcut.secondary("f")})})})};function Rt({label:e,labelSuffix:t,help:o,placeholder:n,attributeName:r,attributeValue:s,updateProperty:i=()=>{}}){const[d,m]=(0,c.useState)(null),[p,u]=(0,c.useState)(!1),g=(0,a.useSelect)((e=>e(z.H).getPersonalizationTagsList()),[]),h=(0,c.useRef)(null),y=(0,c.useCallback)(((e,t,o)=>{var n,s;const l=null!==(n=o?.start)&&void 0!==n?n:t.length,a=null!==(s=o?.end)&&void 0!==s?s:t.length;let c=(0,b.create)({html:t});c=(0,b.insert)(c,(0,b.create)({html:`\x3c!--${e}--\x3e`}),l,a);const d=(0,b.toHTMLString)({value:c});i(r,d),m(null)}),[r,i]),w=(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{children:e}),(0,l.jsx)(v.Button,{className:"woocommerce-settings-panel-personalization-tags-button",icon:"shortcode",title:(0,x.__)("Personalization Tags","woocommerce"),onClick:()=>{u(!0),I("rich_text_with_button_personalization_tags_shortcode_icon_clicked",{attributeName:r,label:e})}}),t]});return r?(0,l.jsxs)(v.BaseControl,{id:"",label:w,className:`woocommerce-settings-panel-${r}-text`,help:o,__nextHasNoMarginBottom:!0,children:[(0,l.jsx)(F,{isOpened:p,onInsert:e=>{y(e,null!=s?s:"",d),u(!1),I("rich_text_with_button_personalization_tags_inserted",{attributeName:r,value:e})},closeCallback:()=>u(!1),openedBy:"RichTextWithButton-BaseControl"}),(0,l.jsx)(A,{contentRef:h,onUpdate:(e,t)=>{const o=(null!=s?s:"").replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);i(r,o)}}),(0,l.jsx)(_.RichText,{ref:h,className:"woocommerce-settings-panel-richtext",placeholder:n,onFocus:()=>{m(j(h,null!=s?s:""))},onKeyUp:()=>{m(j(h,null!=s?s:""))},onClick:()=>{m(j(h,null!=s?s:""))},onChange:e=>{var t;e=k(null!==(t=e)&&void 0!==t?t:"",g),i(r,e),M("rich_text_with_button_input_field_updated",{attributeName:r})},value:null!=s?s:"","data-automation-id":`email_${r}`})]}):null}function Ot({close:e}){M("edit_template_modal_opened");const{onNavigateToEntityRecord:t,template:o}=(0,a.useSelect)((e=>{const{getEditorSettings:t}=e(R.store);return{onNavigateToEntityRecord:t().onNavigateToEntityRecord,template:e(z.H).getCurrentTemplate()}}),[]);return(0,l.jsxs)(v.Modal,{size:"medium",onRequestClose:e,__experimentalHideHeader:!0,children:[(0,l.jsx)("p",{children:(0,x.__)("This template is used by multiple emails. Any changes made would affect other emails on the site. Are you sure you want to edit the template?","woocommerce")}),(0,l.jsxs)(v.Flex,{justify:"end",children:[(0,l.jsx)(v.FlexItem,{children:(0,l.jsx)(v.Button,{variant:"tertiary",onClick:()=>{I("edit_template_modal_cancel_button_clicked"),e()},children:(0,x.__)("Cancel","woocommerce")})}),(0,l.jsx)(v.FlexItem,{children:(0,l.jsx)(v.Button,{variant:"primary",onClick:()=>{I("edit_template_modal_continue_button_clicked",{templateId:o.id}),t({postId:o.id,postType:"wp_template"})},disabled:!o.id,children:(0,x.__)("Edit template","woocommerce")})})]})]})}function Vt(){const{template:e,currentEmailContent:t,canUpdateTemplates:o,postType:n}=(0,a.useSelect)((e=>({template:e(z.H).getCurrentTemplate(),currentEmailContent:e(z.H).getEditedEmailContent(),canUpdateTemplates:e(z.H).canUserEditTemplates(),postType:e(z.H).getEmailPostType()})),[]),[r]=Le("swap"),[s,i]=(0,c.useState)(!1),[d,m]=(0,c.useState)(!1);return(0,l.jsxs)(l.Fragment,{children:[e&&(0,l.jsx)(v.PanelRow,{children:(0,l.jsxs)(v.Flex,{justify:"start",children:[(0,l.jsx)(v.FlexItem,{className:"editor-post-panel__row-label",children:(0,x.__)("Template","woocommerce")}),(0,l.jsxs)(v.FlexItem,{children:[!(r?.length>1||o)&&(0,l.jsx)("b",{children:e?.title}),(r?.length>1||o)&&(0,l.jsx)(v.DropdownMenu,{icon:null,text:e?.title,toggleProps:{variant:"tertiary"},label:(0,x.__)("Template actions","woocommerce"),onToggle:t=>I("sidebar_template_actions_clicked",{currentTemplate:e?.title,isOpen:t}),children:({onClose:e})=>(0,l.jsxs)(l.Fragment,{children:[o&&(0,l.jsx)(v.MenuItem,{onClick:()=>{I("sidebar_template_actions_edit_template_clicked"),i(!0),e()},children:(0,x.__)("Edit template","woocommerce")}),r?.length>1&&(0,l.jsx)(v.MenuItem,{onClick:()=>{I("sidebar_template_actions_swap_template_clicked"),m(!0),e()},children:(0,x.__)("Swap template","woocommerce")})]})})]})]})}),s&&(0,l.jsx)(Ot,{close:()=>(I("edit_template_modal_closed"),i(!1))}),d&&(0,l.jsx)(Ye,{onSelectCallback:()=>m(!1),closeCallback:()=>m(!1),previewContent:t,postType:n})]})}const Dt={recordEvent:I,recordEventOnce:M,debouncedRecordEvent:H};function Gt(){const e=(0,c.useMemo)((()=>(0,d.applyFilters)("woocommerce_email_editor_setting_sidebar_extension_component",Rt,Dt)),[]),t=(0,c.useMemo)((()=>(0,d.applyFilters)("woocommerce_email_editor_setting_sidebar_email_status_component",(()=>null),Dt)),[]);return(0,l.jsxs)(R.PluginDocumentSettingPanel,{name:"email-settings-panel",title:(0,x.__)("Settings","woocommerce"),className:"woocommerce-email-editor__settings-panel",children:[(0,l.jsx)(t,{}),(0,l.jsx)(Vt,{}),(0,l.jsx)(R.ErrorBoundary,{canCopyContent:!0,children:(0,l.jsx)(e,{})})]})}const $t={recordEvent:I,recordEventOnce:M,debouncedRecordEvent:H};function Wt(){const e=(0,d.applyFilters)("woocommerce_email_editor_template_sections",[],$t);return 0===e.length?null:(0,l.jsx)(R.PluginDocumentSettingPanel,{name:"template-settings-panel",title:(0,x.__)("Settings","woocommerce"),className:"woocommerce-email-editor__settings-panel",children:e.map((e=>(0,l.jsx)(R.ErrorBoundary,{children:(0,l.jsx)("div",{children:e.render()},e.id)},`error-boundary-${e.id}`)))})}function Ut(){const{isDirty:e}=(0,R.useEntitiesSavedStatesIsDirty)(),{hasEmptyContent:t,isEmailSent:o,urls:n}=(0,a.useSelect)((e=>({hasEmptyContent:e(z.H).hasEmptyContent(),isEmailSent:e(z.H).isEmailSent(),urls:e(z.H).getUrls()})),[]);function r(){n.send&&(window.location.href=n.send)}const s=t||o||e,i=(0,d.applyFilters)("woocommerce_email_editor_send_button_label",(0,x.__)("Send","woocommerce"));return(0,l.jsx)(v.Button,{variant:"primary",size:"compact",onClick:()=>{I("header_send_button_clicked"),(0,d.applyFilters)("woocommerce_email_editor_send_action_callback",r)()},disabled:s,"data-automation-id":"email_editor_send_button",children:i})}function qt({children:e}){const t=(0,c.useRef)(document.createElement("div"));return(0,c.useEffect)((()=>{const e=document.getElementsByClassName("editor-post-publish-button__button")[0];e&&e.parentNode?.insertBefore(t.current,e.nextSibling)}),[t]),(0,c.createPortal)((0,l.jsx)(l.Fragment,{children:e}),t.current)}function Zt(){const e=(0,c.useRef)(null),{hasNonPostEntityChanges:t,isEditedPostDirty:o,isEditingTemplate:n}=(0,a.useSelect)((e=>({hasNonPostEntityChanges:e(R.store).hasNonPostEntityChanges(),isEditedPostDirty:e(R.store).isEditedPostDirty(),isEditingTemplate:"wp_template"===e(R.store).getCurrentPostType()})),[]),r=n||t||o&&t,s=(0,c.useCallback)(((e,t)=>{t&&e.classList.contains("force-hidden")&&e.classList.remove("force-hidden"),t||e.classList.contains("force-hidden")||e.classList.add("force-hidden")}),[]);return(0,c.useEffect)((()=>{const t=document.getElementsByClassName("editor-post-publish-button__button")[0];return s(t,r),t?(e.current&&e.current.disconnect(),e.current=new MutationObserver((()=>{s(t,r)})),e.current.observe(t,{attributes:!0,childList:!0,subtree:!1}),()=>e.current?.disconnect()):()=>e.current?.disconnect()}),[r,s]),(0,l.jsx)(qt,{children:!r&&(0,l.jsx)(Ut,{})})}const Jt=()=>{const e="email-validation",t=(0,a.useSelect)((t=>t(ie.store).getNotices(e)));return{notices:t,hasValidationNotice:(0,c.useCallback)((e=>e?void 0!==t.find((t=>t.id===e)):t?.length>0),[t]),addValidationNotice:(0,c.useCallback)(((t,o,n=[])=>{(0,a.dispatch)(ie.store).createNotice("error",o,{id:t,isDismissible:!1,actions:n,context:e})}),[e]),removeValidationNotice:(0,c.useCallback)((t=>{(0,a.dispatch)(ie.store).removeNotice(t,e)}),[e])}};function Yt(){const{notices:e}=Jt();return 0===e.length?null:(0,l.jsx)(v.Notice,{status:"error",className:"woocommerce-email-editor-validation-errors components-editor-notices__pinned",isDismissible:!1,children:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("strong",{children:(0,x.__)("Fix errors to continue:","woocommerce")}),(0,l.jsx)("ul",{children:e.map((({id:e,content:t,actions:o})=>(0,l.jsxs)("li",{children:[t,o.length>0?o.map((({label:e,onClick:t})=>(0,l.jsx)(v.Button,{onClick:t,variant:"link",children:e},e))):null]},e)))})]})})}function Kt({context:e="email-editor"}){const{notices:t}=(0,a.useSelect)((t=>({notices:t(ie.store).getNotices(e)})),[e]),o=(0,c.useMemo)((()=>({"site-editor-save-success":{content:(0,x.__)("Email design updated.","woocommerce"),removeActions:!0},"editor-save":{content:(0,x.__)("Email saved.","woocommerce"),removeActions:!1,contentCheck:e=>e.content.includes((0,x.__)("Post updated."))}})),[]),{removeNotice:n}=(0,a.useDispatch)(ie.store),r=t.filter((({type:e})=>"snackbar"===e)).map((e=>o[e.id]?o[e.id].contentCheck&&!o[e.id].contentCheck(e)?e:{...e,content:o[e.id].content,spokenMessage:o[e.id].content,actions:o[e.id].removeActions?[]:e.actions}:e));return(0,l.jsx)(v.SnackbarList,{notices:r,className:"components-editor-notices__snackbar",onRemove:t=>n(t,e)})}function Xt({children:e}){const[t]=(0,c.useState)(document.createElement("div"));return(0,c.useEffect)((()=>{const e=document.getElementsByClassName("editor-visual-editor ")[0];e&&e.parentNode?.insertBefore(t,e)}),[t]),(0,c.createPortal)((0,l.jsx)(l.Fragment,{children:e}),t)}function Qt(){const{notices:e}=(0,a.useSelect)((e=>({notices:e(ie.store).getNotices("email-editor")})),[]),{removeNotice:t}=(0,a.useDispatch)(ie.store),o=e.filter((({isDismissible:e,type:t})=>e&&"default"===t)),n=e.filter((({isDismissible:e,type:t})=>!e&&"default"===t));return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(Xt,{children:[(0,l.jsx)(v.NoticeList,{notices:n,className:"components-editor-notices__pinned"}),(0,l.jsx)(v.NoticeList,{notices:o,className:"components-editor-notices__dismissible",onRemove:e=>t(e,"email-editor")}),(0,l.jsx)(Yt,{})]}),(0,l.jsx)(Kt,{context:"global"}),(0,l.jsx)(Kt,{context:"email-editor"})]})}const eo=e=>{const t=(0,m.getBlockSupport)(e,"background");return t&&!1!==t?.backgroundImage};function to(){const e=(0,a.useSelect)((e=>e("core/block-editor").getSelectedBlock()),[]),t=(0,m.hasBlockSupport)(e?.name,"border",!1)||(0,m.hasBlockSupport)(e?.name,"__experimentalBorder",!1);return(0,l.jsxs)(l.Fragment,{children:[t&&(0,l.jsx)(v.Fill,{name:"InspectorControlsBorder",children:(0,l.jsxs)(v.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:[(0,x.__)("Border display may vary or be unsupported in some email clients.","woocommerce"),(0,l.jsx)("br",{}),(0,x.__)("Units other than pixels (px) lack support in old email clients.","woocommerce")]})}),eo(e?.name)&&(0,l.jsx)(v.Fill,{name:"InspectorControlsBackground",children:(0,l.jsx)(v.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:(0,x.__)("Select a background color for email clients that do not support background images.","woocommerce")})})]})}const oo=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",children:(0,l.jsx)(le.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"})}),no=(0,l.jsx)(le.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(le.Path,{d:"M20 11.2H6.8l3.7-3.7-1-1L3.9 12l5.6 5.5 1-1-3.7-3.7H20z"})}),ro={edit:{opacity:0,scale:.2},hover:{opacity:1,scale:1,clipPath:"inset( 22% round 2px )"}},so={edit:{clipPath:"inset(0% round 0px)"},hover:{clipPath:"inset( 22% round 2px )"},tap:{clipPath:"inset(0% round 0px)"}},io=()=>{const{urls:e}=(0,a.useSelect)((e=>({urls:e(z.H).getUrls()})),[]);function t(){e.listings&&(window.location.href=e.back)}return(0,l.jsx)(oe,{children:({length:e})=>e<=1&&(0,l.jsxs)(v.__unstableMotion.div,{className:"woocommerce-email-editor__view-mode-toggle",transition:{duration:.2},animate:"edit",initial:"edit",whileHover:"hover",whileTap:"tap",children:[(0,l.jsx)(v.Button,{label:(0,x.__)("Close editor","woocommerce"),showTooltip:!0,tooltipPosition:"middle right",onClick:()=>{I("header_close_button_clicked"),(0,d.applyFilters)("woocommerce_email_editor_close_action_callback",t)()},children:(0,l.jsx)(v.__unstableMotion.div,{variants:so,children:(0,l.jsx)("div",{className:"woocommerce-email-editor__view-mode-toggle-icon",children:(0,l.jsx)(Re,{className:"woocommerce-email-editor-icon__icon",icon:oo,size:48})})})}),(0,l.jsx)(v.__unstableMotion.div,{className:"woocommerce-email-editor-icon",variants:ro,children:(0,l.jsx)(Re,{icon:no})})]})})};function lo({postId:e,postType:t,settings:o,contentRef:n}){const{currentPost:r,onNavigateToEntityRecord:s,onNavigateToPreviousEntityRecord:i}=function(e,t,o){const[n,r]=(0,c.useReducer)(((e,{type:t,post:o,previousRenderingMode:n})=>"push"===t?[...e,{post:o,previousRenderingMode:n}]:"pop"===t&&e.length>1?e.slice(0,-1):e),[{post:{postId:e,postType:t}}]),{post:s,previousRenderingMode:i}=n[n.length-1],{getRenderingMode:l}=(0,a.useSelect)(R.store),{setRenderingMode:d}=(0,a.useDispatch)(R.store),m=(0,c.useCallback)((e=>{r({type:"push",post:{postId:e.postId,postType:e.postType},previousRenderingMode:l()}),d(o)}),[l,d,o]),p=(0,c.useCallback)((()=>{r({type:"pop"}),i&&d(i)}),[d,i]);return{currentPost:s,onNavigateToEntityRecord:m,onNavigateToPreviousEntityRecord:n.length>1?p:void 0}}(e,t,"post-only"),{post:d,template:m,isFullscreenEnabled:p}=(0,a.useSelect)((e=>{const{getEntityRecord:t}=e(se.store),{getEditedPostTemplate:o}=e(z.H),n=t("postType",r.postType,r.postId);return{template:n&&"wp_template"!==r.postType?o(n.template):null,post:n,isFullscreenEnabled:e(z.H).isFeatureActive("fullscreenMode")}}),[r.postType,r.postId]),{isFullScreenForced:u,displaySendEmailButton:_}=o,{removeEditorPanel:g}=(0,a.useDispatch)(R.store);(0,c.useEffect)((()=>{g("post-status")}),[g]);const[h]=He(),y=(0,c.useMemo)((()=>({...o,onNavigateToEntityRecord:s,onNavigateToPreviousEntityRecord:i,defaultRenderingMode:"wp_template"===r.postType?"post-only":"template-locked",supportsTemplateMode:!0})),[o,s,i,r.postType]);return d&&("wp_template"===r.postType||d.template===m?.slug||!d.template&&m)?(M("editor_layout_loaded"),(0,l.jsx)(v.SlotFillProvider,{children:(0,l.jsxs)(R.ErrorBoundary,{canCopyContent:!0,children:[(0,l.jsx)(Ce.CommandMenu,{}),(0,l.jsxs)(Q,{postId:r.postId,postType:r.postType,settings:y,templateId:m&&m.id,styles:h,contentRef:n,children:[(0,l.jsx)(R.AutosaveMonitor,{}),(0,l.jsx)(R.LocalAutosaveMonitor,{}),(0,l.jsx)(R.UnsavedChangesWarning,{}),(0,l.jsx)(R.EditorKeyboardShortcutsRegister,{}),(0,l.jsx)(R.PostLockedModal,{}),(0,l.jsx)(Ke,{}),(0,l.jsx)(Pt,{}),(0,l.jsx)(Ft,{}),(0,l.jsx)(ee,{isActive:u||p}),(u||p)&&(0,l.jsx)(io,{}),!u&&(0,l.jsx)(Lt,{}),"wp_template"===r.postType?(0,l.jsx)(Wt,{}):(0,l.jsx)(Gt,{}),_&&(0,l.jsx)(Zt,{}),(0,l.jsx)(Qt,{}),(0,l.jsx)(to,{})]})]})})):(0,l.jsx)("div",{className:"spinner-container",children:(0,l.jsx)(v.Spinner,{style:{width:"80px",height:"80px"}})})}const ao=window.wp.dataControls;function co(e){return{type:"CHANGE_PREVIEW_STATE",state:{isModalOpened:e}}}function mo(e){return{type:"CHANGE_PREVIEW_STATE",state:{toEmail:e}}}function po(e,t){if(!e||!t)throw new Error("setEmailPost requires valid postId and postType parameters");return{type:"SET_EMAIL_POST",state:{postId:e,postType:t}}}const uo=e=>async({registry:t})=>{const o=t.select(z.H).getEmailPostId(),n=t.select(z.H).getEmailPostType();t.dispatch(se.store).editEntityRecord("postType",n,o,{template:e})};function*_o(e){if(!(0,a.select)(z.H).getPreviewState().isSendingPreviewEmail){yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:null,isSendingPreviewEmail:!0}};try{const t=(0,a.select)(z.H).getEmailPostId();yield(0,ao.apiFetch)({path:"/woocommerce-email-editor/v1/send_preview_email",method:"POST",data:{email:e,postId:t}}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:Ht.SUCCESS,isSendingPreviewEmail:!1}},I("sent_preview_email",{postId:t,email:e})}catch(t){I("sent_preview_email_error",{email:e}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:Ht.ERROR,isSendingPreviewEmail:!1,errorMessage:JSON.stringify(t?.error)}}}}}function go(e){return{type:"SET_IS_FETCHING_PERSONALIZATION_TAGS",state:{isFetching:e}}}function ho(e){return{type:"SET_PERSONALIZATION_TAGS_LIST",state:{list:e}}}function yo(e){return{type:"SET_CONTENT_VALIDATION",validation:e}}function xo(){if(!window.WooCommerceEmailEditor)throw new Error("WooCommerceEmailEditor global object is not available. This is required for the email editor to work.");return{editorSettings:window.WooCommerceEmailEditor.editor_settings,theme:window.WooCommerceEmailEditor.editor_theme,styles:{globalStylesPostId:window.WooCommerceEmailEditor.user_theme_post_id},urls:window.WooCommerceEmailEditor.urls,preview:{toEmail:window.WooCommerceEmailEditor.current_wp_user_email,isModalOpened:!1,isSendingPreviewEmail:!1,sendingPreviewStatus:null},personalizationTags:{list:[],isFetching:!1},contentValidation:void 0}}function wo(e,t){switch(t.type){case"CHANGE_PREVIEW_STATE":return{...e,preview:{...e.preview,...t.state}};case"SET_EMAIL_POST":return{...e,...t.state};case"CHANGE_PERSONALIZATION_TAGS_STATE":case"SET_IS_FETCHING_PERSONALIZATION_TAGS":case"SET_PERSONALIZATION_TAGS_LIST":return{...e,personalizationTags:{...e.personalizationTags,...t.state}};case"SET_PERSONALIZATION_TAGS":return{...e,personalizationTags:{...e.personalizationTags,list:t.personalizationTags}};case"SET_CONTENT_VALIDATION":return{...e,contentValidation:t.validation};default:return e}}function fo(e){return e?.content&&"function"==typeof e.content?e.content(e):e?.blocks?(0,m.serialize)(e.blocks):e?.content?e.content:""}const bo=new WeakMap;function vo(e){let t=bo.get(e);return t||(t={...e,get blocks(){return(0,m.parse)(e.content)}},bo.set(e,t)),t}function jo(e){return e?{...e,title:e?.title?.raw||e?.title||"",content:e?.content?.raw||e?.content||""}:null}const ko=(0,a.createRegistrySelector)((e=>(t,o)=>!!e(At.store).get(z.H,o))),So=(0,a.createRegistrySelector)((e=>()=>{const t=e(z.H).getEmailPostId(),o=e(z.H).getEmailPostType();return!!e(se.store).hasEditsForEntityRecord("postType",o,t)})),Co=(0,a.createRegistrySelector)((e=>()=>{const t=e(z.H).getEmailPostId(),o=e(z.H).getEmailPostType(),n=e(se.store).getEntityRecord("postType",o,t);if(!n)return!0;const{content:r}=n;return!r.raw})),Eo=(0,a.createRegistrySelector)((e=>()=>{const t=e(z.H).getEmailPostId(),o=e(z.H).getEmailPostType(),n=e(se.store).getEntityRecord("postType",o,t);return!!n&&"sent"===n.status})),To=(0,a.createRegistrySelector)((e=>()=>{const t=e(z.H).getEmailPostId(),o=e(z.H).getEmailPostType(),n=e(se.store).getEditedEntityRecord("postType",o,t);return n?fo(n):""})),Po=(0,a.createRegistrySelector)((e=>()=>{const t=e(z.H).getEmailPostType();return e(se.store).getEntityRecords("postType",t,{per_page:30,status:"publish,sent"})?.filter((e=>""!==e?.content?.raw))||[]})),No=(0,a.createRegistrySelector)((e=>{const t=e(z.H).getEmailPostType();return(0,a.createSelector)((()=>t?e(se.store).getBlockPatterns().filter((({templateTypes:e,postTypes:o})=>Array.isArray(e)&&e.includes("email-template")&&(void 0===o||0===o.length||o.includes(t)))).map(vo):[]),(()=>[e(se.store).getBlockPatterns(),t]))})),Bo=(0,a.createRegistrySelector)((e=>()=>e(se.store).canUser("create",{kind:"postType",name:"wp_template"})));function Io(e,t){return Bo()?e(se.store).getEditedEntityRecord("postType","wp_template",t):jo(e(se.store).getEntityRecord("postType","wp_template",t,{context:"view"}))}const Mo=(0,a.createRegistrySelector)((e=>(t,o)=>{const n=o||e(R.store).getEditedPostAttribute("template");if(n){const t={context:"view",per_page:-1,_woocommerce_email_editor:"fetch-all-templates"},o=e(se.store).getEntityRecords("postType","wp_template",t)?.find((e=>e.slug===n));return o?Io(e,o.id):null}const r=e(se.store).getDefaultTemplateId({slug:"email-general"});return Io(e,r)})),Ho=(0,a.createRegistrySelector)((e=>()=>{if("wp_template"===e(R.store).getCurrentPostType()){const t=e(R.store).getCurrentPostId();return e(se.store).getEditedEntityRecord("postType","wp_template",t)}return e(z.H).getEditedPostTemplate()})),zo=()=>{const e=Ho();return e?fo(e):""},Fo=(0,a.createRegistrySelector)((e=>()=>{const t=e(z.H).getGlobalStylesPostId();return{postId:t,canEdit:e(se.store).canUser("update",{kind:"root",name:"globalStyles",id:t})}})),Ao=(0,a.createRegistrySelector)((e=>()=>{const{postId:t,canEdit:o}=Fo();return t&&void 0!==o&&t?o?e(se.store).getEditedEntityRecord("postType","wp_global_styles",t):jo(e(se.store).getEntityRecord("postType","wp_global_styles",t,{context:"view"})):null})),Lo=(0,a.createRegistrySelector)((e=>()=>{const t=e(z.H).getEmailPostType();return e(se.store).getEntityRecords("postType","wp_template",{per_page:-1,post_type:t,context:"view"})?.filter((e=>e.post_types.includes(t)))}));function Ro(e){return e.postId}function Oo(e){return e.postType}function Vo(e){return e.editorSettings}function Do(e){return e.editorSettings.__experimentalFeatures.color.palette}function Go(e){return e.preview}function $o(e){return e.personalizationTags}const Wo=(0,a.createRegistrySelector)((e=>t=>{const o=t.personalizationTags.list,n=e(z.H).getEmailPostType();if(!n)return o;if("wp_template"===n){const t=e(z.H).getCurrentTemplate();return o.filter((e=>void 0===e.postTypes||0===e.postTypes.length||Array.isArray(t.post_types)&&t.post_types.some((t=>e.postTypes.includes(t)))))}return o.filter((e=>void 0===e.postTypes||0===e.postTypes.length||e.postTypes.includes(n)))}));function Uo(e){return e.theme.styles}function qo(e){return e.theme}function Zo(e){return e.styles.globalStylesPostId}function Jo(e){return e.urls}function Yo(e){return e.contentValidation}function*Ko(){const e=yield(0,a.select)(z.H),t=e.personalizationTags?.isFetching;if(!t){yield go(!0);try{const e=yield(0,ao.apiFetch)({path:"/woocommerce-email-editor/v1/get_personalization_tags",method:"GET"});yield ho(e.result)}finally{yield go(!1)}}}const Xo=()=>{const e=(0,a.createReduxStore)(z.H,{actions:r,controls:ao.controls,selectors:s,resolvers:i,reducer:wo,initialState:xo()});return(0,a.register)(e),e},Qo=window.wp.mediaUtils,en=()=>{(0,d.addFilter)("editor.MediaUpload","woocommerce/email-editor/replace-media-upload",(()=>Qo.MediaUpload))},tn=()=>{const e={"You’ve tried to select a block that is part of a template that may be used elsewhere on your site. Would you like to edit the template?":{domain:"default",replacementText:(0,x.__)("You’ve tried to select a block that is part of a template that may be used in other emails. Would you like to edit the template?","woocommerce")}};(0,d.addFilter)("i18n.gettext","woocommerce/email-editor/override-text",((t,o,n)=>e[o]&&e[o].domain===(n||"default")?e[o].replacementText:t))},on=e=>{(0,d.doAction)("woocommerce_email_editor_events",e.detail)},nn=()=>{P()&&B.addEventListener(N,on)};window.addEventListener("unload",(function(){P()&&B.removeEventListener(N,on)}));const rn=(...e)=>{const t=(0,a.select)(R.store).isInserterOpened(),o=!!document.getElementsByClassName("block-editor-inserter__quick-inserter").length;let n="other_inserter";t?n="inserter_sidebar":o&&(n="quick_inserter");const r=e[0],s=e[5];!1===Array.isArray(r)&&"object"==typeof r&&I(`${n}_library_block_selected`,{blockName:r.name}),Array.isArray(r)&&s&&s.patternName&&I(`${n}_library_pattern_selected`,{patternName:s.patternName})},sn={"core/editor":{autosave:"editor_content_auto_saved",setDeviceType:e=>{I(`header_preview_dropdown_${e.toLowerCase()}_selected`)},setRenderingMode:e=>{(0,a.select)(R.store).getRenderingMode()!==e&&document.querySelector(`[aria-label="${(0,x.__)("View options")}"]`)&&I("preview_dropdown_rendering_mode_changed",{renderingMode:e})}},"core/block-editor":{insertBlock:rn,insertBlocks:rn},"core/preferences":{set:(e,t,o)=>{if((0,a.select)(At.store).get(e,t)===o)return;const n={focusMode:"focus_mode_toggle",fullscreenMode:"full_screen_mode_toggle",distractionFree:"distraction_free_toggle",fixedToolbar:"fixed_toolbar_toggle"};n[t]&&I(n[t],{isEnabled:o})}},"core/commands":{open:"command_menu_opened",close:"command_menu_closed"}},ln={},an={},cn=()=>{P()&&(0,a.use)((e=>({dispatch:t=>{const o="object"==typeof t?t.name:t,n=e.dispatch(o),r=sn[o];if(!r)return n;ln[o]||(ln[o]={}),an[o]||(an[o]={});for(const[e,t]of Object.entries(r))an[o][e]||(an[o][e]=n[e],ln[o][e]=(...n)=>{try{"function"==typeof t?t(...n):"string"==typeof t&&I(t)}catch(e){console.error("Error tracking event",e)}return an[o][e](...n)}),n[e]=ln[o][e];return n}})))};let dn=[];function mn(e){dn.forEach((t=>{const o=e.target?.matches?.(t.selector)?e.target:e.target?.closest?.(t.selector);o&&("function"==typeof t.track?t.track(o,e):I(t.track))}))}const pn=new WeakMap,un={core:["saveEditedEntityRecord","saveEntityRecord"]},_n=()=>{Object.keys(pn).length>0||(0,a.use)((e=>({dispatch:t=>{const o="object"==typeof t?t.name:t;if(!un[o])return e.dispatch(o);const n=e.dispatch(o);pn[o]||(pn[o]={});const r=un[o].filter((e=>!pn[o][e]));if(r.length>0)for(const t of r)pn[o][t]=n[t],n[t]=async(...n)=>{const r=e.select(z.H).getContentValidation(),s=r?.validateContent;if(s){let e;try{e=s()}catch(t){e=!1}if(!e)return Promise.reject(new Error((0,x.__)("Content validation failed.","woocommerce")))}return await pn[o][t](...n)};return n}})))},gn=window.wp.isShallowEqual;var hn=o.n(gn);function yn(e){const t=(0,c.useRef)(e);return hn()(e,t.current)||(t.current=e),t.current}const xn=[],wn=()=>{const{addValidationNotice:e,hasValidationNotice:t,removeValidationNotice:o}=Jt(),{editedContent:n,editedTemplateContent:r}=(0,a.useSelect)((e=>({editedContent:e(z.H).getEditedEmailContent(),editedTemplateContent:e(z.H).getCurrentTemplateContent()}))),s=yn(n),i=yn(r),l=(0,c.useCallback)((()=>((e,t,{addValidationNotice:o,hasValidationNotice:n,removeValidationNotice:r})=>{const s=(0,d.applyFilters)("woocommerce_email_editor_content_validation_rules",xn);let i=!0;return s.forEach((({id:s,testContent:l,message:a,actions:c})=>{l(e+t)?(o(s,a,c),i=!1):n(s)&&r(s)})),i})(s,i,{addValidationNotice:e,hasValidationNotice:t,removeValidationNotice:o})),[s,i,e,o,t]);return(0,c.useEffect)((()=>((0,a.dispatch)(z.H).setContentValidation({validateContent:l}),()=>{(0,a.dispatch)(z.H).setContentValidation(void 0)})),[l]),(0,c.useEffect)((()=>{const e=(0,a.subscribe)((()=>{t()&&l()}),se.store);return()=>e()}),[t,l]),{validateContent:l}},fn=()=>{const e=(0,c.useMemo)((()=>{const e=(0,x.__)("Saving failed.");return new RegExp("^"+e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))}),[]);(0,c.useEffect)((()=>{const t=(0,a.subscribe)((()=>{(0,a.select)(ie.store).getNotices().forEach((t=>{"string"==typeof t.content&&e.test(t.content)&&(0,a.dispatch)(ie.store).removeNotice(t.id)}))}));return()=>{t()}}),[e])},bn=()=>{const e=(0,c.useRef)(null),[t,o]=(0,c.useState)(0),n=(0,c.useCallback)((t=>{e.current=t,o((e=>++e))}),[e,o]),r=(0,a.useSelect)((e=>{var t;const{getEditorSettings:o}=e(R.store);return null!==(t=o()?.allowedIframeStyleHandles)&&void 0!==t?t:[]}));return(0,c.useEffect)((()=>{if(!e.current)return;const{ownerDocument:t}=e.current;Array.from(document.styleSheets).filter((e=>{if(!(e?.ownerNode instanceof Element))return!1;const t=e.ownerNode.getAttribute("id"),o=t&&!r.includes(t);return(0,d.applyFilters)("woocommerce_email_editor_iframe_stylesheet_should_remove",o,e)})).map((e=>e.ownerNode.getAttribute("id"))).forEach((e=>{const o=t.getElementById(e);o&&o.remove();const n=t.createElement("style");n.id=e,t.head.appendChild(n)}))}),[r,t]),n};function vn({postId:e,postType:t,isPreview:o=!1}){const[n,r]=(0,c.useState)(!1),{settings:s}=(0,a.useSelect)((e=>({settings:e(z.H).getInitialEditorSettings()})),[]);wn(),fn();const{setEmailPost:i}=(0,a.useDispatch)(z.H);(0,c.useEffect)((()=>{i(e,t),r(!0)}),[e,t,i]);const d=bn();if(!n)return null;const m={...s,allowedBlockTypes:p(),isPreviewMode:o};return(0,l.jsx)(c.StrictMode,{children:(0,l.jsx)(lo,{postId:e,postType:t,settings:m,contentRef:d})})}function jn(){nn(),cn(),P()&&(dn=[{track:"header_preview_dropdown_preview_in_new_tab_selected",selector:".editor-preview-dropdown__button-external"},{track:()=>{const e=document.getElementsByClassName("is-collapsed editor-collapsible-block-toolbar").length;I("header_blocks_tool_button_clicked",{isBlockToolsCollapsed:e})},selector:".editor-collapsible-block-toolbar__toggle"},{track:e=>{const t=e.classList.contains("is-opened");I("header_more_menu_dropdown_toggle",{isOpened:t})},selector:`.components-dropdown-menu__toggle[aria-label="${(0,x.__)("Options")}"]`},{track:e=>{(e.textContent===(0,x.__)("Save")&&"false"===e.getAttribute("aria-disabled")||e.textContent===(0,x.__)("Saving…"))&&I("header_save_button_clicked")},selector:".editor-post-publish-button"},{track:"header_save_email_button_clicked",selector:".editor-post-saved-state.is-saving"},{track:"inserter_sidebar_library_close_icon_clicked",selector:".block-editor-inserter__menu .block-editor-tabbed-sidebar__close-button"},{track:e=>{const t=e.classList.contains("is-opened");I("header_preview_dropdown_clicked",{isOpened:t})},selector:".editor-preview-dropdown__toggle"},{track:()=>{I("sidebar_tab_selected",{tab:"document"})},selector:'[data-tab-id="edit-post/document"]'},{track:()=>{I("sidebar_tab_selected",{tab:"block"})},selector:'[data-tab-id="edit-post/block"]'},{track:e=>{const t=e.classList.contains("is-pressed");I("header_inserter_sidebar_clicked",{isOpened:t})},selector:".editor-document-tools__inserter-toggle"},{track:e=>{const t=e.classList.contains("is-pressed");I("header_listview_sidebar_clicked",{isOpened:t})},selector:".editor-document-tools__document-overview-toggle"},{track:e=>{I("command_bar_command_clicked",{command:e.dataset?.value})},selector:'.commands-command-menu__container [role="option"]'}],document.addEventListener("click",mn)),Xo(),_n(),(0,d.addFilter)("blocks.registerBlockType","woocommerce-email-editor/layout/addAttribute",ve),(0,d.addFilter)("editor.BlockListBlock","woocommerce-email-editor/with-layout-styles",Se),(0,d.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-inspector-controls",je),ue(),en(),tn()}function kn(e){const t=document.getElementById(e);if(!t)return;const{current_post_id:o,current_post_type:n}=window.WooCommerceEmailEditor;if(null==o)throw new Error("current_post_id is required but not provided.");if(!n)throw new Error("current_post_type is required but not provided.");const r=(0,d.applyFilters)("woocommerce_email_editor_wrap_editor_component",vn);jn(),(0,c.createRoot)(t).render((0,l.jsx)(r,{postId:o,postType:n}))}function Sn({postId:e,postType:t,isPreview:o=!1}){const[n,r]=(0,c.useState)(!1);(0,c.useLayoutEffect)((()=>{jn(),r(!0)}),[]);const s=(0,d.applyFilters)("woocommerce_email_editor_wrap_editor_component",vn);return n?(0,l.jsx)(s,{postId:e,postType:t,isPreview:o}):null}function Cn(e){"loading"===document.readyState?window.addEventListener("DOMContentLoaded",(()=>{kn(e)}),{once:!0}):kn(e)}(window.wc=window.wc||{}).emailEditor=n})();