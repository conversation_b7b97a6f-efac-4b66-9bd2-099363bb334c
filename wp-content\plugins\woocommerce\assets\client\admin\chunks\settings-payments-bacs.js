"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[9680],{59255:(e,t,c)=>{c.d(t,{W:()=>a});var n=c(56427),o=c(39793);const a=({size:e="medium"})=>(0,o.jsx)("div",{className:`woocommerce-field-placeholder woocommerce-field-placeholder--${e}`,children:(0,o.jsx)(n.Placeholder,{})})},32905:(e,t,c)=>{c.d(t,{w:()=>s});var n=c(56427),o=c(51609),a=c(39793);const s=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});s.Layout=({children:e})=>((0,o.useEffect)((()=>{const e=document.getElementById("wpbody");e&&e.querySelector(".settings-layout")&&e.classList.add("has-settings-layout")}),[]),(0,a.jsx)("div",{className:"settings-layout",children:e})),s.Section=({title:e,description:t,children:c,id:n})=>(0,a.jsxs)("div",{className:"settings-section",id:n,children:[(0,a.jsxs)("div",{className:"settings-section__details",children:[(0,a.jsx)("h2",{children:e}),(0,a.jsx)("p",{children:t})]}),(0,a.jsx)("div",{className:"settings-section__controls",children:c})]}),s.Actions=({children:e})=>(0,a.jsx)(n.Card,{className:"settings-card__wrapper ",children:(0,a.jsx)(n.CardBody,{className:"form__actions",children:e})}),s.Form=({children:e,onSubmit:t})=>(0,a.jsx)("form",{onSubmit:t,className:"settings-form",children:e})},53490:(e,t,c)=>{c.r(t),c.d(t,{SettingsPaymentsBacs:()=>k,default:()=>v});var n=c(27723),o=c(47143),a=c(56427),s=c(86087),i=c(40314),r=c(32905),l=c(59255),d=c(98846),m=c(51609),u=c(18537);const _=e=>{switch(e){case"AU":return(0,n.__)("BSB","woocommerce");case"CA":return(0,n.__)("Bank transit number","woocommerce");case"IN":return(0,n.__)("IFSC","woocommerce");case"IT":return(0,n.__)("Branch sort","woocommerce");case"NZ":case"SE":return(0,n.__)("Bank code","woocommerce");case"US":return(0,n.__)("Routing number","woocommerce");case"ZA":return(0,n.__)("Branch code","woocommerce");default:return(0,n.__)("Sort code","woocommerce")}},h=e=>{switch(e){case"AU":case"CA":case"IN":case"IT":case"NZ":case"SE":case"US":case"ZA":case"GB":case"IE":return!0;default:return!1}},b=()=>Math.random().toString(36).substring(2,10),x=e=>""===e.trim()?"This field is required.":void 0;var p=c(39793);const w=({account:e,onClose:t,onSave:c,defaultCountry:o})=>{const s=window.wcSettings.countries,[i,r]=(0,m.useState)(e||{account_name:"",account_number:"",bank_name:"",sort_code:"",iban:"",bic:"",country_code:o}),[l,d]=(0,m.useState)(e?.country_code||o),[b,w]=(0,m.useState)({}),j=(e,t)=>{r((c=>({...c,[e]:t})))};return(0,p.jsxs)(a.Modal,{className:"bank-account-modal",title:e?(0,n.__)("Edit bank account","woocommerce"):(0,n.__)("Add a bank account","woocommerce"),onRequestClose:t,shouldCloseOnClickOutside:!1,children:[(0,p.jsxs)("div",{className:"bank-account-modal__content",children:[(0,p.jsx)("p",{className:"bank-account-modal__description",children:e?(0,n.__)("Edit your bank account details.","woocommerce"):(0,n.__)("Add your bank account details.","woocommerce")}),(0,p.jsx)(a.SelectControl,{className:"bank-account-modal__field is-required",label:(0,n.__)("Country","woocommerce"),required:!0,value:l,options:Object.entries(s).map((([e,t])=>({label:(0,u.decodeEntities)(t),value:e}))),onChange:e=>{d(e),j("country_code",e),j("sort_code","")}}),(0,p.jsx)(a.TextControl,{className:"bank-account-modal__field is-required",label:(0,n.__)("Account Name","woocommerce"),required:!0,value:i.account_name,onChange:e=>j("account_name",e),help:b.account_name?(0,p.jsx)("span",{className:"bank-account-modal__error",children:b.account_name}):void 0}),(0,p.jsx)(a.TextControl,{className:"bank-account-modal__field",label:(0,n.__)("Bank Name","woocommerce"),value:i.bank_name,onChange:e=>j("bank_name",e)}),(0,p.jsx)(a.TextControl,{className:"bank-account-modal__field",label:(0,n.__)("Account Number","woocommerce"),value:i.account_number,onChange:e=>j("account_number",e),help:b.account_number?(0,p.jsx)("span",{className:"bank-account-modal__error",children:b.account_number}):void 0}),h(l)&&(0,p.jsx)(a.TextControl,{className:"bank-account-modal__field is-required",label:_(l),required:!0,value:(g=i.sort_code||"",k=l,"GB"!==k&&"IE"!==k?g:null!==(v=g.replace(/\D/g,"").substring(0,6).match(/.{1,2}/g)?.join("-"))&&void 0!==v?v:""),onChange:e=>{"GB"!==l&&"IE"!==l||(e=e.replace(/\D/g,"").substring(0,6)),j("sort_code",e)},help:b.sort_code?(0,p.jsx)("span",{className:"bank-account-modal__error",children:b.sort_code}):void 0}),(0,p.jsx)(a.TextControl,{className:"bank-account-modal__field",label:(0,n.__)("IBAN","woocommerce"),value:i.iban,onChange:e=>j("iban",e),help:b.iban?(0,p.jsx)("span",{className:"bank-account-modal__error",children:b.iban}):void 0}),(0,p.jsx)(a.TextControl,{className:"bank-account-modal__field",label:(0,n.__)("BIC / SWIFT","woocommerce"),value:i.bic,onChange:e=>j("bic",e),help:b.bic?(0,p.jsx)("span",{className:"bank-account-modal__error",children:b.bic}):void 0})]}),(0,p.jsxs)("div",{className:"bank-account-modal__actions",children:[(0,p.jsx)(a.Button,{variant:"tertiary",onClick:t,children:(0,n.__)("Cancel","woocommerce")}),(0,p.jsx)(a.Button,{className:"bank-account-modal__save",variant:"primary",onClick:()=>{(()=>{const e={};e.account_name=x(i.account_name),h(l)&&(e.sort_code=x(i.sort_code));const t=Object.fromEntries(Object.entries(e).filter((([,e])=>e)));return w(t),0===Object.keys(t).length})()&&c(i)},children:(0,n.__)("Save","woocommerce")})]})]});var g,k,v};var j=c(15698);const g=({accounts:e,onChange:t,defaultCountry:c})=>{const[o,s]=(0,m.useState)((()=>e.map((e=>({...e,id:b()})))));(0,m.useEffect)((()=>{e.length&&0===o.length&&s(e.map((e=>({...e,id:b()}))))}),[e,o.length]);const[i,r]=(0,m.useState)(null),[l,u]=(0,m.useState)(!1),[_,h]=(0,m.useState)(null),x=(e=null)=>{r(e),u(!0)};return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsxs)(j.q6,{items:o,className:"bank-accounts__list",setItems:e=>{s(e),t(e.map((({id:e,...t})=>t)))},children:[(0,p.jsx)("div",{className:"bank-accounts__list-header",children:(0,p.jsxs)("div",{className:"bank-accounts__list-item-inner",children:[(0,p.jsx)("div",{className:"bank-accounts__list-item-before"}),(0,p.jsxs)("div",{className:"bank-accounts__list-item-text",children:[(0,p.jsx)("div",{children:"Account Name"}),(0,p.jsx)("div",{children:"Account Details"}),(0,p.jsx)("div",{children:"Bank Name"})]}),(0,p.jsx)("div",{className:"bank-accounts__list-item-after"})]})}),o.map(((e,t)=>(0,p.jsx)(j.Uq,{id:e.id,className:"bank-accounts__list-item"+(0===t?" first-item":""),children:(0,p.jsxs)("div",{className:"bank-accounts__list-item-inner",children:[(0,p.jsx)("div",{className:"bank-accounts__list-item-before",children:(0,p.jsx)(j.Gh,{})}),(0,p.jsxs)("div",{className:"bank-accounts__list-item-text",children:[(0,p.jsx)("div",{children:e.account_name}),(0,p.jsx)("div",{children:e.account_number||e.iban}),(0,p.jsx)("div",{children:e.bank_name})]}),(0,p.jsx)("div",{className:"bank-accounts__list-item-after",children:(0,p.jsx)(d.EllipsisMenu,{label:(0,n.__)("Options","woocommerce"),placement:"bottom-right",renderContent:({onClose:t=()=>{}})=>(0,p.jsxs)(a.MenuGroup,{children:[(0,p.jsx)(a.MenuItem,{role:"menuitem",onClick:()=>{t(),x(e)},children:(0,n.__)("View / edit","woocommerce")}),(0,p.jsx)(a.MenuItem,{isDestructive:!0,onClick:()=>{t(),h(e)},children:(0,n.__)("Delete","woocommerce")})]})})})]})},e.id))),(0,p.jsx)("li",{className:"bank-accounts__list-item action"+(0===o.length?" first-item":""),children:(0,p.jsx)(a.Button,{variant:"secondary",onClick:()=>x(null),children:(0,n.__)("+ Add account","woocommerce")})})]}),l&&(0,p.jsx)(w,{account:i,onClose:()=>u(!1),onSave:e=>{const c=o.findIndex((e=>e.id===i?.id));let n;-1!==c?(n=[...o],n[c]={...e,id:i?.id||b()}):n=[...o,{...e,id:b()}],s(n),t(n.map((({id:e,...t})=>t))),u(!1)},defaultCountry:c}),_&&(0,p.jsxs)(a.Modal,{title:(0,n.__)("Delete account","woocommerce"),onRequestClose:()=>h(null),shouldCloseOnClickOutside:!1,children:[(0,p.jsx)("p",{children:(0,n.__)("Are you sure you want to delete this bank account?","woocommerce")}),(0,p.jsxs)("div",{style:{display:"flex",justifyContent:"flex-end",gap:"8px",marginTop:"16px"},children:[(0,p.jsx)(a.Button,{variant:"secondary",onClick:()=>h(null),children:(0,n.__)("Cancel","woocommerce")}),(0,p.jsx)(a.Button,{variant:"primary",isDestructive:!0,onClick:()=>{if(!_)return;const e=o.filter((e=>e.id!==_.id));s(e),t(e.map((({id:e,...t})=>t))),h(null)},children:(0,n.__)("Delete","woocommerce")})]})]})]})},k=()=>{const e=window.wcSettings?.admin?.preloadSettings?.general?.woocommerce_default_country||"US",{createSuccessNotice:t,createErrorNotice:c}=(0,o.useDispatch)("core/notices"),{bacsSettings:d,isLoading:m}=(0,o.useSelect)((e=>({bacsSettings:e(i.paymentGatewaysStore).getPaymentGateway("bacs"),isLoading:!e(i.paymentGatewaysStore).hasFinishedResolution("getPaymentGateway",["bacs"])})),[]),{invalidateResolution:u,invalidateResolutionForStoreSelector:_}=(0,o.useDispatch)(i.paymentSettingsStore),{accountsOption:h,isLoadingAccounts:b}=(0,o.useSelect)((e=>{const t=e(i.optionsStore);return{accountsOption:t.getOption("woocommerce_bacs_accounts"),isLoadingAccounts:!t.hasFinishedResolution("getOption",["woocommerce_bacs_accounts"])}}),[]),[x,w]=(0,s.useState)({}),[j,k]=(0,s.useState)(!1),[v,y]=(0,s.useState)(!1);(0,s.useEffect)((()=>{d&&(w({enabled:d.enabled,title:d.settings.title.value,description:d.description,instructions:d.settings.instructions.value}),y(!1))}),[d]);const[C,S]=(0,s.useState)([]);(0,s.useEffect)((()=>{h&&S(h)}),[h]);const{updateOptions:f}=(0,o.useDispatch)(i.optionsStore),{updatePaymentGateway:N}=(0,o.useDispatch)(i.paymentGatewaysStore);return(0,p.jsx)(r.w,{children:(0,p.jsx)(r.w.Layout,{children:(0,p.jsxs)(r.w.Form,{onSubmit:e=>{e.preventDefault(),(async()=>{if(!d)return;k(!0);const e={title:String(x.title),instructions:String(x.instructions)};try{await Promise.all([f({woocommerce_bacs_accounts:C.map((({account_name:e,account_number:t,bank_name:c,sort_code:n,iban:o,bic:a,country_code:s})=>({account_name:e,account_number:t,bank_name:c,sort_code:n,iban:o,bic:a,country_code:s})))}),N("bacs",{enabled:Boolean(x.enabled),description:String(x.description),settings:e})]),y(!1),t((0,n.__)("Settings updated successfully","woocommerce"))}catch(e){c((0,n.__)("Failed to update settings","woocommerce"))}finally{k(!1),u("getPaymentProviders",[]),_("getOfflinePaymentGateways")}})()},children:[(0,p.jsxs)(r.w.Section,{title:(0,n.__)("Enable and customise","woocommerce"),description:(0,n.__)("Choose how you want to present bank transfer to your customers during checkout.","woocommerce"),children:[m?(0,p.jsx)(l.W,{size:"small"}):(0,p.jsx)(a.CheckboxControl,{label:(0,n.__)("Enable direct bank transfers","woocommerce"),checked:Boolean(x.enabled),onChange:e=>{w({...x,enabled:e}),y(!0)}}),m?(0,p.jsx)(l.W,{size:"medium"}):(0,p.jsx)(a.TextControl,{label:(0,n.__)("Title","woocommerce"),help:(0,n.__)("Payment method name that the customer will see during checkout.","woocommerce"),placeholder:(0,n.__)("Direct bank transfer payments","woocommerce"),value:String(x.title),onChange:e=>{w({...x,title:e}),y(!0)}}),m?(0,p.jsx)(l.W,{size:"large"}):(0,p.jsx)(a.TextareaControl,{label:(0,n.__)("Description","woocommerce"),help:(0,n.__)("Payment method description that the customer will see during checkout.","woocommerce"),value:String(x.description),onChange:e=>{w({...x,description:e}),y(!0)}}),m?(0,p.jsx)(l.W,{size:"large"}):(0,p.jsx)(a.TextareaControl,{label:(0,n.__)("Instructions","woocommerce"),help:(0,n.__)("Instructions that will be added to the thank you page and emails.","woocommerce"),value:String(x.instructions),onChange:e=>{w({...x,instructions:e}),y(!0)}})]}),(0,p.jsx)(r.w.Section,{title:(0,n.__)("Account details","woocommerce"),description:(0,n.__)("Configure your bank account details.","woocommerce"),children:b?(0,p.jsx)(l.W,{size:"large"}):(0,p.jsx)(g,{accounts:C,onChange:e=>{S(e),y(!0)},defaultCountry:e})}),(0,p.jsx)(r.w.Actions,{children:(0,p.jsx)(a.Button,{variant:"primary",type:"submit",isBusy:j,disabled:j||!v,children:(0,n.__)("Save changes","woocommerce")})})]})})})},v=k}}]);