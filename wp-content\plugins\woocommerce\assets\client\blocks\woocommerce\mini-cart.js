import*as t from"@wordpress/interactivity";import*as e from"@woocommerce/stores/woocommerce/cart";var r={d:(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const n=(o={getConfig:()=>t.getConfig,getContext:()=>t.getContext,getElement:()=>t.getElement,store:()=>t.store,useLayoutEffect:()=>t.useLayoutEffect,useRef:()=>t.useRef},a={},r.d(a,o),a);var o,a;r.d({},{});function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function c(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var s={normalizePrecision:function(t){var e=t.reduce((function(t,e){return Math.max(t.getPrecision(),e.getPrecision())}));return t.map((function(t){return t.getPrecision()!==e?t.convertPrecision(e):t}))},minimum:function(t){var e=c(t),r=e[0],n=e.slice(1),o=r;return n.forEach((function(t){o=o.lessThan(t)?o:t})),o},maximum:function(t){var e=c(t),r=e[0],n=e.slice(1),o=r;return n.forEach((function(t){o=o.greaterThan(t)?o:t})),o}};function l(t){return!isNaN(parseInt(t))&&isFinite(t)}function m(t){return t%2==0}function d(t){return l(t)&&!Number.isInteger(t)}function p(t){return Math.abs(t)%1==.5}function g(t){return void 0===t}function f(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:".",r={};return Object.entries(t).forEach((function(t){if("object"===i(t[1])){var n=f(t[1]);Object.entries(n).forEach((function(n){r[t[0]+e+n[0]]=n[1]}))}else r[t[0]]=t[1]})),r}function h(){var t={HALF_ODD:function(t){var e=Math.round(t);return p(t)&&m(e)?e-1:e},HALF_EVEN:function(t){var e=Math.round(t);return p(t)?m(e)?e:e-1:e},HALF_UP:function(t){return Math.round(t)},HALF_DOWN:function(t){return p(t)?Math.floor(t):Math.round(t)},HALF_TOWARDS_ZERO:function(t){return p(t)?Math.sign(t)*Math.floor(Math.abs(t)):Math.round(t)},HALF_AWAY_FROM_ZERO:function(t){return p(t)?Math.sign(t)*Math.ceil(Math.abs(t)):Math.round(t)},DOWN:function(t){return Math.floor(t)}};return{add:function(t,e){return t+e},subtract:function(t,e){return t-e},multiply:function(t,e){return d(t)||d(e)?function(t,e){var r=function(t){return Math.pow(10,function(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0).toString();if(t.indexOf("e-")>0)return parseInt(t.split("e-")[1]);var e=t.split(".")[1];return e?e.length:0}(t))},n=Math.max(r(t),r(e));return Math.round(t*n)*Math.round(e*n)/(n*n)}(t,e):t*e},divide:function(t,e){return t/e},modulo:function(t,e){return t%e},round:function(e){return t[arguments.length>1&&void 0!==arguments[1]?arguments[1]:"HALF_EVEN"](e)}}}var y=h();function b(t){var e=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0;for(var r in e)t=t.replace("{{".concat(r,"}}"),e[r]);return t};return{getExchangeRate:function(r,n){return(o=t.endpoint,!Boolean(o)||"object"!==i(o)&&"function"!=typeof o||"function"!=typeof o.then?function(r,n){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,n){var o=Object.assign(new XMLHttpRequest,{onreadystatechange:function(){4===o.readyState&&(o.status>=200&&o.status<400?r(JSON.parse(o.responseText)):n(new Error(o.statusText)))},onerror:function(){n(new Error("Network error"))}});o.open("GET",t,!0),function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(var r in e)t.setRequestHeader(r,e[r])}(o,e.headers),o.send()}))}(e(t.endpoint,{from:r,to:n}),{headers:t.headers})}(r,n):t.endpoint).then((function(o){return f(o)[e(t.propertyPath,{from:r,to:n})]}));var o}}}function v(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Error;if(!t)throw new r(e)}function w(t){v(Number.isInteger(t),"You must provide an integer.",TypeError)}var I=h(),_=Object.assign((function t(e){var r=Object.assign({},{amount:t.defaultAmount,currency:t.defaultCurrency,precision:t.defaultPrecision},e),n=r.amount,o=r.currency,a=r.precision;w(n),w(a);var i=t.globalLocale,c=t.globalFormat,u=t.globalRoundingMode,s=t.globalFormatRoundingMode,m=Object.assign({},t.globalExchangeRatesApi),d=function(e){var r=Object.assign({},Object.assign({},{amount:n,currency:o,precision:a},e),Object.assign({},{locale:this.locale},e));return Object.assign(t({amount:r.amount,currency:r.currency,precision:r.precision}),{locale:r.locale})},p=function(t){v(this.hasSameCurrency(t),"You must provide a Dinero instance with the same currency.",TypeError)};return{getAmount:function(){return n},getCurrency:function(){return o},getLocale:function(){return this.locale||i},setLocale:function(t){return d.call(this,{locale:t})},getPrecision:function(){return a},convertPrecision:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s;w(t);var r=this.getPrecision(),n=t>r,o=n?I.multiply:I.divide,a=n?[t,r]:[r,t],i=Math.pow(10,I.subtract.apply(I,a));return d.call(this,{amount:I.round(o(this.getAmount(),i),e),precision:t})},add:function(e){p.call(this,e);var r=t.normalizePrecision([this,e]);return d.call(this,{amount:I.add(r[0].getAmount(),r[1].getAmount()),precision:r[0].getPrecision()})},subtract:function(e){p.call(this,e);var r=t.normalizePrecision([this,e]);return d.call(this,{amount:I.subtract(r[0].getAmount(),r[1].getAmount()),precision:r[0].getPrecision()})},multiply:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return d.call(this,{amount:I.round(I.multiply(this.getAmount(),t),e)})},divide:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return d.call(this,{amount:I.round(I.divide(this.getAmount(),t),e)})},percentage:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;return v(function(t){return l(t)&&t<=100&&t>=0}(t),"You must provide a numeric value between 0 and 100.",RangeError),this.multiply(I.divide(t,100),e)},allocate:function(t){var e=this;!function(t){v(function(t){return t.length>0&&t.every((function(t){return t>=0}))&&t.some((function(t){return t>0}))}(t),"You must provide a non-empty array of numeric values greater than 0.",TypeError)}(t);for(var r=t.reduce((function(t,e){return I.add(t,e)})),n=this.getAmount(),o=t.map((function(t){var o=Math.floor(I.divide(I.multiply(e.getAmount(),t),r));return n=I.subtract(n,o),d.call(e,{amount:o})})),a=0;n>0;)t[a]>0&&(o[a]=o[a].add(d.call(this,{amount:1})),n=I.subtract(n,1)),a+=1;return o},convert:function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.endpoint,o=void 0===n?m.endpoint:n,a=r.propertyPath,i=void 0===a?m.propertyPath||"rates.{{to}}":a,c=r.headers,s=void 0===c?m.headers:c,l=r.roundingMode,p=void 0===l?u:l,f=Object.assign({},{endpoint:o,propertyPath:i,headers:s,roundingMode:p});return b(f).getExchangeRate(this.getCurrency(),t).then((function(r){return v(!g(r),'No rate was found for the destination currency "'.concat(t,'".'),TypeError),d.call(e,{amount:I.round(I.multiply(e.getAmount(),parseFloat(r)),f.roundingMode),currency:t})}))},equalsTo:function(t){return this.hasSameAmount(t)&&this.hasSameCurrency(t)},lessThan:function(e){p.call(this,e);var r=t.normalizePrecision([this,e]);return r[0].getAmount()<r[1].getAmount()},lessThanOrEqual:function(e){p.call(this,e);var r=t.normalizePrecision([this,e]);return r[0].getAmount()<=r[1].getAmount()},greaterThan:function(e){p.call(this,e);var r=t.normalizePrecision([this,e]);return r[0].getAmount()>r[1].getAmount()},greaterThanOrEqual:function(e){p.call(this,e);var r=t.normalizePrecision([this,e]);return r[0].getAmount()>=r[1].getAmount()},isZero:function(){return 0===this.getAmount()},isPositive:function(){return this.getAmount()>=0},isNegative:function(){return this.getAmount()<0},hasSubUnits:function(){return 0!==I.modulo(this.getAmount(),Math.pow(10,a))},hasCents:function(){return 0!==I.modulo(this.getAmount(),Math.pow(10,a))},hasSameCurrency:function(t){return this.getCurrency()===t.getCurrency()},hasSameAmount:function(e){var r=t.normalizePrecision([this,e]);return r[0].getAmount()===r[1].getAmount()},toFormat:function(){var t,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,r=(t=/^(?:(\$|USD)?0(?:(,)0)?(\.)?(0+)?|0(?:(,)0)?(\.)?(0+)?\s?(dollar)?)$/gm.exec(arguments.length>0&&void 0!==arguments[0]?arguments[0]:c),{getMatches:function(){return null!==t?t.slice(1).filter((function(t){return!g(t)})):[]},getMinimumFractionDigits:function(){var t=function(t){return"."===t};return g(this.getMatches().find(t))?0:this.getMatches()[y.add(this.getMatches().findIndex(t),1)].split("").length},getCurrencyDisplay:function(){return{USD:"code",dollar:"name",$:"symbol"}[this.getMatches().find((function(t){return"USD"===t||"dollar"===t||"$"===t}))]},getStyle:function(){return g(this.getCurrencyDisplay(this.getMatches()))?"decimal":"currency"},getUseGrouping:function(){return!g(this.getMatches().find((function(t){return","===t})))}});return this.toRoundedUnit(r.getMinimumFractionDigits(),e).toLocaleString(this.getLocale(),{currencyDisplay:r.getCurrencyDisplay(),useGrouping:r.getUseGrouping(),minimumFractionDigits:r.getMinimumFractionDigits(),style:r.getStyle(),currency:this.getCurrency()})},toUnit:function(){return I.divide(this.getAmount(),Math.pow(10,a))},toRoundedUnit:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,r=Math.pow(10,t);return I.divide(I.round(I.multiply(this.toUnit(),r),e),r)},toObject:function(){return{amount:n,currency:o,precision:a}},toJSON:function(){return this.toObject()}}}),{defaultAmount:0,defaultCurrency:"USD",defaultPrecision:2},{globalLocale:"en-US",globalFormat:"$0,0.00",globalRoundingMode:"HALF_EVEN",globalFormatRoundingMode:"HALF_AWAY_FROM_ZERO",globalExchangeRatesApi:{endpoint:void 0,headers:void 0,propertyPath:void 0}},s);const C=_;function k(t,e){if(!t)return null;const r=window.getComputedStyle(t)[e];if("rgba(0, 0, 0, 0)"!==r&&"transparent"!==r){const t=r.match(/\d+/g);if(!t||t.length<3)return null;const[e,n,o]=t.slice(0,3);return`rgb(${e}, ${n}, ${o})`}return k(t.parentElement,e)}const x=(t,e)=>{if(!t?.currency_code)return e;const{currency_code:r,currency_symbol:n,currency_thousand_separator:o,currency_decimal_separator:a,currency_minor_unit:i,currency_prefix:c,currency_suffix:u}=t;return{code:r||e.code,symbol:n||e.symbol,thousandSeparator:"string"==typeof o?o:e.thousandSeparator,decimalSeparator:"string"==typeof a?a:e.decimalSeparator,minorUnit:Number.isFinite(i)?i:e.minorUnit,prefix:"string"==typeof c?c:e.prefix,suffix:"string"==typeof u?u:e.suffix}},A=(t,e,r)=>{if(""===t||void 0===t)return"";const n="number"==typeof t?t:parseInt(t,10);return Number.isFinite(n)?((t,e)=>{const{minorUnit:r,prefix:n,suffix:o,decimalSeparator:a,thousandSeparator:i}=e,c=t/10**r,{beforeDecimal:u,afterDecimal:s}=(t=>{const e=t.split(".");return{beforeDecimal:e[0],afterDecimal:e[1]||""}})(c.toString()),l=`${n}${((t,e)=>t.replace(/\B(?=(\d{3})+(?!\d))/g,e))(u,i)}${((t,e,r)=>t?`${e}${t.padEnd(r,"0")}`:r>0?`${e}${"0".repeat(r)}`:"")(s,a,r)}${o}`,m=document.createElement("textarea");return m.innerHTML=l,m.value})(n,{...e,...r}):""},M="I acknowledge that using a private store means my plugin will inevitably break on the next store release.",{currency:S,placeholderImgSrc:P}=(0,n.getConfig)("woocommerce"),{addToCartBehaviour:D,onCartClickBehaviour:E,checkoutUrl:F,displayCartPriceIncludingTax:L,buttonAriaLabelTemplate:O}=(0,n.getConfig)("woocommerce/mini-cart"),{reduceQuantityLabel:N,increaseQuantityLabel:T,quantityDescriptionLabel:H,removeFromCartLabel:R,lowInStockLabel:q}=(0,n.getConfig)("woocommerce/mini-cart-products-table-block"),{itemsInCartTextTemplate:$}=(0,n.getConfig)("woocommerce/mini-cart-title-items-counter-block");!function(){const t=document.createElement("style"),e=getComputedStyle(document.body).backgroundColor,r=document.querySelector(".wc-block-mini-cart__button"),n=k(r,"backgroundColor")||"#fff",o=k(r,"color")||"#000";t.appendChild(document.createTextNode(`div:where(.wp-block-woocommerce-mini-cart-contents) {\n\t\t\t\tbackground-color: ${e};\n\t\t\t}\n\t\t\tspan:where(.wc-block-mini-cart__badge) {\n\t\t\t\tbackground-color: ${o};\n\t\t\t\tcolor: ${n};\n\t\t\t}`)),document.head.appendChild(t)}();const{state:j,actions:U}=(0,n.store)("woocommerce",{},{lock:M}),{state:V,callbacks:z}=(0,n.store)("woocommerce/mini-cart",{},{lock:!0}),{state:B}=(0,n.store)("woocommerce/mini-cart",{},{lock:M});(0,n.store)("woocommerce/mini-cart",{state:{get totalItemsInCart(){return j.cart.items.reduce(((t,{quantity:e})=>t+e),0)},get formattedSubtotal(){const t=L?parseInt(j.cart.totals.total_items,10)+parseInt(j.cart.totals.total_items_tax,10):parseInt(j.cart.totals.total_items,10),e=x(j.cart.totals,S);return A(t,e)},get drawerRole(){return B.isOpen?"dialog":null},get drawerTabIndex(){return B.isOpen?"-1":null},get drawerOverlayClass(){const t="wc-block-components-drawer__screen-overlay wc-block-components-drawer__screen-overlay--with-slide-out";return B.isOpen?`${t} wc-block-components-drawer__screen-overlay--with-slide-in`:`${t} wc-block-components-drawer__screen-overlay--is-hidden`},get badgeIsVisible(){const t=V.totalItemsInCart>0,{productCountVisibility:e}=(0,n.getContext)();return"always"===e||"greater_than_zero"===e&&t},get cartIsEmpty(){return 0===V.totalItemsInCart},get buttonAriaLabel(){return O.replace("%d",B.totalItemsInCart).replace("%1$d",B.totalItemsInCart).replace("%2$s",B.formattedSubtotal)},get shouldShowTaxLabel(){return parseInt(j.cart.totals.total_items_tax,10)>0}},callbacks:{setupOpenDrawerListener:()=>("open_drawer"===D&&document.body.addEventListener("wc-blocks_added_to_cart",z.openDrawer),()=>{document.body.removeEventListener("wc-blocks_added_to_cart",z.openDrawer)}),openDrawer(){"navigate_to_checkout"!==E?B.isOpen=!0:window.location.href=F},closeDrawer(){B.isOpen=!1},overlayCloseDrawer(t){t.target===t.currentTarget&&(B.isOpen=!1)},disableScrollingOnBody(){B.isOpen?Object.assign(document.body.style,{overflow:"hidden",paddingRight:window.innerWidth-document.documentElement.clientWidth+"px"}):Object.assign(document.body.style,{overflow:"",paddingRight:0})}}},{lock:M});const{state:Q}=(0,n.store)("woocommerce/mini-cart-products-table-block",{state:{get cartItem(){const{cartItem:{id:t}}=(0,n.getContext)("woocommerce");return j.cart.items.find((e=>e.id===t))},get currency(){return x(j.cart.totals,S)},get cartItemDiscount(){const{prices:t}=Q.cartItem,e=C({amount:parseInt(t.raw_prices.regular_price,10),precision:t.raw_prices.precision}),r=C({amount:parseInt(t.raw_prices.price,10),precision:t.raw_prices.precision}),n=e.subtract(r).convertPrecision(Q.currency.minorUnit).getAmount(),o=A(n,Q.currency);return window.wc?.blocksCheckout?.applyCheckoutFilter?window.wc.blocksCheckout.applyCheckoutFilter({filterName:"saleBadgePriceFormat",defaultValue:"<price/>",extensions:Q.cartItem.extensions,arg:{context:"cart",cartItem:Q.cartItem,cart:j.cart}}).replace("<price/>",o):o},get lineItemDiscount(){const{quantity:t,prices:e}=Q.cartItem,r=C({amount:parseInt(e.raw_prices.regular_price,10),precision:e.raw_prices.precision}),n=C({amount:parseInt(e.raw_prices.price,10),precision:e.raw_prices.precision}),o=r.subtract(n).multiply(t).convertPrecision(Q.currency.minorUnit).getAmount(),a=A(o,Q.currency);return window.wc?.blocksCheckout?.applyCheckoutFilter?window.wc.blocksCheckout.applyCheckoutFilter({filterName:"saleBadgePriceFormat",defaultValue:"<price/>",extensions:Q.cartItem.extensions,arg:{context:"cart",cartItem:Q.cartItem,cart:j.cart}}).replace("<price/>",a):a},get cartItemHasDiscount(){return Q.cartItem.prices.regular_price!==Q.cartItem.prices.price},get minimumReached(){const{quantity:t,quantity_limits:{minimum:e,multiple_of:r=1}}=Q.cartItem;return t-r<e},get maximumReached(){const{quantity:t,quantity_limits:{maximum:e,multiple_of:r=1}}=Q.cartItem;return t+r>e},get reduceQuantityLabel(){return N.replace("%s",Q.cartItemName)},get increaseQuantityLabel(){return T.replace("%s",Q.cartItemName)},get quantityDescriptionLabel(){return H.replace("%s",Q.cartItemName)},get removeFromCartLabel(){return R.replace("%s",Q.cartItemName)},get cartItemName(){const t=document.createElement("textarea");let{name:e}=Q.cartItem;return window.wc?.blocksCheckout?.applyCheckoutFilter&&(e=window.wc.blocksCheckout.applyCheckoutFilter({filterName:"itemName",defaultValue:e,extensions:Q.cartItem.extensions,arg:{context:"cart",cartItem:Q.cartItem,cart:j.cart}})),t.innerHTML=e,t.value},get itemThumbnail(){return Q.cartItem.images[0]?.thumbnail||P},get priceWithoutDiscount(){return A(parseInt(Q.cartItem.prices.regular_price,10),Q.currency)},get beforeItemPrice(){return window.wc?.blocksCheckout?.applyCheckoutFilter?window.wc.blocksCheckout.applyCheckoutFilter({filterName:"subtotalPriceFormat",defaultValue:"<price/>",extensions:Q.cartItem.extensions,arg:{context:"cart",cartItem:Q.cartItem,cart:j.cart}}).split("<price/>")[0]:null},get afterItemPrice(){return window.wc?.blocksCheckout?.applyCheckoutFilter?window.wc.blocksCheckout.applyCheckoutFilter({filterName:"subtotalPriceFormat",defaultValue:"<price/>",extensions:Q.cartItem.extensions,arg:{context:"cart",cartItem:Q.cartItem,cart:j.cart}}).split("<price/>")[1]:null},get itemPrice(){return A(parseInt(Q.cartItem.prices.price,10),Q.currency)},get lineItemTotal(){const{totals:t}=Q.cartItem,e=Q.currency,r=L?parseInt(t.line_subtotal,10)+parseInt(t.line_subtotal_tax,10):parseInt(t.line_subtotal,10),n=A(r,e);return window.wc?.blocksCheckout?.applyCheckoutFilter?window.wc.blocksCheckout.applyCheckoutFilter({filterName:"cartItemPrice",defaultValue:"<price/>",extensions:Q.cartItem.extensions,arg:{context:"cart",cartItem:Q.cartItem,cart:j.cart}}).replace("<price/>",n):n},get isLineItemTotalDiscountVisible(){return Q.cartItemHasDiscount&&Q.cartItem.quantity>1},get isProductHiddenFromCatalog(){const t=(0,n.getContext)(),{catalog_visibility:e}=Q.cartItem;return("hidden"===e||"search"===e)&&!t.isImageHidden},get isLowInStockVisible(){return!Q.cartItem.show_backorder_badge&&!!Q.cartItem.low_stock_remaining},get lowInStockLabel(){return q.replace("%d",Q.cartItem.low_stock_remaining)},get itemShowRemoveItemLink(){return!window.wc?.blocksCheckout?.applyCheckoutFilter||window.wc.blocksCheckout.applyCheckoutFilter({filterName:"showRemoveItemLink",defaultValue:!0,extensions:Q.cartItem.extensions,arg:{context:"cart",cartItem:Q.cartItem,cart:j.cart}})},get cartItemDataAttr(){const{itemData:t,dataProperty:e}=(0,n.getContext)(),r=t||Q.cartItem[e]?.[0];if(!r)return{hidden:!0};const o=r.key||r.attribute,a=document.createElement("textarea");a.innerHTML=o+":";const i=document.createElement("textarea");return i.innerHTML=r.value,{name:a.value,value:i.value,className:`wc-block-components-product-details__${o.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[\s_]+/g,"-").toLowerCase()}`,hidden:"1"===r.hidden}},get itemDataHasMultipleAttributes(){const{dataProperty:t}=(0,n.getContext)();return Q.cartItem[t]?.length>1},get shouldHideProductDetails(){const{dataProperty:t}=(0,n.getContext)();return 0===Q.cartItem[t].length||"variation"===t&&"variation"!==Q.cartItem.type},get shouldHideSingleProductDetails(){return Q.shouldHideProductDetails||Q.itemDataHasMultipleAttributes},get shouldHideMultipleProductDetails(){return Q.shouldHideProductDetails||!Q.itemDataHasMultipleAttributes}},actions:{overrideInvalidQuantity(t){const e=t.target,r=e.value,{minimum:n,maximum:o}=Q.cartItem.quantity_limits,a=parseInt(r,10);if(Number.isNaN(a))return void(e.value=Q.cartItem.quantity.toString());let i=a;a<n?i=n:a>o&&(i=o),Q.cartItem.quantity=i},*changeQuantity(){const t=Q.cartItem.variation.map((({raw_attribute:t,...e})=>({...e,attribute:t})));yield U.addCartItem({id:Q.cartItem.id,quantity:Q.cartItem.quantity,variation:t})},*removeItemFromCart(){yield U.removeCartItem(Q.cartItem.key)},*incrementQuantity(){const{multiple_of:t=1}=Q.cartItem.quantity_limits,e=Q.cartItem.variation.map((({raw_attribute:t,...e})=>({...e,attribute:t})));yield U.addCartItem({id:Q.cartItem.id,quantity:Q.cartItem.quantity+t,variation:e})},*decrementQuantity(){const{multiple_of:t=1}=Q.cartItem.quantity_limits,e=Q.cartItem.variation.map((({raw_attribute:t,...e})=>({...e,attribute:t})));yield U.addCartItem({id:Q.cartItem.id,quantity:Q.cartItem.quantity-t,variation:e})},hideImage(){(0,n.getContext)().isImageHidden=!0}},callbacks:{itemShortDescription(){const{ref:t}=(0,n.getElement)();if(t){const e=t.querySelector(".wc-block-components-product-metadata__description"),{short_description:r,description:n}=Q.cartItem;e&&(r||n)&&(e.innerHTML=((t,e=15)=>{const r=t.trim().split(/\s+/);return r.length<=e?t:r.slice(0,e).join(" ")+"…"})(r||n))}},filterCartItemClass(){const t=window.wc?.blocksCheckout?.applyCheckoutFilter,e=(0,n.useRef)([]);(0,n.useLayoutEffect)((()=>{if(t){const{ref:r}=(0,n.getElement)();r.classList.remove(...e.current);const o=t({filterName:"cartItemClass",defaultValue:"",extensions:Q.cartItem.extensions,arg:{context:"cart",cartItem:Q.cartItem,cart:j.cart}});e.current=o.split(" ").filter(Boolean),r.classList.add(...e.current)}}))}}},{lock:!0});(0,n.store)("woocommerce/mini-cart-title-items-counter-block",{state:{get itemsInCartText(){const t=V.totalItemsInCart;return $.replace("%d",t.toString())}}},{lock:!0});