"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[5841],{57506:(e,s,t)=>{t.r(s),t.d(s,{Products:()=>L});var o=t(27723),c=t(85816),i=t(14908),n=t(92279),r=t(86087),a=t(56427),l=t(15703),d=t(24148),p=t(56537),m=t(90700),u=t(83306),k=t(52619),_=t(40314),w=t(47143),g=t(56109),h=t(64321),x=t(51614),A=t(48958),C=t(16527),b=t(71506),j=t(10952),y=t(85259),P=t(30288),v=t(3246),f=t(50348),S=t(39793);const T=({isExpanded:e,onClick:s})=>(0,S.jsxs)(a.<PERSON>,{className:"woocommerce-task-products__button-view-less-product-types",onClick:s,children:[e?(0,o.__)("View less product types","woocommerce"):(0,o.__)("View more product types","woocommerce"),(0,S.jsx)(d.A,{icon:e?p.A:m.A})]}),L=()=>{const[e,s]=(0,r.useState)(!1),[t,c]=(0,r.useState)(!1),{installedPlugins:n,isRequestingPlugins:a}=(0,w.useSelect)((e=>{const{getInstalledPlugins:s,isPluginsRequesting:t}=e(_.pluginsStore);return{isRequestingPlugins:t("installPlugins"),installedPlugins:s()}}),[]),d=(0,h.p)((()=>{const e=(0,g.Qk)("onboarding");return e?.profile&&e?.profile.product_types||["physical"]})()),{productTypes:p,isRequesting:m}=(0,x.A)((0,h.h)(),d),{recordCompletionTime:L}=(0,y.A)("products"),E=(0,r.useMemo)((()=>p.map((e=>({...e,onClick:()=>{e.onClick(),L()}})))),[L,p]),{loadSampleProduct:N,isLoadingSampleProducts:F}=(0,b.A)({redirectUrlAfterSuccess:(0,l.getAdminLink)("edit.php?post_type=product&wc_onboarding_active_task=products")}),O=(0,r.useMemo)((()=>{const s=E.filter((e=>d.includes(e.key)));return e&&E.forEach((e=>!s.includes(e)&&s.push(e))),(0,k.applyFilters)(P.j1,s)}),[d,e,E]),q=(0,r.useMemo)((()=>{const e=[{...P.p3,onClick:()=>{P.p3.onClick(),L()}}];return!window.wcAdminFeatures?.printful||a||n.includes("printful-shipping-for-woocommerce")||e.push(P.tF),e}),[L,a,n]);return(0,S.jsxs)("div",{className:"woocommerce-task-products",children:[(0,S.jsx)(i.Text,{variant:"title",as:"h2",className:"woocommerce-task-products__title",children:(0,o.__)("What product do you want to add?","woocommerce")}),(0,S.jsxs)("div",{className:"woocommerce-product-content",children:[(0,S.jsx)(A.A,{items:O,onClickLoadSampleProduct:()=>c(!0),showOtherOptions:e,isTaskListItemClicked:m}),(0,S.jsx)(T,{isExpanded:e,onClick:()=>{e||(0,u.recordEvent)("tasklist_view_more_product_types_click"),s(!e)}}),(0,S.jsx)(A.A,{items:q,showOtherOptions:!1,isTaskListItemClicked:m}),(0,S.jsx)(v.d,{textProps:{className:"woocommerce-products-marketplace-link"},message:(0,o.__)("Visit {{Link}}the WooCommerce Marketplace{{/Link}} to enhance your store with additional options such as Subscriptions, Gift Cards, and more.","woocommerce"),eventName:"tasklist_add_product_visit_marketplace_click",targetUrl:(0,f.isFeatureEnabled)("marketplace")?(0,l.getAdminLink)("admin.php?page=wc-admin&tab=extensions&path=/extensions&category=merchandising"):"https://woocommerce.com/product-category/woocommerce-extensions/merchandising/",linkType:(0,f.isFeatureEnabled)("marketplace")?"wc-admin":"external"})]}),F?(0,S.jsx)(C.A,{}):t&&(0,S.jsx)(j.A,{onCancel:()=>{c(!1),(0,u.recordEvent)("tasklist_cancel_load_sample_products_click")},onImport:()=>{c(!1),N()}})]})},E=()=>(0,S.jsx)(c.WooOnboardingTask,{id:"products",children:(0,S.jsx)(L,{})});(0,n.registerPlugin)("wc-admin-onboarding-task-products",{scope:"woocommerce-tasks",render:()=>(0,S.jsx)(E,{})})}}]);