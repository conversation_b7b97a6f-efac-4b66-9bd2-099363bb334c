import*as e from"@wordpress/interactivity";var t={438:e=>{e.exports=import("@wordpress/interactivity-router")}},r={};function i(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,i),a.exports}i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const o=(v={getConfig:()=>e.getConfig,getContext:()=>e.getContext,getServerContext:()=>e.getServerContext,store:()=>e.store},m={},i.d(m,v),m),a=e=>{if("string"!=typeof e)return"";const t=document.implementation.createHTMLDocument("");return t.body.innerHTML=e,t.body.textContent||""},{getContext:n,store:s,getServerContext:c,getConfig:l}=o,p="woocommerce/product-filters";var v,m;const u={state:{get params(){const{activeFilters:e}=n(),t={};function r(e,r){if(e in t&&t[e].length>0)return t[e]=`${t[e]},${r}`;t[e]=r}const i=l(p),o=i?.taxonomyParamsMap||{};return e.forEach((e=>{const{type:i,value:a}=e;if(a){if("price"===i){const[e,r]=a.split("|");e&&(t.min_price=e),r&&(t.max_price=r)}if("status"===i&&r("filter_stock_status",a),"rating"===i&&r("rating_filter",a),i.includes("attribute")){const[,o]=i.split("/");r(`filter_${o}`,a),t[`query_type_${o}`]=e.attributeQueryType||"or"}if(i.includes("taxonomy")){const[,e]=i.split("/");r(o[e],a)}}})),t},get activeFilters(){const{activeFilters:e}=n();return e.filter((e=>!!e.value)).sort(((e,t)=>e.activeLabel.toLowerCase().localeCompare(t.activeLabel.toLowerCase()))).map((e=>({...e,activeLabel:a(e.activeLabel),uid:`${e.type}/${e.value}`})))},get isFilterSelected(){const{activeFilters:e,item:t}=n();return e.some((e=>e.type===t.type&&e.value===t.value))}},actions:{openOverlay:()=>{if(n().isOverlayOpened=!0,document.getElementById("wpadminbar")){const e=(document.documentElement||document.body.parentNode||document.body).scrollTop;document.body.style.setProperty("--adminbar-mobile-padding",`max(calc(var(--wp-admin--admin-bar--height) - ${e}px), 0px)`)}},closeOverlay:()=>{n().isOverlayOpened=!1},closeOverlayOnEscape:e=>{n().isOverlayOpened&&"Escape"===e.key&&d.closeOverlay()},removeActiveFiltersBy:e=>{const t=n();t.activeFilters=t.activeFilters.filter((t=>!e(t)))},toggleFilter:()=>{y.isFilterSelected?function(){const{item:e}=n();d.removeActiveFiltersBy((t=>t.type===e.type&&t.value===e.value))}():function(){const e=n(),t={value:e.item.value,type:e.item.type,attributeQueryType:e.item.attributeQueryType,activeLabel:e.activeLabelTemplate.replace("{{label}}",e.item?.ariaLabel||e.item.label)},r=e.activeFilters.filter((e=>!(e.value===t.value&&e.type===t.type)));r.push(t),e.activeFilters=r}(),d.navigate()},*navigate(){const e=c?c():n(),t=l(p).canonicalUrl,r=new URL(t),{searchParams:o}=r;for(const t in e.params)o.delete(t);for(const e in y.params)o.set(e,y.params[e]);if(window.location.href===r.href)return;const a=l("woocommerce"),s=l(p),v=a?.isBlockTheme||!1,m=s?.isProductArchive||!1;if(a?.needsRefreshForInteractivityAPI||!v&&m)return window.location.href=r.href;const u=yield Promise.resolve().then(i.bind(i,438));yield u.actions.navigate(r.href)}},callbacks:{scrollLimit:()=>{const{isOverlayOpened:e}=n();document.body.style.overflow=e?"hidden":"auto"}}},{state:y,actions:d}=s(p,u);