import*as t from"@wordpress/interactivity";var e={d:(t,r)=>{for(var a in r)e.o(r,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:r[a]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const r=(i={getConfig:()=>t.getConfig,getContext:()=>t.getContext,store:()=>t.store},o={},e.d(o,i),o),a=(t,e)=>Array.isArray(t)&&Array.isArray(e)&&0!==t.length&&0!==e.length&&t.find((t=>Object.entries(t.attributes).every((([t,r])=>e.some((e=>!(e.attribute!==t)&&(e.value===r||e.value&&""===r)))))))||null;var i,o;function n(t,e){if(!t)return null;const r=window.getComputedStyle(t)[e];if("rgba(0, 0, 0, 0)"!==r&&"transparent"!==r){const t=r.match(/\d+/g);if(!t||t.length<3)return null;const[e,a,i]=t.slice(0,3);return`rgb(${e}, ${a}, ${i})`}return n(t.parentElement,e)}!function(){const t=document.querySelector(".wc-block-add-to-cart-with-options-variation-selector-attribute-options__pills");if(!t)return;const e=document.createElement("style"),r=n(t,"backgroundColor")||"#fff",a=n(t,"color")||"#000";e.appendChild(document.createTextNode(`:where(.wc-block-add-to-cart-with-options-variation-selector-attribute-options__pill:has(.wc-block-add-to-cart-with-options-variation-selector-attribute-options__pill-input:checked)) {\n\t\t\t\t--pill-color: ${r};\n\t\t\t\t--pill-background-color: ${a};\n\t\t\t}`)),document.head.appendChild(e)}();const l="I acknowledge that using a private store means my plugin will inevitably break on the next store release.",{actions:s,state:u}=(0,r.store)("woocommerce/add-to-cart-with-options",{state:{get variationId(){const t=(0,r.getContext)();if(!t)return null;const{availableVariations:e,selectedAttributes:i}=t,o=a(e,i);return o?.variation_id||null},get selectedAttributes(){const t=(0,r.getContext)();return t?t.selectedAttributes:[]},get isOptionSelected(){const{selectedValue:t,option:e}=(0,r.getContext)();return t===e.value},get isOptionDisabled(){const{name:t,option:e,selectedAttributes:a,availableVariations:i}=(0,r.getContext)();return""!==e.value&&!(({attributeName:t,attributeValue:e,selectedAttributes:r,availableVariations:a})=>{if(!(t&&e&&Array.isArray(r)&&Array.isArray(a)))return!1;const i=r.some((e=>e.attribute===t))?r.length-1:r.length;return a.some((a=>(a.attributes[t]===e||""===a.attributes[t])&&r.filter((r=>{const i=a.attributes[r.attribute];return i===r.value||""===i&&(r.attribute!==t||e===r.value)})).length>=i))})({attributeName:t,attributeValue:e.value,selectedAttributes:a,availableVariations:i})}},actions:{setAttribute(t,e){const{selectedAttributes:a}=(0,r.getContext)(),i=a.findIndex((e=>e.attribute===t));""!==e?i>=0?a[i]={attribute:t,value:e}:a.push({attribute:t,value:e}):i>=0&&a.splice(i,1)},removeAttribute(t){const{selectedAttributes:e}=(0,r.getContext)(),a=e.findIndex((e=>e.attribute===t));a>=0&&e.splice(a,1)},handlePillClick(){if(u.isOptionDisabled)return;const t=(0,r.getContext)();t.selectedValue===t.option.value?t.selectedValue="":t.selectedValue=t.option.value,s.setAttribute(t.name,t.selectedValue)},handleDropdownChange(t){const e=(0,r.getContext)();e.selectedValue=t.currentTarget.value,s.setAttribute(e.name,e.selectedValue)}},callbacks:{setDefaultSelectedAttribute(){const t=(0,r.getContext)();t.selectedValue&&s.setAttribute(t.name,t.selectedValue)},setSelectedVariationId:()=>{const{availableVariations:t,selectedAttributes:e}=(0,r.getContext)(),i=a(t,e),{actions:o}=(0,r.store)("woocommerce/product-data",{},{lock:l}),n=i?.variation_id;o.setVariationId(null!=n?n:null)},validateVariation(){s.clearErrors("variable-product");const{availableVariations:t,selectedAttributes:e}=(0,r.getContext)(),i=a(t,e),{errorMessages:o}=(0,r.getConfig)();i?.variation_id?i?.is_in_stock||s.addError({code:"variableProductOutOfStock",message:o?.variableProductOutOfStock||"",group:"variable-product"}):s.addError({code:"variableProductMissingAttributes",message:o?.variableProductMissingAttributes||"",group:"variable-product"})}}},{lock:l});