{"name": "woocommerce/checkout-order-summary-cart-items-block", "version": "1.0.0", "title": "Cart Items", "description": "Shows cart items.", "category": "woocommerce", "supports": {"align": false, "html": false, "multiple": false, "reusable": false, "lock": false}, "attributes": {"className": {"type": "string", "default": ""}, "disableProductDescriptions": {"type": "boolean", "default": false}, "lock": {"type": "object", "default": {"remove": true, "move": false}}}, "parent": ["woocommerce/checkout-order-summary-block"], "textdomain": "woocommerce", "$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3}