{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "woocommerce/product-filter-attribute", "title": "Attribute Filter", "description": "Enable customers to filter the product grid by selecting one or more attributes, such as color.", "category": "woocommerce", "keywords": ["WooCommerce"], "textdomain": "woocommerce", "apiVersion": 3, "ancestor": ["woocommerce/product-filters"], "supports": {"interactivity": true, "color": {"text": true, "background": false, "__experimentalDefaultControls": {"text": false}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontWeight": true, "__experimentalFontFamily": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": false}}, "spacing": {"margin": true, "padding": true, "blockGap": true, "__experimentalDefaultControls": {"margin": false, "padding": false, "blockGap": false}}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": false, "radius": false, "style": false, "width": false}}}, "usesContext": ["query", "filterParams"], "attributes": {"attributeId": {"type": "number", "default": 0}, "showCounts": {"type": "boolean", "default": false}, "queryType": {"type": "string", "default": "or"}, "displayStyle": {"type": "string", "default": "woocommerce/product-filter-checkbox-list"}, "selectType": {"type": "string", "default": "multiple"}, "isPreview": {"type": "boolean", "default": false}, "sortOrder": {"type": "string", "default": "count-desc"}, "hideEmpty": {"type": "boolean", "default": true}}, "example": {"attributes": {"isPreview": true}}, "style": "woocommerce/product-filter-attribute-view-style"}