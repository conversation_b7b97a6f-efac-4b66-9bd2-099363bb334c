(()=>{"use strict";var e={d:(t,o)=>{for(var a in o)e.o(o,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:o[a]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ADMIN_URL:()=>U,COUNTRIES:()=>E,CURRENCY:()=>V,CURRENT_USER_IS_ADMIN:()=>C,HOME_URL:()=>I,LOCALE:()=>R,ORDER_STATUSES:()=>P,PLACEHOLDER_IMG_SRC:()=>T,SITE_CURRENCY:()=>D,SITE_TITLE:()=>A,STORE_PAGES:()=>z,WC_ASSET_URL:()=>O,WC_VERSION:()=>q,WP_LOGIN_URL:()=>M,WP_VERSION:()=>N,allSettings:()=>i,defaultFields:()=>W,getAdminLink:()=>w,getCurrencyPrefix:()=>x,getCurrencySuffix:()=>L,getPaymentMethodData:()=>v,getSetting:()=>h,getSettingWithCoercion:()=>S,isWcVersion:()=>g,isWpVersion:()=>b}),(0,window.wp.hooks.addFilter)("woocommerce_admin_analytics_settings","woocommerce-blocks/exclude-draft-status-from-analytics",(e=>{const t=e=>"customStatuses"===e.key?{...e,options:e.options.filter((e=>"checkout-draft"!==e.value))}:e,o=e.woocommerce_actionable_order_statuses.options.map(t),a=e.woocommerce_excluded_report_order_statuses.options.map(t);return{...e,woocommerce_actionable_order_statuses:{...e.woocommerce_actionable_order_statuses,options:o},woocommerce_excluded_report_order_statuses:{...e.woocommerce_excluded_report_order_statuses,options:a}}}));const o={adminUrl:"",countries:[],countryData:{},currency:{code:"USD",precision:2,symbol:"$",symbolPosition:"left",decimalSeparator:".",priceFormat:"%1$s%2$s",thousandSeparator:","},currentUserId:0,currentUserIsAdmin:!1,homeUrl:"",locale:{siteLocale:"en_US",userLocale:"en_US",weekdaysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},orderStatuses:[],placeholderImgSrc:"",siteTitle:"",storePages:[],wcAssetUrl:"",wcVersion:"",wpLoginUrl:"",wpVersion:""},a="object"==typeof window.wcSettings?window.wcSettings:{},i={...o,...a};function n(e,t){const o=s(e),a=s(t),i=o.pop(),n=a.pop(),r=u(o,a);return 0!==r?r:i&&n?u(i.split("."),n.split(".")):i||n?i?-1:1:0}i.currency={...o.currency,...i.currency},i.locale={...o.locale,...i.locale};const r=(e,t,o)=>{_(o);const a=n(e,t);return m[o].includes(a)};n.validate=e=>"string"==typeof e&&/^[v\d]/.test(e)&&l.test(e),n.compare=r,n.sastisfies=(e,t)=>{const o=t.match(/^([<>=~^]+)/),a=o?o[1]:"=";if("^"!==a&&"~"!==a)return r(e,t,a);const[i,n,l]=s(e),[d,c,m]=s(t);return 0===p(i,d)&&("^"===a?u([n,l],[c,m])>=0:0===p(n,c)&&p(l,m)>=0)};const l=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,s=e=>{if("string"!=typeof e)throw new TypeError("Invalid argument expected string");const t=e.match(l);if(!t)throw new Error(`Invalid argument not valid semver ('${e}' received)`);return t.shift(),t},d=e=>"*"===e||"x"===e||"X"===e,c=e=>{const t=parseInt(e,10);return isNaN(t)?e:t},p=(e,t)=>{if(d(e)||d(t))return 0;const[o,a]=((e,t)=>typeof e!=typeof t?[String(e),String(t)]:[e,t])(c(e),c(t));return o>a?1:o<a?-1:0},u=(e,t)=>{for(let o=0;o<Math.max(e.length,t.length);o++){const a=p(e[o]||0,t[o]||0);if(0!==a)return a}return 0},m={">":[1],">=":[0,1],"=":[0],"<=":[-1,0],"<":[-1]},y=Object.keys(m),_=e=>{if("string"!=typeof e)throw new TypeError("Invalid operator type, expected string but got "+typeof e);if(-1===y.indexOf(e))throw new Error(`Invalid operator, expected one of ${y.join("|")}`)},h=(e,t=!1,o=(e,t)=>void 0!==e?e:t)=>{let a=t;if(e in i)a=i[e];else if(e.includes("_data")){const o=e.replace("_data",""),i=h("paymentMethodData",{});a=o in i?i[o]:t}return o(a,t)},S=(e,t,o)=>{const a=e in i?i[e]:t;return o(a,t)?a:t},f=(e,t,o)=>{let a=h(e,"");return/^\d+\.\d+-.*$/.test(a)&&(a=a.replace(/-[a-zA-Z0-9]*[\-.]*/,".0-rc."),a=a.endsWith(".")?a.substring(0,a.length-1):a),n.compare(a,t,o)},b=(e,t="=")=>f("wpVersion",e,t),g=(e,t="=")=>f("wcVersion",e,t),w=e=>h("adminUrl")+e,v=(e,t=null)=>{var o;return null!==(o=h("paymentMethodData",{})[e])&&void 0!==o?o:t},x=(e,t)=>({left:e,left_space:e+" ",right:"",right_space:""}[t]||""),L=(e,t)=>({left:"",left_space:"",right:e,right_space:" "+e}[t]||""),U=i.adminUrl,E=i.countries,C=i.currentUserIsAdmin,I=i.homeUrl,R=i.locale,P=i.orderStatuses,T=i.placeholderImgSrc,A=i.siteTitle,z=i.storePages,O=i.wcAssetUrl,q=i.wcVersion,M=i.wpLoginUrl,N=i.wpVersion,V=i.currency,D={code:V.code,symbol:V.symbol,thousandSeparator:V.thousandSeparator,decimalSeparator:V.decimalSeparator,minorUnit:V.precision,prefix:x(V.symbol,V.symbolPosition),suffix:L(V.symbol,V.symbolPosition)},F=window.wc.wcTypes,W=S("defaultFields",{email:{label:"Email address",optionalLabel:"Email address (optional)",required:!0,hidden:!1,autocomplete:"email",autocapitalize:"none",type:"email",index:0,validation:[]},country:{label:"Country/Region",optionalLabel:"Country/Region (optional)",required:!0,hidden:!1,autocomplete:"country",index:1,validation:[]},first_name:{label:"First name",optionalLabel:"First name (optional)",required:!0,hidden:!1,autocomplete:"given-name",autocapitalize:"sentences",index:10,validation:[]},last_name:{label:"Last name",optionalLabel:"Last name (optional)",required:!0,hidden:!1,autocomplete:"family-name",autocapitalize:"sentences",index:20,validation:[]},company:{label:"Company",optionalLabel:"Company (optional)",required:!1,hidden:!0,autocomplete:"organization",autocapitalize:"sentences",index:30,validation:[]},address_1:{label:"Address",optionalLabel:"Address (optional)",required:!0,hidden:!1,autocomplete:"address-line1",autocapitalize:"sentences",index:40,validation:[]},address_2:{label:"Apartment, suite, etc.",optionalLabel:"Apartment, suite, etc. (optional)",required:!1,hidden:!1,autocomplete:"address-line2",autocapitalize:"sentences",index:50,validation:[]},city:{label:"City",optionalLabel:"City (optional)",required:!0,hidden:!1,autocomplete:"address-level2",autocapitalize:"sentences",index:70,validation:[]},state:{label:"State/County",optionalLabel:"State/County (optional)",required:!0,hidden:!1,autocomplete:"address-level1",autocapitalize:"sentences",index:80,validation:[]},postcode:{label:"Postal code",optionalLabel:"Postal code (optional)",required:!0,hidden:!1,autocomplete:"postal-code",autocapitalize:"characters",index:90,validation:[]},phone:{label:"Phone",optionalLabel:"Phone (optional)",required:!0,hidden:!1,type:"tel",autocomplete:"tel",autocapitalize:"characters",index:100,validation:[]}},F.isFormFields);(this.wc=this.wc||{}).wcSettings=t})();