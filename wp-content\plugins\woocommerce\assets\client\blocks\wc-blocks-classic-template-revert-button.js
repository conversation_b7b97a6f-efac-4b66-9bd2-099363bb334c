(()=>{var e={146:()=>{}},t={};function o(r){var n=t[r];if(void 0!==n)return n.exports;var c=t[r]={exports:{}};return e[r](c,c.exports,o),c.exports}(()=>{"use strict";const e=window.wp.editor,t=window.wp.data,r=window.wp.blocks,n=window.wp.components,c=window.wp.i18n,i=window.wp.element,s=window.wp.coreData,l=window.wp.blockEditor,d=window.wp.plugins;o(146);const a=window.ReactJSXRuntime,u=e=>e.some((e=>"woocommerce/legacy-template"===e.name||u(e.innerBlocks))),p=()=>{const{blocks:o,editedPostId:d}=(0,t.useSelect)((e=>({blocks:e(l.store).getBlocks(),editedPostId:e("core/edit-site").getEditedPostId()})),[]),{replaceBlocks:p}=(0,t.useDispatch)(l.store),w=(0,s.useEntityRecord)("postType","wp_template",d),m=(0,i.useMemo)((()=>u(o)),[o]),g=(0,i.useMemo)((()=>(e=>e.reduce(((e,t)=>"core/template-part"===t.name?e:[...e,t.clientId]),[]))(o)),[o]);return(0,a.jsx)(a.Fragment,{children:!m&&(0,a.jsx)(e.PluginDocumentSettingPanel,{name:"wc-block-editor-revert-button-panel",children:(0,a.jsxs)("div",{className:"wc-block-editor-revert-button-container",children:[(0,a.jsx)(n.Button,{variant:"secondary",onClick:()=>{p(g,(0,r.createBlock)("core/group",{layout:{inherit:!0,type:"constrained"}},[(0,r.createBlock)("woocommerce/legacy-template",{template:w?.record?.slug})]))},children:(0,c.__)("Revert to Classic Template","woocommerce")}),(0,a.jsx)("span",{children:(0,i.createInterpolateElement)((0,c.__)("The <strongText /> template doesn’t allow for reordering or customizing blocks, but might work better with your extensions.","woocommerce"),{strongText:(0,a.jsx)("strong",{children:w?.record?.title?.rendered?`${w.record.title.rendered} (Classic)`:""})})})]})})})},w=["single-product","archive-product","product-search-results","taxonomy-product_cat","taxonomy-product_tag","taxonomy-product_attribute"],m="woocommerce-blocks-revert-button-templates";let g;(0,t.subscribe)((()=>{const o=g,r=(0,t.select)("core/edit-site");if(!(()=>{const o=(0,t.select)(e.store)?.getCurrentPostType();return"wp_template"===o||"wp_template_part"===o})())return;if(g=r?.getEditedPostId(),o===g)return;const n=w.some((e=>g?.includes(e))),c=void 0!==e.PluginDocumentSettingPanel;if(n&&c){if((0,d.getPlugin)(m))return;return(0,d.registerPlugin)(m,{render:p})}void 0!==(0,d.getPlugin)(m)&&(0,d.unregisterPlugin)(m)}),"core/edit-site")})()})();