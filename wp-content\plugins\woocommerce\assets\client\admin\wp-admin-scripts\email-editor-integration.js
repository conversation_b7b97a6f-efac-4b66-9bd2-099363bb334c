/*! For license information please see email-editor-integration.js.LICENSE.txt */
(()=>{"use strict";var e={32915:(e,t,o)=>{o.d(t,{initializeEditor:()=>jn});var n={};o.r(n),o.d(n,{requestSendingNewsletterPreview:()=>uo,setContentValidation:()=>ho,setEmailPost:()=>mo,setIsFetchingPersonalizationTags:()=>_o,setPersonalizationTagsList:()=>go,setTemplateToPost:()=>po,togglePreviewModal:()=>lo,updateSendPreviewEmail:()=>co});var r={};o.r(r),o.d(r,{canUserEditGlobalEmailStyles:()=>Fo,canUserEditTemplates:()=>No,getBlockPatternsForEmailTemplate:()=>Po,getContentValidation:()=>Jo,getCurrentTemplate:()=>Io,getCurrentTemplateContent:()=>Ho,getEditedEmailContent:()=>Eo,getEditedPostTemplate:()=>Mo,getEmailPostId:()=>Ao,getEmailPostType:()=>Lo,getEmailTemplates:()=>Ro,getGlobalEmailStylesPost:()=>zo,getGlobalStylesPostId:()=>qo,getInitialEditorSettings:()=>Oo,getPaletteColors:()=>Vo,getPersonalizationTagsList:()=>$o,getPersonalizationTagsState:()=>Go,getPreviewState:()=>Do,getSentEmailEditorPosts:()=>To,getStyles:()=>Wo,getTheme:()=>Uo,getUrls:()=>Zo,hasEdits:()=>ko,hasEmptyContent:()=>So,isEmailSent:()=>Co,isFeatureActive:()=>jo});var s={};o.r(s),o.d(s,{getPersonalizationTagsList:()=>Yo});var i=o(39793),a=o(47143),l=o(86087),c=o(52619);window.wp.formatLibrary;const d=window.wp.blocks;function m(){try{return(0,d.getBlockTypes)().filter((e=>!0===e.supports?.email)).map((e=>e.name))}catch(e){return console.error("Failed to get allowed block names:",e),[]}}const p=window.wp.blockLibrary,u=window.wp.blockEditor,_=window.wp.compose,g=(0,_.createHigherOrderComponent)((e=>function(t){return"core/columns"!==t.name?(0,i.jsx)(e,{...t}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e,{...t}),(0,i.jsx)(u.InspectorControls,{children:(0,i.jsx)("style",{children:"\n      .components-panel__body .components-toggle-control .components-form-toggle { opacity: 0.3; }\n      .components-panel__body .components-toggle-control .components-form-toggle__input { pointer-events: none; }\n      .components-panel__body .components-toggle-control label { pointer-events: none; }\n    "})})]})}),"columnsEditCallback"),h=["core/column","core/columns"];var y=o(27723);function w({layoutClassNames:e}){const t=(0,u.useBlockProps)({className:e});return(0,i.jsxs)("div",{...t,children:[(0,i.jsx)("p",{children:(0,y.__)("This is the Content block.","woocommerce")}),(0,i.jsx)("p",{children:(0,y.__)("It will display all the blocks in the email content, which might be only simple text paragraphs. You can enrich your message with images, incorporate data through tables, explore different layout designs with columns, or use any other block type.","woocommerce")})]})}const x=(0,_.createHigherOrderComponent)((e=>function(t){return"core/image"!==t.name?(0,i.jsx)(e,{...t}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e,{...t}),(0,i.jsx)(u.InspectorControls,{children:(0,i.jsx)("style",{children:"\n        .components-tools-panel .components-toggle-control { display: none; }\n      "})})]})}),"imageEditCallback"),f=window.wp.richText;var b=o(56427);const v=(e,t)=>{const o=e.current.ownerDocument.defaultView.getSelection();if(!o.rangeCount)return{start:0,end:0};const n=o.getRangeAt(0);if(null===o.anchorNode.previousSibling)return{start:o.anchorOffset,end:o.anchorOffset+n.toString().length};const r=(0,f.create)({html:t});let s=o.anchorNode.previousSibling;s=function(e){let t=e;for(;t&&t?.children?.length>0;)t=t.children[0];return t}(s);const i=function(e,t){let o=null;for(const[n,r]of t.entries())if(r)for(const t of r)t?.attributes&&e.tagName.toLowerCase()===t.tagName?.toLowerCase()&&e.getAttribute("data-link-href")===t?.attributes["data-link-href"]&&(o=n);return o}(s,r.formats);if(null!==i)return{start:i+o.anchorOffset+1,end:i+o.anchorOffset+n.toString().length};const a=function(e,t){for(const[o,n]of t.entries()){if(!n)continue;const{attributes:t}=n;if(e.getAttribute("data-rich-text-comment")===t["data-rich-text-comment"])return o}return null}(s,r.replacements);return null!==a?{start:a+o.anchorOffset+1,end:a+o.anchorOffset+n.toString().length}:{start:r.text.length,end:r.text.length+n.toString().length}},j=(e,t)=>(t.forEach((t=>{if(!e.includes(t.token.slice(0,t.token.length-1)))return;const o=t.token.substring(1,t.token.length-1).replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),n=new RegExp(`(?<!\x3c!--)(?<!["'])\\[(${o}(\\s[^\\]]*)?)\\](?!--\x3e)`,"g");e=e.replace(n,(e=>`\x3c!--${e}--\x3e`))})),e),k=({groupedTags:e,activeCategory:t,onCategorySelect:o})=>{const n=e=>e===t?"woocommerce-personalization-tags-modal-menu-item-active":"";return(0,i.jsxs)(b.MenuGroup,{className:"woocommerce-personalization-tags-modal-menu",children:[(0,i.jsx)(b.MenuItem,{onClick:()=>o(null),className:n(null),children:(0,y.__)("All","woocommerce")}),(0,i.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true",role:"presentation","data-testid":"woocommerce-personalization-tags-modal-menu-separator"}),Object.keys(e).map(((e,t,r)=>(0,i.jsxs)(l.Fragment,{children:[(0,i.jsx)(b.MenuItem,{onClick:()=>o(e),className:n(e),children:e}),t<r.length-1&&(0,i.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true",role:"presentation","data-testid":"woocommerce-personalization-tags-modal-menu-separator"})]},e)))]})},S=({groupedTags:e,activeCategory:t,onInsert:o,canInsertLink:n,closeCallback:r,openLinkModal:s})=>{const{updateBlockAttributes:l}=(0,a.useDispatch)(u.store),c=(0,a.useSelect)((e=>e(u.store).getSelectedBlockClientId())),d=(0,a.useSelect)((e=>e(u.store).getBlock(c))),m=["core/button"].includes(d?.name),p=null===t?Object.entries(e):[[t,e[t]||[]]];return(0,i.jsx)(i.Fragment,{children:p.map((([e,t])=>(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"woocommerce-personalization-tags-modal-category",children:e}),(0,i.jsx)("div",{className:"woocommerce-personalization-tags-modal-category-group",children:t.map((t=>{const a=/\burl\b/.test(t.token);return(0,i.jsxs)("div",{className:"woocommerce-personalization-tags-modal-category-group-item",children:[(0,i.jsxs)("div",{className:"woocommerce-personalization-tags-modal-item-text",children:[(0,i.jsx)("strong",{children:t.name}),t.valueToInsert]}),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-end"},children:[(0,i.jsx)(b.Button,{variant:"link",onClick:()=>{o&&o(t.valueToInsert,!1)},children:(0,y.__)("Insert","woocommerce")}),m&&a&&(0,i.jsx)(b.Button,{variant:"link",onClick:()=>{l(c,{url:t.valueToInsert}),r()},children:(0,y.__)("Set as URL","woocommerce")}),e===(0,y.__)("Link","woocommerce")&&n&&(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(b.Button,{variant:"link",onClick:()=>{r(),s(t)},children:(0,y.__)("Insert as link","woocommerce")})})]})]},t.token)}))})]},e)))})},C=({onInsert:e,isOpened:t,closeCallback:o,tag:n})=>{const[r,s]=(0,l.useState)((0,y.__)("Link","woocommerce"));return t?(0,i.jsxs)(b.Modal,{size:"small",title:(0,y.__)("Insert Link","woocommerce"),onRequestClose:o,className:"woocommerce-personalization-tags-modal",children:[(0,i.jsx)(b.TextControl,{label:(0,y.__)("Link Text","woocommerce"),value:r,onChange:s}),(0,i.jsx)(b.Button,{isPrimary:!0,onClick:()=>{e&&e(n.token,r)},children:(0,y.__)("Insert","woocommerce")})]}):null},E=window.lodash,T=()=>(0,c.applyFilters)("woocommerce_email_editor_events_tracking_enabled",!1),P="email_editor_events",N=new EventTarget,B=(e,t={})=>{if(!T())return;const o={name:`${P}_${e}`,..."object"!=typeof t?{data:t}:t};N.dispatchEvent(new CustomEvent(P,{detail:o}))},M=function(){const e={};return(t,o={})=>{if(!T())return;const n=`${t}_${JSON.stringify(o).length}`;e[n]||(B(t,o),e[n]=!0)}}(),I=(0,E.debounce)(B,700);var H=o(58039);const F=({onInsert:e,isOpened:t,closeCallback:o,canInsertLink:n=!1,openedBy:r=""})=>{const[s,c]=(0,l.useState)(null),[d,m]=(0,l.useState)(""),[p,u]=(0,l.useState)(null),[_,g]=(0,l.useState)(!1),h=(0,a.useSelect)((e=>e(H.H).getPersonalizationTagsList()),[]);if(_)return(0,i.jsx)(C,{onInsert:(t,o)=>{e(t,o),g(!1)},isOpened:_,closeCallback:()=>g(!1),tag:p});if(!t)return null;M("personalization_tags_modal_opened",{openedBy:r});const w=h.reduce(((e,t)=>{const{category:o,name:n,token:r}=t;return(!d||n.toLowerCase().includes(d.toLowerCase())||r.toLowerCase().includes(d.toLowerCase()))&&(e[o]||(e[o]=[]),e[o].push(t)),e}),{});return(0,i.jsxs)(b.Modal,{size:"medium",title:(0,y.__)("Personalization Tags","woocommerce"),onRequestClose:()=>{o(),B("personalization_tags_modal_closed",{openedBy:r})},className:"woocommerce-personalization-tags-modal",children:[(0,i.jsxs)("p",{children:[(0,y.__)("Insert personalization tags to dynamically fill in information and personalize your emails.","woocommerce")," ",(0,i.jsx)(b.ExternalLink,{href:"https://kb.mailpoet.com/article/435-a-guide-to-personalisation-tags-for-tailored-newsletters#list",onClick:()=>B("personalization_tags_modal_learn_more_link_clicked",{openedBy:r}),children:(0,y.__)("Learn more","woocommerce")})]}),(0,i.jsx)(b.SearchControl,{onChange:e=>{m(e),M("personalization_tags_modal_search_control_input_updated",{openedBy:r})},value:d}),(0,i.jsx)(k,{groupedTags:w,activeCategory:s,onCategorySelect:e=>{c(e),B("personalization_tags_modal_category_menu_clicked",{category:e,openedBy:r})}}),(0,i.jsx)(S,{groupedTags:w,activeCategory:s,onInsert:t=>{e(t),B("personalization_tags_modal_tag_insert_button_clicked",{insertedTag:t,activeCategory:s,openedBy:r})},closeCallback:o,canInsertLink:n,openLinkModal:e=>{u(e),g(!0)}})]})},z=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,l.useState)(!1),[r,s]=(0,l.useState)(null),[a,c]=(0,l.useState)(""),[d,m]=(0,l.useState)("");return(0,l.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("span[data-rich-text-comment]");if(t){const e=t.innerText.replace(/^\[|\]$/g,"");m(e),c(e),s(t),n(!0)}};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,i.jsx)(i.Fragment,{children:o&&r&&(0,i.jsx)(b.Popover,{position:"bottom right",onClose:()=>n(!1),anchor:r,className:"woocommerce-personalization-tag-popover",children:(0,i.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,i.jsx)(b.TextControl,{label:(0,y.__)("Personalization Tag","woocommerce"),value:a,onChange:e=>c(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),(0,i.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,i.jsx)(b.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,y.__)("Cancel","woocommerce")}),(0,i.jsx)(b.Button,{isPrimary:!0,onClick:()=>{t(d,a),n(!1)},children:(0,y.__)("Update","woocommerce")})]})]})})})},R=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,l.useState)(!1),[r,s]=(0,l.useState)(null),[c,d]=(0,l.useState)(""),[m,p]=(0,l.useState)(""),u=(0,a.useSelect)((e=>e(H.H).getPersonalizationTagsList()),[]);return(0,l.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("a[data-link-href]");t&&(s(t),p(t.getAttribute("data-link-href")||""),d(t.textContent||""),n(!0))};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,i.jsx)(i.Fragment,{children:o&&r&&(0,i.jsx)(b.Popover,{position:"bottom left",onClose:()=>n(!1),anchor:r,className:"woocommerce-personalization-tag-popover",children:(0,i.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,i.jsx)(b.TextControl,{label:(0,y.__)("Link Text","woocommerce"),value:c,onChange:e=>d(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,autoComplete:"off"}),(0,i.jsx)(b.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,y.__)("Link tag","woocommerce"),value:m,onChange:e=>{p(e)},options:u.filter((e=>e.category===(0,y.__)("Link","woocommerce"))).map((e=>({label:e.name,value:e.token})))}),(0,i.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,i.jsx)(b.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,y.__)("Cancel","woocommerce")}),(0,i.jsx)(b.Button,{isPrimary:!0,onClick:()=>{n(!1),t(r,m,c)},children:(0,y.__)("Update link","woocommerce")})]})]})})})},A=window.wp.editor;function L({contentRef:e}){const[t,o]=(0,l.useState)(!1),n=(0,a.useSelect)((e=>e("core/block-editor").getSelectedBlockClientId())),{updateBlockAttributes:r}=(0,a.useDispatch)("core/block-editor"),s=(0,a.useSelect)((e=>e("core/block-editor").getBlockAttributes(n))),c="text"in s?"text":"content",d=s?.[c]?.originalHTML||s?.[c]||"",m=(0,l.useCallback)(((t,o)=>{let{start:s,end:i}=v(e,d),a="";if(o){let e=(0,f.create)({html:d});e=(0,f.insert)(e,o,s,i),i=s+o.length,e=(0,f.applyFormat)(e,{type:"woocommerce-email-editor/link-shortcode",attributes:{"data-link-href":t,contenteditable:"false",style:"text-decoration: underline;"}},s,i),a=(0,f.toHTMLString)({value:e})}else{let e=(0,f.create)({html:d});e=(0,f.insert)(e,(0,f.create)({html:`\x3c!--${t}--\x3e&nbsp;`}),s,i),a=(0,f.toHTMLString)({value:e})}r(n,{[c]:a})}),[d,c,e,n,r]);return(0,a.useSelect)((e=>{const t=e(H.H);if(!t)return!1;const o=t.getEmailPostId(),n=t.getEmailPostType(),r=e(A.store).getCurrentPostId(),s=e(A.store).getCurrentPostType();if(String(r)===String(o)&&String(s)===String(n))return!0;if("wp_template"===s){const e=t.getCurrentTemplate();if(!e)return!1;const o=t.getEmailTemplates();return!!o&&o.some((t=>t.id===e.id&&t.post_types?.includes(n)))}return!1}),[])?(0,i.jsx)(u.BlockControls,{children:(0,i.jsxs)(b.ToolbarGroup,{children:[(0,i.jsx)(b.ToolbarButton,{icon:"shortcode",title:(0,y.__)("Personalization Tags","woocommerce"),onClick:()=>{o(!0),B("block_controls_personalization_tags_button_clicked")}}),(0,i.jsx)(z,{contentRef:e,onUpdate:(e,t)=>{const o=d.replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);r(n,{[c]:o})}}),(0,i.jsx)(R,{contentRef:e,onUpdate:(e,t,o)=>{const s=e.getAttribute("data-link-href").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),i=new RegExp(`<a([^>]*?)data-link-href="${s}"([^>]*?)>${e.textContent}</a>`,"gi"),a=d.replace(i,((e,n,r)=>`<a${n}data-link-href="${t}"${r}>${o}</a>`));r(n,{content:a})}}),(0,i.jsx)(F,{isOpened:t,onInsert:(e,t)=>{m(e,t),o(!1)},closeCallback:()=>o(!1),canInsertLink:!0,openedBy:"block-controls"})]})}):null}const O=(0,_.createHigherOrderComponent)((e=>t=>{const{attributes:o,setAttributes:n,name:r}=t,{content:s}=o,c=(0,a.useSelect)((e=>e(H.H).getPersonalizationTagsList()),[]),d=(0,l.useCallback)((()=>s?j(s,c):""),[s,c]),m=(0,l.useCallback)((e=>{if(void 0!==e.content){const t=j(e.content,c);n({...e,content:t})}else n(e)}),[c,n]);return"core/paragraph"===r||"core/heading"===r||"core/list-item"===r?(0,i.jsx)(e,{...t,attributes:{...o,content:d()},setAttributes:m}):(0,i.jsx)(e,{...t})}),"personalizationTagsLiveContentUpdate"),V=window.wp.domReady;var D=o.n(V);const G=["core/social-links"];function $(){(0,d.getBlockTypes)().forEach((e=>{const t=e.name;if(G.includes(t))return;const o=(0,a.select)(d.store).getBlockStyles(t);Array.isArray(o)&&0!==o?.length&&o.forEach((e=>{(0,d.unregisterBlockStyle)(t,e.name)}))}))}const W=e=>t=>{const{setAttributes:o}=t,n=(0,l.useCallback)((e=>{e?.url&&e.url?.startsWith("http://[")&&(e.url=e.url.replace("http://[","[")),o(e)}),[o]);return(0,i.jsx)(e,{...t,setAttributes:n})},U=["behance","bluesky","chain","discord","facebook","feed","github","gravatar","instagram","linkedin","mail","mastodon","medium","patreon","pinterest","reddit","spotify","telegram","threads","tiktok","tumblr","twitch","twitter","vimeo","wordpress","whatsapp","x","youtube"],q=e=>t=>{if("core/social-links"!==t.name)return(0,i.jsx)(e,{...t});const o=`\n\t\t.block-editor-tools-panel-color-gradient-settings__item:has([title="${(0,y.__)("Icon color")}"]) {\n\t\t\tdisplay: none !important;\n\t\t}\n\t\t.block-editor-tools-panel-color-gradient-settings__item:nth-child(2 of .block-editor-tools-panel-color-gradient-settings__item){\n\t\t\tborder-top:1px solid #ddd;\n\t\t\tborder-top-left-radius:2px;\n\t\t\tborder-top-right-radius:2px;\n\t\t}\n\t\t`;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e,{...t}),(0,i.jsx)(u.InspectorControls,{group:"color",children:(0,i.jsx)("style",{children:o})})]})};const Z=window.wp.privateApis,{unlock:J}=(0,Z.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-site"),{ColorPanel:Y}=J(u.privateApis),{useGlobalStylesOutputWithConfig:K}=J(u.privateApis),{Editor:X,FullscreenMode:Q,ViewMoreMenuGroup:ee,BackButton:te}=J(A.privateApis),{registerEntityAction:oe,unregisterEntityAction:ne}=J((0,a.dispatch)(A.store));var re=o(3582);const se=window.wp.notices;var ie=o(5573);const ae=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})}),le=window.wp.htmlEntities;function ce(e){return"string"==typeof e.title?(0,le.decodeEntities)(e.title):e.title&&"rendered"in e.title?(0,le.decodeEntities)(e.title.rendered):e.title&&"raw"in e.title?(0,le.decodeEntities)(e.title.raw):""}function de(e,t){return t?e.length>1?(0,y.sprintf)((0,y._n)("Are you sure you want to permanently delete %d item?","Are you sure you want to permanently delete %d items?",e.length,"woocommerce"),e.length):(0,y.sprintf)((0,y.__)('Are you sure you want to permanently delete "%s"?',"woocommerce"),(0,le.decodeEntities)(ce(e[0]))):e.length>1?(0,y.sprintf)((0,y._n)("Are you sure you want to move %d item to the trash ?","Are you sure you want to move %d items to the trash ?",e.length,"woocommerce"),e.length):(0,y.sprintf)((0,y.__)('Are you sure you want to move "%s" to the trash?',"woocommerce"),ce(e[0]))}const me=e=>{ne("postType",e,"move-to-trash"),oe("postType",e,(()=>{const e=(0,c.applyFilters)("woocommerce_email_editor_trash_modal_should_permanently_delete",!1);return{id:"trash-email-post",label:e?(0,y.__)("Permanently delete","woocommerce"):(0,y.__)("Move to trash","woocommerce"),supportsBulk:!0,icon:ae,isEligible(e){if("wp_template"===e.type||"wp_template_part"===e.type||"wp_block"===e.type)return!1;const{permissions:t}=e;return t?.delete},hideModalHeader:!0,modalFocusOnMount:"firstContentElement",RenderModal:({items:t,closeModal:o,onActionPerformed:n})=>{const[r,s]=(0,l.useState)(!1),{createSuccessNotice:c,createErrorNotice:d}=(0,a.useDispatch)(se.store),{deleteEntityRecord:m}=(0,a.useDispatch)(re.store),{urls:p}=(0,a.useSelect)((e=>({urls:e(H.H).getUrls()})),[]);return(0,i.jsxs)(b.__experimentalVStack,{spacing:"5",children:[(0,i.jsx)(b.__experimentalText,{children:de(t,e)}),(0,i.jsxs)(b.__experimentalHStack,{justify:"right",children:[(0,i.jsx)(b.Button,{variant:"tertiary",onClick:()=>{o?.(),B("trash_modal_cancel_button_clicked")},disabled:r,__next40pxDefaultSize:!0,children:(0,y.__)("Cancel","woocommerce")}),(0,i.jsx)(b.Button,{variant:"primary",onClick:async()=>{B("trash_modal_move_to_trash_button_clicked"),s(!0);const r=await Promise.allSettled(t.map((t=>m("postType",t.type,t.id,{force:e},{throwOnError:!0}))));if(r.every((({status:e})=>"fulfilled"===e))){let o;o=1===r.length?e?(0,y.sprintf)((0,y.__)('"%s" permanently deleted.',"woocommerce"),ce(t[0])):(0,y.sprintf)((0,y.__)('"%s" moved to the trash.',"woocommerce"),ce(t[0])):e?(0,y.__)("The items were permanently deleted.","woocommerce"):(0,y.sprintf)((0,y._n)("%s item moved to the trash.","%s items moved to the trash.",t.length,"woocommerce"),t.length),c(o,{type:"snackbar",id:"trash-email-post-action"}),n?.(t),p?.listings&&(window.location.href=p.listings)}else{let e;if(1===r.length){const t=r[0];e=t.reason?.message?t.reason.message:(0,y.__)("An error occurred while performing the action.","woocommerce")}else{const t=new Set,o=r.filter((({status:e})=>"rejected"===e));for(const e of o){const o=e;o.reason?.message&&t.add(o.reason.message)}e=0===t.size?(0,y.__)("An error occurred while performing the action.","woocommerce"):1===t.size?(0,y.sprintf)((0,y.__)("An error occurred while performing the action: %s","woocommerce"),[...t][0]):(0,y.sprintf)((0,y.__)("Some errors occurred while performing the action: %s","woocommerce"),[...t].join(","))}B("trash_modal_move_to_trash_error",{errorMessage:e}),d(e,{type:"snackbar"})}s(!1),o?.()},isBusy:r,disabled:r,__next40pxDefaultSize:!0,children:e?(0,y.__)("Delete permanently","woocommerce"):(0,y.__)("Move to trash","woocommerce")})]})]})}}})())};function pe(){(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/filter-set-url-attribute",W),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/deactivate-stack-on-mobile",g),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/hide-expand-on-click",x),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/deactivate-image-filter",((e,t)=>"core/image"===t?{...e,supports:{...e.supports,filter:{duetone:!1}}}:e)),(0,f.unregisterFormatType)("core/image"),(0,f.unregisterFormatType)("core/code"),(0,f.unregisterFormatType)("core/language"),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-columns-layout",((e,t)=>h.includes(t)?{...e,supports:{...e.supports,layout:!1,background:{backgroundImage:!0}}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-group-variations",((e,t)=>"core/group"===t?{...e,variations:e.variations.filter((e=>"group"===e.name)),supports:{...e.supports,layout:!1}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-buttons",((e,t)=>"core/buttons"===t?{...e,supports:{...e.supports,layout:!1,__experimentalEmailFlexLayout:!0}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-post-content",((e,t)=>{return"core/post-content"===t?{...e,edit:(o=e.edit,function(e){const{postId:t,postType:n}=e.context,{__unstableLayoutClassNames:r}=e;return t&&n?(0,i.jsx)(o,{...e}):(0,i.jsx)(w,{layoutClassNames:r})})}:e;var o})),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-quote",((e,t)=>"core/quote"===t?{...e,styles:[],supports:{...e.supports,align:[]}}:e)),(0,f.registerFormatType)("woocommerce-email-editor/shortcode",{name:"woocommerce-email-editor/shortcode",title:(0,y.__)("Personalization Tags","woocommerce"),className:"woocommerce-email-editor-personalization-tags",tagName:"span",attributes:{},edit:L}),(0,f.registerFormatType)("woocommerce-email-editor/link-shortcode",{name:"woocommerce-email-editor/link-shortcode",title:(0,y.__)("Personalization Tags Link","woocommerce"),className:"woocommerce-email-editor-personalization-tags-link",tagName:"a",attributes:{"data-link-href":"data-link-href",contenteditable:"contenteditable",style:"style"},edit:null}),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-live-content-update",O),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/block-support",(e=>e.supports?.shadow?{...e,supports:{...e.supports,shadow:!1}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-social-link-variations",((e,t)=>"core/social-link"===t?{...e,variations:e.variations.filter((e=>U.includes(e.name))),supports:{...e.supports,layout:!1}}:e)),(0,d.registerBlockVariation)("core/social-links",{name:"social-links-default",title:"Social Icons",attributes:{openInNewTab:!0,showLabels:!1,align:"center",className:"is-style-logos-only"},isDefault:!0,innerBlocks:[{name:"core/social-link",attributes:{service:"wordpress",url:"https://wordpress.org"}},{name:"core/social-link",attributes:{service:"facebook",url:"https://www.facebook.com/WordPress/"}},{name:"core/social-link",attributes:{service:"x",url:"https://x.com/WordPress"}}]}),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/disable-social-links-icon-color",q),(0,c.addAction)("core.registerPostTypeSchema","woocommerce-email-editor/modify-move-to-trash-action",(e=>{me(e)})),(0,c.addAction)("core.registerPostTypeActions","woocommerce-email-editor/modify-move-to-trash-action",(e=>{me(e)})),(0,d.registerBlockVariation)("core/site-logo",{name:"site-logo-default",title:"Site Logo",attributes:{align:"center",width:120},isDefault:!0}),(0,p.registerCoreBlocks)(),D()($)}var ue=o(4921);const _e=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M9 9v6h11V9H9zM4 20h1.5V4H4v16z"})}),ge=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M12.5 15v5H11v-5H4V9h7V4h1.5v5h7v6h-7Z"})}),he=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M4 15h11V9H4v6zM18.5 4v16H20V4h-1.5z"})}),ye="__experimentalEmailFlexLayout";function we(e){return(0,d.hasBlockSupport)(e,ye)}function xe({justificationValue:e,onChange:t,isToolbar:o=!1}){const n=[{value:"left",icon:_e,label:(0,y.__)("Justify items left","woocommerce")},{value:"center",icon:ge,label:(0,y.__)("Justify items center","woocommerce")},{value:"right",icon:he,label:(0,y.__)("Justify items right","woocommerce")}];if(o){const o=n.map((e=>e.value));return(0,i.jsx)(u.JustifyContentControl,{value:e,onChange:t,allowedControls:o,popoverProps:{placement:"bottom-start"}})}return(0,i.jsx)(b.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,label:(0,y.__)("Justification","woocommerce"),value:e,onChange:t,className:"block-editor-hooks__flex-layout-justification-controls",children:n.map((({value:e,icon:t,label:o})=>(0,i.jsx)(b.__experimentalToggleGroupControlOptionIcon,{value:e,icon:t,label:o},e)))})}function fe({setAttributes:e,attributes:t,name:o}){if(!(0,d.getBlockSupport)(o,ye,{}))return null;const{justifyContent:n="left"}=t.layout||{},r=o=>{e({layout:{...t.layout,justifyContent:o}})},s=()=>{const{justifyContent:o,...n}=t.layout||{};e({layout:n})};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(u.InspectorControls,{children:(0,i.jsx)(b.__experimentalToolsPanel,{label:(0,y.__)("Layout","woocommerce"),resetAll:s,children:(0,i.jsx)(b.__experimentalToolsPanelItem,{isShownByDefault:!0,onDeselect:s,hasValue:()=>t.layout?.justifyContent||!1,label:(0,y.__)("Justification","woocommerce"),children:(0,i.jsx)(b.Flex,{children:(0,i.jsx)(b.FlexItem,{children:(0,i.jsx)(xe,{justificationValue:n,onChange:r})})})})})}),(0,i.jsx)(u.BlockControls,{group:"block",__experimentalShareWithChildBlocks:!0,children:(0,i.jsx)(xe,{justificationValue:n,onChange:r,isToolbar:!0})})]})}function be(e){return we(e.name)?{...e,attributes:{...e.attributes,layout:{type:"object"}}}:e}const ve=(0,_.createHigherOrderComponent)((e=>t=>[we(t.name)&&(0,i.jsx)(fe,{...t},"layout"),(0,i.jsx)(e,{...t},"edit")]),"withLayoutControls");function je({block:e,props:t}){const{attributes:o}=t,{layout:n}=o,r=`is-content-justification-${n?.justifyContent||"left"}`,s=(0,ue.A)(r,"is-layout-email-flex is-layout-flex");return(0,i.jsx)(e,{...t,className:s})}const ke=(0,_.createHigherOrderComponent)((e=>function(t){return we(t.name)?(0,i.jsx)(je,{block:e,props:t}):(0,i.jsx)(e,{...t})}),"withLayoutStyles"),Se=window.wp.commands;var Ce=o(76597),Ee=o.n(Ce);function Te(){const{globalStylePost:e}=(0,a.useSelect)((e=>({globalStylePost:e(H.H).getGlobalEmailStylesPost()||null})),[]),t=(0,l.useCallback)((t=>{e&&(0,a.dispatch)(re.store).editEntityRecord("postType","wp_global_styles",e.id,{styles:t.styles,settings:t.settings})}),[e]);return{userTheme:{settings:e?.settings,styles:e?.styles},updateUserTheme:t}}function Pe(e){if("string"!=typeof e)return null;const t=e.match(/^var:preset\|([a-zA-Z0-9-]+)\|([a-zA-Z0-9-]+)$/);return t?`--wp--preset--${t[1]}--${t[2]}`:null}function Ne(e){const t=Pe(e);return t?`var(${t})`:e}function Be(e){const t=Pe(e);if(!t)return e;const o=document.querySelector(":root");return o&&getComputedStyle(o).getPropertyValue(t).trim()||e}const Me=[];function Ie(){const{userTheme:e}=Te(),{editorTheme:t,layout:o,deviceType:n,editorSettingsStyles:r}=(0,a.useSelect)((e=>{const{getEditorSettings:t,getDeviceType:o}=e(A.store),n=t();return{editorTheme:e(H.H).getTheme(),layout:n.__experimentalFeatures?.layout,deviceType:o(),editorSettingsStyles:n.styles}}),[]),s=(0,l.useMemo)((()=>Ee().all([{},t||{},e||{}])),[t,e]),[i]=K(s);let c="";o&&"Mobile"!==n&&(c=`display:flow-root; width:${o?.contentSize}; margin: 0 auto;box-sizing: border-box;`);const d=s.styles?.spacing?.padding;return d&&(c+=`padding-left:${Ne(d.left)};`,c+=`padding-right:${Ne(d.right)};`),[(0,l.useMemo)((()=>[...null!=i?i:[],{css:`.is-root-container{ ${c} }`},...null!=r?r:[]]),[i,r,c])||Me]}const He=[];function Fe(e,t){return e.map((e=>"core/post-content"===e.name?{...e,name:"core/group",innerBlocks:t}:e.innerBlocks?.length?{...e,innerBlocks:Fe(e.innerBlocks,t)}:e))}const ze={};function Re(e=""){const{templates:t,patterns:o,emailPosts:n,hasEmailPosts:r}=(0,a.useSelect)((t=>{const o="swap"!==e?t(H.H).getSentEmailEditorPosts():void 0;return{templates:t(H.H).getEmailTemplates(),patterns:t(H.H).getBlockPatternsForEmailTemplate(),emailPosts:o,hasEmailPosts:!(!o||!o?.length)}}),[e]),s=(0,l.useMemo)((()=>{let n=[];const r=e&&(0,d.parse)(e);if(n=r?[{blocks:r}]:o,!n||!t)return He;const s=[];return t?.filter((e=>"email-general"!==e.slug))?.forEach((e=>{n?.forEach((t=>{let o=(0,d.parse)(e.content?.raw);o=Fe(o,t.blocks),s.push({id:e.id,slug:e.slug,previewContentParsed:o,emailParsed:t.blocks,template:e,category:"basic",type:e.type,displayName:t.title?`${e.title.rendered} - ${t.title}`:e.title.rendered})}))})),s}),[t,o,e]),i=(0,l.useMemo)((()=>n?.map((e=>{const t=(0,c.applyFilters)("woocommerce_email_editor_preferred_template_title","",e),{postTemplateContent:o}=function(e,t=[]){const o=e.template,n={postTemplateContent:null};if(!o)return n;if(ze[o])return ze[o];const r=t.find((e=>e.slug===o));if(!r)return n;const s={postTemplateContent:r?.template};return ze[o]=s,s}(e,s),n=(0,d.parse)(e.content?.raw);let r=n;o?.content?.raw&&(r=Fe((0,d.parse)(o?.content?.raw),n));const i={...e,title:{raw:e.title.raw,rendered:t||e.title.rendered}};return{id:e.id,slug:e.slug,previewContentParsed:r,emailParsed:n,category:"recent",type:e.type,displayName:i.title.rendered,template:i}}))),[n,s]);return[s||He,i||He,r]}const Ae=(0,l.forwardRef)((function({icon:e,size:t=24,...o},n){return(0,l.cloneElement)(e,{width:t,height:t,...o,ref:n})})),Le=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})}),Oe=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})}),Ve=(0,window.wp.priorityQueue.createQueue)();function De({children:e,placeholder:t}){const[o,n]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{const e={};return Ve.add(e,(()=>{(0,l.flushSync)((()=>{n(!0)}))})),()=>{Ve.cancel(e)}}),[]),o?e:t}function Ge(){return(0,i.jsxs)("div",{className:"block-editor-inserter__no-results",children:[(0,i.jsx)(Ae,{className:"block-editor-inserter__no-results-icon",icon:Le}),(0,i.jsx)("p",{children:(0,y.__)("No recent templates.","woocommerce")}),(0,i.jsx)("p",{children:(0,y.__)("Your recent creations will appear here as soon as you begin.","woocommerce")})]})}const $e=(0,l.memo)((function({templates:e,onTemplateSelection:t,selectedCategory:o}){const{layout:n}=(0,a.useSelect)((e=>{const{getEditorSettings:t}=e(A.store);return{layout:t().__experimentalFeatures.layout}})),[r]=Ie(),s=r.reduce(((e,t)=>{var o;return e+(null!==(o=t.css)&&void 0!==o?o:"")}),"")+`.is-root-container { width: ${n.contentSize}; margin: 0 auto; }`;return"recent"===o&&0===e.length?(0,i.jsx)(Ge,{}):(0,i.jsx)("div",{className:"block-editor-block-patterns-list",role:"listbox",children:e.map((e=>(0,i.jsx)("div",{className:"block-editor-block-patterns-list__list-item email-editor-pattern__list-item",children:(0,i.jsx)("div",{className:"block-editor-block-patterns-list__item",role:"button",tabIndex:0,onClick:()=>{t(e)},onKeyPress:o=>{"Enter"!==o.key&&" "!==o.key||t(e)},children:(0,i.jsxs)(De,{placeholder:(0,i.jsx)("p",{children:(0,y.__)("rendering template","woocommerce")}),children:[(0,i.jsx)(u.BlockPreview,{blocks:e.previewContentParsed,viewportWidth:900,minHeight:300,additionalStyles:[{css:s}]}),(0,i.jsx)(b.__experimentalHStack,{className:"block-editor-patterns__pattern-details",children:(0,i.jsx)("h4",{className:"block-editor-block-patterns-list__item-title",children:e.displayName})})]})})},`${e.slug}_${e.displayName}_${e.id}`)))})}),((e,t)=>e.templates.length===t.templates.length&&e.selectedCategory===t.selectedCategory));function We({templates:e,onTemplateSelection:t,selectedCategory:o}){const n=(0,l.useMemo)((()=>e.filter((e=>e.category===o))),[o,e]);return(0,i.jsxs)("div",{className:"block-editor-block-patterns-explorer__list",children:["recent"===o&&(0,i.jsx)("div",{className:"email-editor-recent-templates-info",children:(0,i.jsxs)(b.__experimentalHStack,{spacing:1,expanded:!1,justify:"start",children:[(0,i.jsx)(Ae,{icon:Oe}),(0,i.jsx)("p",{children:(0,y.__)("Templates created on the legacy editor will not appear here.","woocommerce")})]})}),(0,i.jsx)($e,{templates:n,onTemplateSelection:t,selectedCategory:o})]})}function Ue({selectedCategory:e,templateCategories:t,onClickCategory:o}){const n="block-editor-block-patterns-explorer__sidebar";return(0,i.jsx)("div",{className:n,children:(0,i.jsx)("div",{className:`${n}__categories-list`,children:t.map((({name:t,label:r})=>(0,i.jsx)(b.Button,{label:r,className:`${n}__categories-list__item`,isPressed:e===t,onClick:()=>{o(t)},children:r},t)))})})}const qe=[{name:"recent",label:"Recent"},{name:"basic",label:"Basic"}],Ze=(0,l.memo)((function({hasEmailPosts:e,templates:t,handleTemplateSelection:o,templateSelectMode:n}){const[r,s]=(0,l.useState)(qe[1].name),a="swap"===n,c=qe.filter((({name:e})=>"recent"!==e||!a));return(0,l.useEffect)((()=>{setTimeout((()=>{e&&!a&&s(qe[0].name)}),1e3)}),[e,a]),(0,i.jsxs)("div",{className:"block-editor-block-patterns-explorer",children:[(0,i.jsx)(Ue,{templateCategories:c,selectedCategory:r,onClickCategory:e=>{B("template_select_modal_category_change",{category:e}),s(e)}}),(0,i.jsx)(We,{templates:t,onTemplateSelection:o,selectedCategory:r})]})}));function Je({onSelectCallback:e,closeCallback:t=null,previewContent:o="",postType:n}){const r=o?"swap":"new";M("template_select_modal_opened",{templateSelectMode:r});const[s,l,c]=Re(o),d=s?.length>0,m=t=>{const s=t.type===n,i=t.template;B("template_select_modal_template_selected",{templateSlug:t.slug,templateSelectMode:r,templateType:t.type}),o||(0,a.dispatch)(A.store).resetEditorBlocks(t.emailParsed),(0,a.dispatch)(H.H).setTemplateToPost(s?i.template:t.slug),e()},p=()=>{var e;const t=null!==(e=s[0])&&void 0!==e?e:null;t&&(B("template_select_modal_handle_close_without_template_selected"),m(t))};return(0,i.jsxs)(b.Modal,{title:"new"===r?(0,y.__)("Start with an email preset","woocommerce"):(0,y.__)("Select a template","woocommerce"),onRequestClose:()=>(B("template_select_modal_closed",{templateSelectMode:r}),t?t():p()),isFullScreen:!0,children:[(0,i.jsx)(Ze,{hasEmailPosts:c,templates:[...s,...l],handleTemplateSelection:m,templateSelectMode:r}),(0,i.jsx)(b.Flex,{className:"email-editor-modal-footer",justify:"flex-end",children:(0,i.jsx)(b.FlexItem,{children:(0,i.jsx)(b.Button,{variant:"tertiary",className:"email-editor-start_from_scratch_button",onClick:()=>(B("template_select_modal_start_from_scratch_clicked"),p()),isBusy:!d,children:(0,y.__)("Start from scratch","woocommerce")})})})]})}function Ye(){const[e,t]=(0,l.useState)(!1),{emailContentIsEmpty:o,emailHasEdits:n,postType:r}=(0,a.useSelect)((e=>({emailContentIsEmpty:e(H.H).hasEmptyContent(),emailHasEdits:e(H.H).hasEdits(),postType:e(H.H).getEmailPostType()})),[]);return!o||n||e?null:(0,i.jsx)(Je,{onSelectCallback:()=>t(!0),postType:r})}const Ke=(0,i.jsx)(ie.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)(ie.Path,{d:"M12 4c-4.4 0-8 3.6-8 8v.1c0 4.1 3.2 7.5 7.2 7.9h.8c4.4 0 8-3.6 8-8s-3.6-8-8-8zm0 15V5c3.9 0 7 3.1 7 7s-3.1 7-7 7z"})}),Xe=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M6.9 7L3 17.8h1.7l1-2.8h4.1l1 2.8h1.7L8.6 7H6.9zm-.7 6.6l1.5-4.3 1.5 4.3h-3zM21.6 17c-.1.1-.2.2-.3.2-.1.1-.2.1-.4.1s-.3-.1-.4-.2c-.1-.1-.1-.3-.1-.6V12c0-.5 0-1-.1-1.4-.1-.4-.3-.7-.5-1-.2-.2-.5-.4-.9-.5-.4 0-.8-.1-1.3-.1s-1 .1-1.4.2c-.4.1-.7.3-1 .4-.2.2-.4.3-.6.5-.1.2-.2.4-.2.7 0 .3.1.5.2.8.2.2.4.3.8.3.3 0 .6-.1.8-.3.2-.2.3-.4.3-.7 0-.3-.1-.5-.2-.7-.2-.2-.4-.3-.6-.4.2-.2.4-.3.7-.4.3-.1.6-.1.8-.1.3 0 .6 0 .8.1.2.1.4.3.5.5.1.2.2.5.2.9v1.1c0 .3-.1.5-.3.6-.2.2-.5.3-.9.4-.3.1-.7.3-1.1.4-.4.1-.8.3-1.1.5-.3.2-.6.4-.8.7-.2.3-.3.7-.3 1.2 0 .6.2 1.1.5 1.4.3.4.9.5 1.6.5.5 0 1-.1 1.4-.3.4-.2.8-.6 1.1-1.1 0 .4.1.7.3 1 .2.3.6.4 1.2.4.4 0 .7-.1.9-.2.2-.1.5-.3.7-.4h-.3zm-3-.9c-.2.4-.5.7-.8.8-.3.2-.6.2-.8.2-.4 0-.6-.1-.9-.3-.2-.2-.3-.6-.3-1.1 0-.5.1-.9.3-1.2s.5-.5.8-.7c.3-.2.7-.3 1-.5.3-.1.6-.3.7-.6v3.4z"})}),Qe=(0,i.jsx)(ie.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)(ie.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"})}),et=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),tt={};function ot(e){return!Object.keys(e).some((t=>Object.keys(e[t]).length>0))}function nt(e){const t=e=>{if("object"==typeof e&&null!==e||void 0===e){if(Array.isArray(e)&&0===e.length)return;for(const o in e)if(e.hasOwnProperty(o)){const n=t(e[o]);void 0===n||ot(n)?delete e[o]:e[o]=n}}return e};return t(e)}const rt=()=>{const{userTheme:e,updateUserTheme:t}=Te(),o=(0,l.useMemo)((()=>e?nt(function(e){const t=e=>{if("object"==typeof e&&null!==e)for(const o in e)e.hasOwnProperty(o)&&(e[o]=t(e[o]));else if("string"==typeof e)return e.replace(/var\(--([a-z]+)--([a-z]+(?:--[a-z0-9]+(?:-[a-z0-9]+)*)*)--([a-z0-9-]+)\)/g,((e,t,o,n)=>`var:${o.split("--").concat(n).join("|")}`));return e};return t(e)}(e?.styles)):tt),[e]),{styles:n}=(0,a.useSelect)((e=>({styles:e(H.H).getStyles()}))),r=(0,l.useCallback)((o=>{const n={...e,styles:nt(o)};t(n)}),[t,e]),s=(0,l.useCallback)(((o,n)=>{const r=function(e,t,o){const n=Array.isArray(t)?[...t]:[t],r=Array.isArray(e)?[...e]:{...e},s=n.pop();let i=r;return n.forEach((e=>{const t=i[e];i[e]=Array.isArray(t)?[...t]:{...t},i=i[e]})),i[s]=o,r}(e,["styles",...o],n);t(r)}),[t,e]);return{styles:(0,l.useMemo)((()=>n?o?Ee().all([n,o]):n:tt),[n,o]),userStyles:e?.styles,defaultStyles:n,updateStyleProp:s,updateStyles:r}},st=[],it={start:{scale:1,opacity:1},hover:{scale:0,opacity:0}},at={hover:{opacity:1},start:{opacity:.5}},lt={hover:{scale:1,opacity:1},start:{scale:0,opacity:0}};function ct({label:e,isFocused:t,withHoverView:o}){const{colors:n}=(0,a.useSelect)((e=>({colors:e(H.H).getPaletteColors()})),[]),r=(0,l.useMemo)((()=>(n?.theme||st).concat(n?.default||st)),[n]),{styles:s}=rt(),{backgroundColor:c,headingColor:d,highlightedColors:m}=(0,l.useMemo)((()=>{const e=Be(s?.color?.background)||"white",t=Be(s?.color?.text)||"black",o=Be(s?.elements?.h1?.color?.text)||t,n=Be(s?.elements?.link?.color?.text)||o,i=Be(s?.elements?.button?.color?.background)||n,a=r.find((({color:e})=>e.toLowerCase()===t.toLowerCase())),l=r.find((({color:e})=>e.toLowerCase()===i.toLowerCase())),c=[...a?[a]:st,...l?[l]:st,...r].filter((({color:t},o,n)=>t.toLowerCase()!==e.toLowerCase()&&o===n.findIndex((e=>e.color.toLowerCase()===t.toLowerCase())))).slice(0,2);return{backgroundColor:e,headingColor:o,highlightedColors:c}}),[s,r]),p=s?.elements?.heading?.typography?.fontWeight||"inherit",u=s?.elements?.heading?.typography?.fontFamily||"inherit",[_,g]=(0,l.useState)(!1);return(0,i.jsx)("div",{onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:(0,i.jsxs)(b.__unstableMotion.div,{style:{height:152,width:"100%",background:c,cursor:o?"pointer":void 0},initial:"start",animate:(_||t)&&e?"hover":"start",children:[(0,i.jsx)(b.__unstableMotion.div,{variants:it,style:{height:"100%",overflow:"hidden"},children:(0,i.jsxs)(b.__experimentalHStack,{spacing:10,justify:"center",style:{height:"100%",overflow:"hidden"},children:[(0,i.jsx)(b.__unstableMotion.div,{style:{fontFamily:u,fontSize:65,color:d,fontWeight:p},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:.3,type:"tween"},children:"Aa"}),(0,i.jsx)(b.__experimentalVStack,{spacing:4,children:m.map((({slug:e,color:t},o)=>(0,i.jsx)(b.__unstableMotion.div,{style:{height:32,width:32,background:t,borderRadius:16},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:1===o?.2:.1}},e)))})]})}),(0,i.jsx)(b.__unstableMotion.div,{variants:o&&at,style:{height:"100%",width:"100%",position:"absolute",top:0,overflow:"hidden",filter:"blur(60px)",opacity:.1},children:(0,i.jsx)(b.__experimentalHStack,{spacing:0,justify:"flex-start",style:{height:"100%",overflow:"hidden"},children:r.slice(0,4).map((({color:e})=>(0,i.jsx)("div",{style:{height:"100%",background:e,flexGrow:1}},e)))})}),(0,i.jsx)(b.__unstableMotion.div,{variants:lt,style:{height:"100%",width:"100%",overflow:"hidden",position:"absolute",top:0},children:(0,i.jsx)(b.__experimentalVStack,{spacing:3,justify:"center",style:{height:"100%",overflow:"hidden",padding:10,boxSizing:"border-box"},children:e&&(0,i.jsx)("div",{style:{fontSize:40,fontFamily:u,color:d,fontWeight:p,lineHeight:"1em",textAlign:"center"},children:e})})})]})})}function dt(){return(0,i.jsx)(b.Card,{size:"small",className:"edit-site-global-styles-screen-root",variant:"primary",children:(0,i.jsx)(b.CardBody,{children:(0,i.jsxs)(b.__experimentalVStack,{spacing:4,children:[(0,i.jsx)(b.Card,{children:(0,i.jsx)(b.CardMedia,{children:(0,i.jsx)(ct,{})})}),(0,i.jsxs)(b.__experimentalItemGroup,{children:[(0,i.jsx)(b.__experimentalNavigatorButton,{path:"/typography",onClick:()=>B("styles_sidebar_navigation_click",{path:"typography"}),children:(0,i.jsx)(b.__experimentalItem,{children:(0,i.jsxs)(b.__experimentalHStack,{justify:"flex-start",children:[(0,i.jsx)(b.Icon,{icon:Xe,size:24}),(0,i.jsx)(b.FlexItem,{children:(0,y.__)("Typography","woocommerce")})]})})}),(0,i.jsx)(b.__experimentalNavigatorButton,{path:"/colors",onClick:()=>B("styles_sidebar_navigation_click",{path:"colors"}),children:(0,i.jsx)(b.__experimentalItem,{children:(0,i.jsxs)(b.__experimentalHStack,{justify:"flex-start",children:[(0,i.jsx)(b.Icon,{icon:Qe,size:24}),(0,i.jsx)(b.FlexItem,{children:(0,y.__)("Colors","woocommerce")})]})})}),(0,i.jsx)(b.__experimentalNavigatorButton,{path:"/layout",onClick:()=>B("styles_sidebar_navigation_click",{path:"layout"}),children:(0,i.jsx)(b.__experimentalItem,{children:(0,i.jsxs)(b.__experimentalHStack,{justify:"flex-start",children:[(0,i.jsx)(b.Icon,{icon:et,size:24}),(0,i.jsx)(b.FlexItem,{children:(0,y.__)("Layout","woocommerce")})]})})})]})]})})})}const mt={typography:{},color:{}},pt=(e,t,o="heading",n=!1)=>{switch(t){case"text":return{typography:e.typography,color:e.color};case"heading":return((e,t="heading",o=!1)=>o?Ee().all([mt,e.elements.heading||{},e.elements[t]||{}]):{...mt,...e.elements.heading||{},...e.elements[t]||{}})(e,null!=o?o:"heading",n);default:return e.elements[t]||mt}};function ut({element:e,label:t}){const{styles:o}=rt(),n=pt(o,e,null,!0),{fontFamily:r,fontStyle:s,fontWeight:a,letterSpacing:l,textDecoration:c,textTransform:d}=n.typography,m=n.color?.text||"inherit",p=n.color?.background||"#f0f0f0",u=(0,y.sprintf)((0,y.__)("Typography %s styles","woocommerce"),t);return(0,i.jsx)(b.__experimentalItem,{children:(0,i.jsx)(b.__experimentalNavigatorButton,{path:`/typography/${e}`,"aria-label":u,onClick:()=>B("styles_sidebar_screen_typography_button_click",{element:e,label:t,path:`typography/${e}`}),children:(0,i.jsxs)(b.__experimentalHStack,{justify:"flex-start",children:[(0,i.jsx)(b.FlexItem,{className:"edit-site-global-styles-screen-typography__indicator",style:{fontFamily:null!=r?r:"serif",background:p,color:m,fontStyle:null!=s?s:"normal",fontWeight:null!=a?a:"normal",letterSpacing:null!=l?l:"normal",textDecoration:null!=c?c:"link"===e?"underline":"none",textTransform:null!=d?d:"none"},children:"Aa"}),(0,i.jsx)(b.FlexItem,{children:t})]})})})}const _t=function(){return(0,i.jsx)(b.Card,{size:"small",variant:"primary",isBorderless:!0,children:(0,i.jsx)(b.CardBody,{children:(0,i.jsxs)(b.__experimentalVStack,{spacing:3,children:[(0,i.jsx)(b.__experimentalHeading,{level:3,className:"edit-site-global-styles-subtitle",children:(0,y.__)("Elements","woocommerce")}),(0,i.jsxs)(b.__experimentalItemGroup,{isBordered:!0,isSeparated:!0,size:"small",children:[(0,i.jsx)(ut,{element:"text",label:(0,y.__)("Text","woocommerce")}),(0,i.jsx)(ut,{element:"link",label:(0,y.__)("Links","woocommerce")}),(0,i.jsx)(ut,{element:"heading",label:(0,y.__)("Headings","woocommerce")}),(0,i.jsx)(ut,{element:"button",label:(0,y.__)("Buttons","woocommerce")})]})]})})})},gt=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})}),ht=b.Navigator||b.__experimentalNavigatorProvider;function yt({title:e,description:t,onBack:o}){return(0,i.jsxs)(b.__experimentalVStack,{spacing:0,children:[(0,i.jsx)(b.__experimentalView,{children:(0,i.jsx)(b.__experimentalSpacer,{marginBottom:0,paddingX:4,paddingY:3,children:(0,i.jsxs)(b.__experimentalHStack,{spacing:2,children:[(0,i.jsx)(ht.BackButton,{style:{minWidth:24,padding:0},icon:gt,size:"small","aria-label":(0,y.__)("Navigate to the previous view","woocommerce"),onClick:o}),(0,i.jsx)(b.__experimentalSpacer,{children:(0,i.jsx)(b.__experimentalHeading,{className:"woocommerce-email-editor-styles-header",level:2,size:13,children:e})})]})})}),t&&(0,i.jsx)("p",{className:"woocommerce-email-editor-styles-header-description",children:t})]})}b.Navigator||(ht.Screen=b.__experimentalNavigatorScreen,ht.BackButton=b.__experimentalNavigatorBackButton);const wt=yt;function xt(){return M("styles_sidebar_screen_typography_opened"),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(wt,{title:(0,y.__)("Typography","woocommerce"),description:(0,y.__)("Manage the typography settings for different elements.","woocommerce")}),(0,i.jsx)(_t,{})]})}const ft={fontFamily:!0,fontSize:!0,fontAppearance:!0,lineHeight:!0,letterSpacing:!1,textTransform:!1,textDecoration:!1,writingMode:!0,textColumns:!0},bt=function({element:e,headingLevel:t,defaultControls:o=ft}){const[n,r]=(0,u.useSettings)("typography.fontSizes","typography.fontFamilies"),s=r?.default||[],{styles:a,defaultStyles:c,updateStyleProp:d}=rt(),m=pt(a,e,t),p=pt(c,e,t),{fontFamily:_,fontSize:g,fontStyle:h,fontWeight:w,lineHeight:x,letterSpacing:f,textDecoration:v,textTransform:j}=m.typography,{fontFamily:k,fontSize:S,fontStyle:C,fontWeight:E,lineHeight:T,letterSpacing:P,textDecoration:N,textTransform:M}=p.typography,H="heading"!==e||"heading"!==t,F=(0,l.useCallback)(((o,n)=>{d("heading"===e?["elements",t,...o]:"text"===e?[...o]:["elements",e,...o],n)}),[e,d,t]),z=t=>{F(["typography","letterSpacing"],t),I("styles_sidebar_screen_typography_element_panel_set_letter_spacing",{element:e,newValue:t,selectedDefaultLetterSpacing:t===P})},R=t=>{F(["typography","lineHeight"],t),I("styles_sidebar_screen_typography_element_panel_set_line_height",{element:e,newValue:t,selectedDefaultLineHeight:t===T})},A=o=>{F(["typography","fontSize"],o),I("styles_sidebar_screen_typography_element_panel_set_font_size",{element:e,headingLevel:t,newValue:o,selectedDefaultFontSize:o===S})},L=t=>{F(["typography","fontFamily"],t),I("styles_sidebar_screen_typography_element_panel_set_font_family",{element:e,newValue:t,selectedDefaultFontFamily:t===k})},O=t=>{F(["typography","textDecoration"],t),I("styles_sidebar_screen_typography_element_panel_set_text_decoration",{element:e,newValue:t,selectedDefaultTextDecoration:t===N})},V=t=>{F(["typography","textTransform"],t),I("styles_sidebar_screen_typography_element_panel_set_text_transform",{element:e,newValue:t,selectedDefaultTextTransform:t===M})},D=({fontStyle:t,fontWeight:o})=>{F(["typography","fontStyle"],t),F(["typography","fontWeight"],o),I("styles_sidebar_screen_typography_element_panel_set_font_appearance",{element:e,newFontStyle:t,newFontWeight:o,selectedDefaultFontStyle:t===C,selectedDefaultFontWeight:o===E})};return(0,i.jsxs)(b.__experimentalToolsPanel,{label:(0,y.__)("Typography","woocommerce"),resetAll:()=>{F(["typography"],{}),B("styles_sidebar_screen_typography_element_panel_reset_all_styles_selected",{element:e,headingLevel:t})},children:[(0,i.jsx)(b.__experimentalToolsPanelItem,{label:(0,y.__)("Font family","woocommerce"),hasValue:()=>_!==k,onDeselect:()=>L(void 0),isShownByDefault:o.fontFamily,children:(0,i.jsx)(u.__experimentalFontFamilyControl,{value:_,onChange:L,size:"__unstable-large",fontFamilies:s,__nextHasNoMarginBottom:!0})}),H&&(0,i.jsx)(b.__experimentalToolsPanelItem,{label:(0,y.__)("Font size","woocommerce"),hasValue:()=>g!==S,onDeselect:()=>A(void 0),isShownByDefault:o.fontSize,children:(0,i.jsx)(b.FontSizePicker,{value:g,onChange:A,fontSizes:n,disableCustomFontSizes:!1,withReset:!1,withSlider:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})}),(0,i.jsx)(b.__experimentalToolsPanelItem,{className:"single-column",label:(0,y.__)("Appearance","woocommerce"),hasValue:()=>w!==E||h!==C,onDeselect:()=>{D({fontStyle:void 0,fontWeight:void 0})},isShownByDefault:o.fontAppearance,children:(0,i.jsx)(u.__experimentalFontAppearanceControl,{value:{fontStyle:h,fontWeight:w},onChange:D,hasFontStyles:!0,hasFontWeights:!0,size:"__unstable-large"})}),(0,i.jsx)(b.__experimentalToolsPanelItem,{className:"single-column",label:(0,y.__)("Line height","woocommerce"),hasValue:()=>x!==T,onDeselect:()=>R(void 0),isShownByDefault:o.lineHeight,children:(0,i.jsx)(u.LineHeightControl,{__nextHasNoMarginBottom:!0,__unstableInputWidth:"auto",value:x,onChange:R,size:"__unstable-large"})}),(0,i.jsx)(b.__experimentalToolsPanelItem,{className:"single-column",label:(0,y.__)("Letter spacing","woocommerce"),hasValue:()=>f!==P,onDeselect:()=>z(void 0),isShownByDefault:o.letterSpacing,children:(0,i.jsx)(u.__experimentalLetterSpacingControl,{value:f,onChange:z,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,i.jsx)(b.__experimentalToolsPanelItem,{className:"single-column",label:(0,y.__)("Text decoration","woocommerce"),hasValue:()=>v!==N,onDeselect:()=>O(void 0),isShownByDefault:o.textDecoration,children:(0,i.jsx)(u.__experimentalTextDecorationControl,{value:v,onChange:O,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,i.jsx)(b.__experimentalToolsPanelItem,{label:(0,y.__)("Letter case","woocommerce"),hasValue:()=>j!==M,onDeselect:()=>V(M),isShownByDefault:o.textTransform,children:(0,i.jsx)(u.__experimentalTextTransformControl,{value:j,onChange:V,showNone:!0,isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})})]})};function vt({element:e,headingLevel:t}){const{styles:o}=rt(),n=pt(o,e,t,!0),{fontFamily:r,fontSize:s,fontStyle:a,fontWeight:l,lineHeight:c,letterSpacing:d,textDecoration:m,textTransform:p}=n.typography,u=n.color?.text||"inherit",_=n.color?.background||"#f0f0f0",g="link"===e?{textDecoration:null!=m?m:"underline"}:{};return(0,i.jsx)("div",{className:"edit-site-typography-preview",style:{fontFamily:null!=r?r:"serif",background:_,color:u,lineHeight:c,fontSize:s,fontStyle:a,fontWeight:l,letterSpacing:d,textDecoration:m,textTransform:p,...g},children:"Aa"})}const jt={text:{title:(0,y.__)("Text","woocommerce"),description:(0,y.__)("Manage the fonts and typography used on text.","woocommerce"),defaultControls:ft},link:{title:(0,y.__)("Links","woocommerce"),description:(0,y.__)("Manage the fonts and typography used on links.","woocommerce"),defaultControls:{...ft,textDecoration:!0}},heading:{title:(0,y.__)("Headings","woocommerce"),description:(0,y.__)("Manage the fonts and typography used on headings.","woocommerce"),defaultControls:{...ft,textTransform:!0}},button:{title:(0,y.__)("Buttons","woocommerce"),description:(0,y.__)("Manage the fonts and typography used on buttons.","woocommerce"),defaultControls:ft}};function kt({element:e}){M("styles_sidebar_screen_typography_element_opened",{element:e});const[t,o]=(0,l.useState)("heading");return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(wt,{title:jt[e].title,description:jt[e].description}),(0,i.jsx)(b.__experimentalSpacer,{marginX:4,children:(0,i.jsx)(vt,{element:e,headingLevel:t})}),"heading"===e&&(0,i.jsx)(b.__experimentalSpacer,{marginX:4,marginBottom:"1em",children:(0,i.jsxs)(b.__experimentalToggleGroupControl,{label:(0,y.__)("Select heading level","woocommerce"),hideLabelFromVision:!0,value:t,onChange:e=>{o(e.toString()),B("styles_sidebar_screen_typography_element_heading_level_selected",{value:e})},isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0,children:[(0,i.jsx)(b.__experimentalToggleGroupControlOption,{value:"heading",label:(0,y._x)("All","heading levels","woocommerce")}),(0,i.jsx)(b.__experimentalToggleGroupControlOption,{value:"h1",label:(0,y._x)("H1","Heading Level","woocommerce")}),(0,i.jsx)(b.__experimentalToggleGroupControlOption,{value:"h2",label:(0,y._x)("H2","Heading Level","woocommerce")}),(0,i.jsx)(b.__experimentalToggleGroupControlOption,{value:"h3",label:(0,y._x)("H3","Heading Level","woocommerce")}),(0,i.jsx)(b.__experimentalToggleGroupControlOption,{value:"h4",label:(0,y._x)("H4","Heading Level","woocommerce")}),(0,i.jsx)(b.__experimentalToggleGroupControlOption,{value:"h5",label:(0,y._x)("H5","Heading Level","woocommerce")}),(0,i.jsx)(b.__experimentalToggleGroupControlOption,{value:"h6",label:(0,y._x)("H6","Heading Level","woocommerce")})]})}),(0,i.jsx)(bt,{element:e,headingLevel:t,defaultControls:jt[e].defaultControls})]})}function St(){M("styles_sidebar_screen_colors_opened");const{userStyles:e,styles:t,updateStyles:o}=rt(),n=(0,a.useSelect)((e=>e(H.H).getTheme()),[]);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(wt,{title:(0,y.__)("Colors","woocommerce"),description:(0,y.__)("Manage palettes and the default color of different global elements.","woocommerce")}),(0,i.jsx)(Y,{value:e,inheritedValue:t,onChange:e=>{o(e),B("styles_sidebar_screen_colors_styles_updated")},settings:n?.settings,panelId:"colors"})]})}function Ct(){const[e]=(0,u.useSettings)("spacing.units"),t=(0,b.__experimentalUseCustomUnits)({availableUnits:e}),{styles:o,defaultStyles:n,updateStyleProp:r}=rt();return(0,i.jsxs)(b.__experimentalToolsPanel,{label:(0,y.__)("Dimensions","woocommerce"),resetAll:()=>{r(["spacing"],n.spacing),B("styles_sidebar_screen_layout_dimensions_reset_all_selected")},children:[(0,i.jsx)(b.__experimentalToolsPanelItem,{isShownByDefault:!0,hasValue:()=>!(0,E.isEqual)(o.spacing.padding,n.spacing.padding),label:(0,y.__)("Padding","woocommerce"),onDeselect:()=>{r(["spacing","padding"],n.spacing.padding),B("styles_sidebar_screen_layout_dimensions_padding_reset_clicked")},className:"tools-panel-item-spacing",children:(0,i.jsx)(u.__experimentalSpacingSizesControl,{allowReset:!0,values:o.spacing.padding,onChange:e=>{r(["spacing","padding"],e),I("styles_sidebar_screen_layout_dimensions_padding_updated",{value:e})},label:(0,y.__)("Padding","woocommerce"),sides:["horizontal","vertical","top","left","right","bottom"],units:t})}),(0,i.jsx)(b.__experimentalToolsPanelItem,{isShownByDefault:!0,label:(0,y.__)("Block spacing","woocommerce"),hasValue:()=>o.spacing.blockGap!==n.spacing.blockGap,onDeselect:()=>{r(["spacing","blockGap"],n.spacing.blockGap),B("styles_sidebar_screen_layout_dimensions_block_spacing_reset_clicked")},className:"tools-panel-item-spacing",children:(0,i.jsx)(u.__experimentalSpacingSizesControl,{label:(0,y.__)("Block spacing","woocommerce"),min:0,onChange:e=>{r(["spacing","blockGap"],e.top),I("styles_sidebar_screen_layout_dimensions_block_spacing_updated",{value:e})},showSideInLabel:!1,sides:["top"],values:{top:o.spacing.blockGap},allowReset:!0})})]})}function Et(){return M("styles_sidebar_screen_layout_opened"),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(yt,{title:(0,y.__)("Layout","woocommerce")}),(0,i.jsx)(Ct,{})]})}const Tt=(0,l.memo)((function(){const{userCanEditGlobalStyles:e}=(0,a.useSelect)((e=>{const{canEdit:t}=e(H.H).canUserEditGlobalEmailStyles();return{userCanEditGlobalStyles:t}}),[]);return e&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(A.PluginSidebarMoreMenuItem,{target:"email-styles-sidebar",icon:Ke,children:(0,y.__)("Email styles","woocommerce")}),(0,i.jsx)(A.PluginSidebar,{name:"email-styles-sidebar",icon:Ke,title:(0,y.__)("Styles","woocommerce"),className:"woocommerce-email-editor-styles-panel",header:(0,y.__)("Styles","woocommerce"),children:(0,i.jsxs)(ht,{initialPath:"/",children:[(0,i.jsx)(ht.Screen,{path:"/",children:(0,i.jsx)(dt,{})}),(0,i.jsx)(ht.Screen,{path:"/typography",children:(0,i.jsx)(xt,{})}),(0,i.jsx)(ht.Screen,{path:"/typography/text",children:(0,i.jsx)(kt,{element:"text"})}),(0,i.jsx)(ht.Screen,{path:"/typography/link",children:(0,i.jsx)(kt,{element:"link"})}),(0,i.jsx)(ht.Screen,{path:"/typography/heading",children:(0,i.jsx)(kt,{element:"heading"})}),(0,i.jsx)(ht.Screen,{path:"/typography/button",children:(0,i.jsx)(kt,{element:"button"})}),(0,i.jsx)(ht.Screen,{path:"/colors",children:(0,i.jsx)(St,{})}),(0,i.jsx)(ht.Screen,{path:"/layout",children:(0,i.jsx)(Et,{})})]})})]})})),Pt=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),Nt=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),Bt=window.wp.keycodes,Mt=window.wp.url;var It;!function(e){e.SUCCESS="success",e.ERROR="error"}(It||(It={}));const Ht=(0,l.memo)((function(){const e=(0,l.useRef)(null),{requestSendingNewsletterPreview:t,togglePreviewModal:o,updateSendPreviewEmail:n}=(0,a.useDispatch)(H.H),{toEmail:r,isSendingPreviewEmail:s,sendingPreviewStatus:d,isModalOpened:m,errorMessage:p,postType:u}=(0,a.useSelect)((e=>({...e(H.H).getPreviewState(),postType:e(H.H).getEmailPostType()})),[]),_=()=>{t(r)},g=(0,l.useMemo)((()=>(0,c.applyFilters)("woocommerce_email_editor_check_sending_method_configuration_link",`https://www.mailpoet.com/blog/mailpoet-smtp-plugin/?utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${u}`)),[u]),h=()=>{B("send_preview_email_modal_closed"),o(!1)};return(0,l.useEffect)((()=>{m&&(e.current?.focus(),B("send_preview_email_modal_opened"))}),[m]),m?(0,i.jsxs)(b.Modal,{className:"woocommerce-send-preview-email",title:(0,y.__)("Send a test email","woocommerce"),onRequestClose:h,focusOnMount:!1,children:[d===It.ERROR?(0,i.jsxs)("div",{className:"woocommerce-send-preview-modal-notice-error",children:[(0,i.jsx)("p",{children:(0,y.__)("Sorry, we were unable to send this email.","woocommerce")}),(0,i.jsx)("strong",{children:p&&(0,y.sprintf)((0,y.__)("Error: %s","woocommerce"),p)}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:g&&(0,l.createInterpolateElement)((0,y.__)("Please check your <link>sending method configuration</link> with your hosting provider.","woocommerce"),{link:(0,i.jsx)("a",{href:g,target:"_blank",rel:"noopener noreferrer",onClick:()=>B("send_preview_email_modal_check_sending_method_configuration_link_clicked")})})}),(0,i.jsx)("li",{children:(0,l.createInterpolateElement)((0,y.__)("Or, sign up for MailPoet Sending Service to easily send emails. <link>Sign up for free</link>","woocommerce"),{link:(0,i.jsx)("a",{href:`https://account.mailpoet.com/?s=1&g=1&utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${u}`,target:"_blank",rel:"noopener noreferrer",onClick:()=>B("send_preview_email_modal_sign_up_for_mailpoet_sending_service_link_clicked")},"sign-up-for-free")})})]})]}):null,(0,i.jsx)("p",{children:(0,y.__)("Send yourself a test email to test how your email would look like in different email apps.","woocommerce")}),(0,i.jsx)(b.TextControl,{label:(0,y.__)("Send to","woocommerce"),onChange:e=>{n(e),M("send_preview_email_modal_send_to_field_updated")},onKeyDown:e=>{const{keyCode:t}=e;t===Bt.ENTER&&(e.preventDefault(),_(),B("send_preview_email_modal_send_to_field_key_code_enter"))},className:"woocommerce-send-preview-email__send-to-field",value:r,type:"email",ref:e,required:!0,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0}),d===It.SUCCESS?(0,i.jsxs)("p",{className:"woocommerce-send-preview-modal-notice-success",children:[(0,i.jsx)(Ae,{icon:Nt,style:{fill:"#4AB866"}}),(0,y.__)("Test email sent successfully!","woocommerce")]}):null,(0,i.jsxs)("div",{className:"woocommerce-send-preview-modal-footer",children:[(0,i.jsx)(b.Button,{variant:"tertiary",onClick:()=>{B("send_preview_email_modal_close_button_clicked"),h()},children:(0,y.__)("Cancel","woocommerce")}),(0,i.jsx)(b.Button,{variant:"primary",onClick:()=>{_(),B("send_preview_email_modal_send_test_email_button_clicked")},disabled:s||!(0,Mt.isEmail)(r),children:s?(0,y.__)("Sending…","woocommerce"):(0,y.__)("Send test email","woocommerce")})]})]}):null}));function Ft(){const{togglePreviewModal:e}=(0,a.useDispatch)(H.H);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(A.PluginPreviewMenuItem,{icon:Pt,onClick:()=>{B("header_preview_dropdown_send_test_email_selected"),e(!0)},children:(0,y.__)("Send a test email","woocommerce")}),(0,i.jsx)(Ht,{})]})}const zt=window.wp.preferences,Rt=()=>{const e=(0,_.useViewportMatch)("large");return(0,i.jsx)(i.Fragment,{children:e&&(0,i.jsx)(ee,{children:(0,i.jsx)(zt.PreferenceToggleMenuItem,{scope:H.H,name:"fullscreenMode",label:(0,y.__)("Fullscreen mode","woocommerce"),info:(0,y.__)("Show and hide the admin user interface","woocommerce"),messageActivated:(0,y.__)("Fullscreen mode activated.","woocommerce"),messageDeactivated:(0,y.__)("Fullscreen mode deactivated.","woocommerce"),shortcut:Bt.displayShortcut.secondary("f")})})})};function At({label:e,labelSuffix:t,help:o,placeholder:n,attributeName:r,attributeValue:s,updateProperty:c=()=>{}}){const[d,m]=(0,l.useState)(null),[p,_]=(0,l.useState)(!1),g=(0,a.useSelect)((e=>e(H.H).getPersonalizationTagsList()),[]),h=(0,l.useRef)(null),w=(0,l.useCallback)(((e,t,o)=>{var n,s;const i=null!==(n=o?.start)&&void 0!==n?n:t.length,a=null!==(s=o?.end)&&void 0!==s?s:t.length;let l=(0,f.create)({html:t});l=(0,f.insert)(l,(0,f.create)({html:`\x3c!--${e}--\x3e`}),i,a);const d=(0,f.toHTMLString)({value:l});c(r,d),m(null)}),[r,c]),x=(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{children:e}),(0,i.jsx)(b.Button,{className:"woocommerce-settings-panel-personalization-tags-button",icon:"shortcode",title:(0,y.__)("Personalization Tags","woocommerce"),onClick:()=>{_(!0),B("rich_text_with_button_personalization_tags_shortcode_icon_clicked",{attributeName:r,label:e})}}),t]});return r?(0,i.jsxs)(b.BaseControl,{id:"",label:x,className:`woocommerce-settings-panel-${r}-text`,help:o,__nextHasNoMarginBottom:!0,children:[(0,i.jsx)(F,{isOpened:p,onInsert:e=>{w(e,null!=s?s:"",d),_(!1),B("rich_text_with_button_personalization_tags_inserted",{attributeName:r,value:e})},closeCallback:()=>_(!1),openedBy:"RichTextWithButton-BaseControl"}),(0,i.jsx)(z,{contentRef:h,onUpdate:(e,t)=>{const o=(null!=s?s:"").replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);c(r,o)}}),(0,i.jsx)(u.RichText,{ref:h,className:"woocommerce-settings-panel-richtext",placeholder:n,onFocus:()=>{m(v(h,null!=s?s:""))},onKeyUp:()=>{m(v(h,null!=s?s:""))},onClick:()=>{m(v(h,null!=s?s:""))},onChange:e=>{var t;e=j(null!==(t=e)&&void 0!==t?t:"",g),c(r,e),M("rich_text_with_button_input_field_updated",{attributeName:r})},value:null!=s?s:"","data-automation-id":`email_${r}`})]}):null}function Lt({close:e}){M("edit_template_modal_opened");const{onNavigateToEntityRecord:t,template:o}=(0,a.useSelect)((e=>{const{getEditorSettings:t}=e(A.store);return{onNavigateToEntityRecord:t().onNavigateToEntityRecord,template:e(H.H).getCurrentTemplate()}}),[]);return(0,i.jsxs)(b.Modal,{size:"medium",onRequestClose:e,__experimentalHideHeader:!0,children:[(0,i.jsx)("p",{children:(0,y.__)("This template is used by multiple emails. Any changes made would affect other emails on the site. Are you sure you want to edit the template?","woocommerce")}),(0,i.jsxs)(b.Flex,{justify:"end",children:[(0,i.jsx)(b.FlexItem,{children:(0,i.jsx)(b.Button,{variant:"tertiary",onClick:()=>{B("edit_template_modal_cancel_button_clicked"),e()},children:(0,y.__)("Cancel","woocommerce")})}),(0,i.jsx)(b.FlexItem,{children:(0,i.jsx)(b.Button,{variant:"primary",onClick:()=>{B("edit_template_modal_continue_button_clicked",{templateId:o.id}),t({postId:o.id,postType:"wp_template"})},disabled:!o.id,children:(0,y.__)("Edit template","woocommerce")})})]})]})}function Ot(){const{template:e,currentEmailContent:t,canUpdateTemplates:o,postType:n}=(0,a.useSelect)((e=>({template:e(H.H).getCurrentTemplate(),currentEmailContent:e(H.H).getEditedEmailContent(),canUpdateTemplates:e(H.H).canUserEditTemplates(),postType:e(H.H).getEmailPostType()})),[]),[r]=Re("swap"),[s,c]=(0,l.useState)(!1),[d,m]=(0,l.useState)(!1);return(0,i.jsxs)(i.Fragment,{children:[e&&(0,i.jsx)(b.PanelRow,{children:(0,i.jsxs)(b.Flex,{justify:"start",children:[(0,i.jsx)(b.FlexItem,{className:"editor-post-panel__row-label",children:(0,y.__)("Template","woocommerce")}),(0,i.jsxs)(b.FlexItem,{children:[!(r?.length>1||o)&&(0,i.jsx)("b",{children:e?.title}),(r?.length>1||o)&&(0,i.jsx)(b.DropdownMenu,{icon:null,text:e?.title,toggleProps:{variant:"tertiary"},label:(0,y.__)("Template actions","woocommerce"),onToggle:t=>B("sidebar_template_actions_clicked",{currentTemplate:e?.title,isOpen:t}),children:({onClose:e})=>(0,i.jsxs)(i.Fragment,{children:[o&&(0,i.jsx)(b.MenuItem,{onClick:()=>{B("sidebar_template_actions_edit_template_clicked"),c(!0),e()},children:(0,y.__)("Edit template","woocommerce")}),r?.length>1&&(0,i.jsx)(b.MenuItem,{onClick:()=>{B("sidebar_template_actions_swap_template_clicked"),m(!0),e()},children:(0,y.__)("Swap template","woocommerce")})]})})]})]})}),s&&(0,i.jsx)(Lt,{close:()=>(B("edit_template_modal_closed"),c(!1))}),d&&(0,i.jsx)(Je,{onSelectCallback:()=>m(!1),closeCallback:()=>m(!1),previewContent:t,postType:n})]})}const Vt={recordEvent:B,recordEventOnce:M,debouncedRecordEvent:I};function Dt(){const e=(0,l.useMemo)((()=>(0,c.applyFilters)("woocommerce_email_editor_setting_sidebar_extension_component",At,Vt)),[]),t=(0,l.useMemo)((()=>(0,c.applyFilters)("woocommerce_email_editor_setting_sidebar_email_status_component",(()=>null),Vt)),[]);return(0,i.jsxs)(A.PluginDocumentSettingPanel,{name:"email-settings-panel",title:(0,y.__)("Settings","woocommerce"),className:"woocommerce-email-editor__settings-panel",children:[(0,i.jsx)(t,{}),(0,i.jsx)(Ot,{}),(0,i.jsx)(A.ErrorBoundary,{canCopyContent:!0,children:(0,i.jsx)(e,{})})]})}const Gt={recordEvent:B,recordEventOnce:M,debouncedRecordEvent:I};function $t(){const e=(0,c.applyFilters)("woocommerce_email_editor_template_sections",[],Gt);return 0===e.length?null:(0,i.jsx)(A.PluginDocumentSettingPanel,{name:"template-settings-panel",title:(0,y.__)("Settings","woocommerce"),className:"woocommerce-email-editor__settings-panel",children:e.map((e=>(0,i.jsx)(A.ErrorBoundary,{children:(0,i.jsx)("div",{children:e.render()},e.id)},`error-boundary-${e.id}`)))})}function Wt(){const{isDirty:e}=(0,A.useEntitiesSavedStatesIsDirty)(),{hasEmptyContent:t,isEmailSent:o,urls:n}=(0,a.useSelect)((e=>({hasEmptyContent:e(H.H).hasEmptyContent(),isEmailSent:e(H.H).isEmailSent(),urls:e(H.H).getUrls()})),[]);function r(){n.send&&(window.location.href=n.send)}const s=t||o||e,l=(0,c.applyFilters)("woocommerce_email_editor_send_button_label",(0,y.__)("Send","woocommerce"));return(0,i.jsx)(b.Button,{variant:"primary",size:"compact",onClick:()=>{B("header_send_button_clicked"),(0,c.applyFilters)("woocommerce_email_editor_send_action_callback",r)()},disabled:s,"data-automation-id":"email_editor_send_button",children:l})}function Ut({children:e}){const t=(0,l.useRef)(document.createElement("div"));return(0,l.useEffect)((()=>{const e=document.getElementsByClassName("editor-post-publish-button__button")[0];e&&e.parentNode?.insertBefore(t.current,e.nextSibling)}),[t]),(0,l.createPortal)((0,i.jsx)(i.Fragment,{children:e}),t.current)}function qt(){const e=(0,l.useRef)(null),{hasNonPostEntityChanges:t,isEditedPostDirty:o,isEditingTemplate:n}=(0,a.useSelect)((e=>({hasNonPostEntityChanges:e(A.store).hasNonPostEntityChanges(),isEditedPostDirty:e(A.store).isEditedPostDirty(),isEditingTemplate:"wp_template"===e(A.store).getCurrentPostType()})),[]),r=n||t||o&&t,s=(0,l.useCallback)(((e,t)=>{t&&e.classList.contains("force-hidden")&&e.classList.remove("force-hidden"),t||e.classList.contains("force-hidden")||e.classList.add("force-hidden")}),[]);return(0,l.useEffect)((()=>{const t=document.getElementsByClassName("editor-post-publish-button__button")[0];return s(t,r),t?(e.current&&e.current.disconnect(),e.current=new MutationObserver((()=>{s(t,r)})),e.current.observe(t,{attributes:!0,childList:!0,subtree:!1}),()=>e.current?.disconnect()):()=>e.current?.disconnect()}),[r,s]),(0,i.jsx)(Ut,{children:!r&&(0,i.jsx)(Wt,{})})}const Zt=()=>{const e="email-validation",t=(0,a.useSelect)((t=>t(se.store).getNotices(e)));return{notices:t,hasValidationNotice:(0,l.useCallback)((e=>e?void 0!==t.find((t=>t.id===e)):t?.length>0),[t]),addValidationNotice:(0,l.useCallback)(((t,o,n=[])=>{(0,a.dispatch)(se.store).createNotice("error",o,{id:t,isDismissible:!1,actions:n,context:e})}),[e]),removeValidationNotice:(0,l.useCallback)((t=>{(0,a.dispatch)(se.store).removeNotice(t,e)}),[e])}};function Jt(){const{notices:e}=Zt();return 0===e.length?null:(0,i.jsx)(b.Notice,{status:"error",className:"woocommerce-email-editor-validation-errors components-editor-notices__pinned",isDismissible:!1,children:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("strong",{children:(0,y.__)("Fix errors to continue:","woocommerce")}),(0,i.jsx)("ul",{children:e.map((({id:e,content:t,actions:o})=>(0,i.jsxs)("li",{children:[t,o.length>0?o.map((({label:e,onClick:t})=>(0,i.jsx)(b.Button,{onClick:t,variant:"link",children:e},e))):null]},e)))})]})})}function Yt({context:e="email-editor"}){const{notices:t}=(0,a.useSelect)((t=>({notices:t(se.store).getNotices(e)})),[e]),o=(0,l.useMemo)((()=>({"site-editor-save-success":{content:(0,y.__)("Email design updated.","woocommerce"),removeActions:!0},"editor-save":{content:(0,y.__)("Email saved.","woocommerce"),removeActions:!1,contentCheck:e=>e.content.includes((0,y.__)("Post updated."))}})),[]),{removeNotice:n}=(0,a.useDispatch)(se.store),r=t.filter((({type:e})=>"snackbar"===e)).map((e=>o[e.id]?o[e.id].contentCheck&&!o[e.id].contentCheck(e)?e:{...e,content:o[e.id].content,spokenMessage:o[e.id].content,actions:o[e.id].removeActions?[]:e.actions}:e));return(0,i.jsx)(b.SnackbarList,{notices:r,className:"components-editor-notices__snackbar",onRemove:t=>n(t,e)})}function Kt({children:e}){const[t]=(0,l.useState)(document.createElement("div"));return(0,l.useEffect)((()=>{const e=document.getElementsByClassName("editor-visual-editor ")[0];e&&e.parentNode?.insertBefore(t,e)}),[t]),(0,l.createPortal)((0,i.jsx)(i.Fragment,{children:e}),t)}function Xt(){const{notices:e}=(0,a.useSelect)((e=>({notices:e(se.store).getNotices("email-editor")})),[]),{removeNotice:t}=(0,a.useDispatch)(se.store),o=e.filter((({isDismissible:e,type:t})=>e&&"default"===t)),n=e.filter((({isDismissible:e,type:t})=>!e&&"default"===t));return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(Kt,{children:[(0,i.jsx)(b.NoticeList,{notices:n,className:"components-editor-notices__pinned"}),(0,i.jsx)(b.NoticeList,{notices:o,className:"components-editor-notices__dismissible",onRemove:e=>t(e,"email-editor")}),(0,i.jsx)(Jt,{})]}),(0,i.jsx)(Yt,{context:"global"}),(0,i.jsx)(Yt,{context:"email-editor"})]})}const Qt=e=>{const t=(0,d.getBlockSupport)(e,"background");return t&&!1!==t?.backgroundImage};function eo(){const e=(0,a.useSelect)((e=>e("core/block-editor").getSelectedBlock()),[]),t=(0,d.hasBlockSupport)(e?.name,"border",!1)||(0,d.hasBlockSupport)(e?.name,"__experimentalBorder",!1);return(0,i.jsxs)(i.Fragment,{children:[t&&(0,i.jsx)(b.Fill,{name:"InspectorControlsBorder",children:(0,i.jsxs)(b.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:[(0,y.__)("Border display may vary or be unsupported in some email clients.","woocommerce"),(0,i.jsx)("br",{}),(0,y.__)("Units other than pixels (px) lack support in old email clients.","woocommerce")]})}),Qt(e?.name)&&(0,i.jsx)(b.Fill,{name:"InspectorControlsBackground",children:(0,i.jsx)(b.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:(0,y.__)("Select a background color for email clients that do not support background images.","woocommerce")})})]})}const to=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",children:(0,i.jsx)(ie.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"})}),oo=(0,i.jsx)(ie.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ie.Path,{d:"M20 11.2H6.8l3.7-3.7-1-1L3.9 12l5.6 5.5 1-1-3.7-3.7H20z"})}),no={edit:{opacity:0,scale:.2},hover:{opacity:1,scale:1,clipPath:"inset( 22% round 2px )"}},ro={edit:{clipPath:"inset(0% round 0px)"},hover:{clipPath:"inset( 22% round 2px )"},tap:{clipPath:"inset(0% round 0px)"}},so=()=>{const{urls:e}=(0,a.useSelect)((e=>({urls:e(H.H).getUrls()})),[]);function t(){e.listings&&(window.location.href=e.back)}return(0,i.jsx)(te,{children:({length:e})=>e<=1&&(0,i.jsxs)(b.__unstableMotion.div,{className:"woocommerce-email-editor__view-mode-toggle",transition:{duration:.2},animate:"edit",initial:"edit",whileHover:"hover",whileTap:"tap",children:[(0,i.jsx)(b.Button,{label:(0,y.__)("Close editor","woocommerce"),showTooltip:!0,tooltipPosition:"middle right",onClick:()=>{B("header_close_button_clicked"),(0,c.applyFilters)("woocommerce_email_editor_close_action_callback",t)()},children:(0,i.jsx)(b.__unstableMotion.div,{variants:ro,children:(0,i.jsx)("div",{className:"woocommerce-email-editor__view-mode-toggle-icon",children:(0,i.jsx)(Ae,{className:"woocommerce-email-editor-icon__icon",icon:to,size:48})})})}),(0,i.jsx)(b.__unstableMotion.div,{className:"woocommerce-email-editor-icon",variants:no,children:(0,i.jsx)(Ae,{icon:oo})})]})})};function io({postId:e,postType:t,settings:o,contentRef:n}){const{currentPost:r,onNavigateToEntityRecord:s,onNavigateToPreviousEntityRecord:c}=function(e,t,o){const[n,r]=(0,l.useReducer)(((e,{type:t,post:o,previousRenderingMode:n})=>"push"===t?[...e,{post:o,previousRenderingMode:n}]:"pop"===t&&e.length>1?e.slice(0,-1):e),[{post:{postId:e,postType:t}}]),{post:s,previousRenderingMode:i}=n[n.length-1],{getRenderingMode:c}=(0,a.useSelect)(A.store),{setRenderingMode:d}=(0,a.useDispatch)(A.store),m=(0,l.useCallback)((e=>{r({type:"push",post:{postId:e.postId,postType:e.postType},previousRenderingMode:c()}),d(o)}),[c,d,o]),p=(0,l.useCallback)((()=>{r({type:"pop"}),i&&d(i)}),[d,i]);return{currentPost:s,onNavigateToEntityRecord:m,onNavigateToPreviousEntityRecord:n.length>1?p:void 0}}(e,t,"post-only"),{post:d,template:m,isFullscreenEnabled:p}=(0,a.useSelect)((e=>{const{getEntityRecord:t}=e(re.store),{getEditedPostTemplate:o}=e(H.H),n=t("postType",r.postType,r.postId);return{template:n&&"wp_template"!==r.postType?o(n.template):null,post:n,isFullscreenEnabled:e(H.H).isFeatureActive("fullscreenMode")}}),[r.postType,r.postId]),{isFullScreenForced:u,displaySendEmailButton:_}=o,{removeEditorPanel:g}=(0,a.useDispatch)(A.store);(0,l.useEffect)((()=>{g("post-status")}),[g]);const[h]=Ie(),y=(0,l.useMemo)((()=>({...o,onNavigateToEntityRecord:s,onNavigateToPreviousEntityRecord:c,defaultRenderingMode:"wp_template"===r.postType?"post-only":"template-locked",supportsTemplateMode:!0})),[o,s,c,r.postType]);return d&&("wp_template"===r.postType||d.template===m?.slug||!d.template&&m)?(M("editor_layout_loaded"),(0,i.jsx)(b.SlotFillProvider,{children:(0,i.jsxs)(A.ErrorBoundary,{canCopyContent:!0,children:[(0,i.jsx)(Se.CommandMenu,{}),(0,i.jsxs)(X,{postId:r.postId,postType:r.postType,settings:y,templateId:m&&m.id,styles:h,contentRef:n,children:[(0,i.jsx)(A.AutosaveMonitor,{}),(0,i.jsx)(A.LocalAutosaveMonitor,{}),(0,i.jsx)(A.UnsavedChangesWarning,{}),(0,i.jsx)(A.EditorKeyboardShortcutsRegister,{}),(0,i.jsx)(A.PostLockedModal,{}),(0,i.jsx)(Ye,{}),(0,i.jsx)(Tt,{}),(0,i.jsx)(Ft,{}),(0,i.jsx)(Q,{isActive:u||p}),(u||p)&&(0,i.jsx)(so,{}),!u&&(0,i.jsx)(Rt,{}),"wp_template"===r.postType?(0,i.jsx)($t,{}):(0,i.jsx)(Dt,{}),_&&(0,i.jsx)(qt,{}),(0,i.jsx)(Xt,{}),(0,i.jsx)(eo,{})]})]})})):(0,i.jsx)("div",{className:"spinner-container",children:(0,i.jsx)(b.Spinner,{style:{width:"80px",height:"80px"}})})}const ao=window.wp.dataControls;function lo(e){return{type:"CHANGE_PREVIEW_STATE",state:{isModalOpened:e}}}function co(e){return{type:"CHANGE_PREVIEW_STATE",state:{toEmail:e}}}function mo(e,t){if(!e||!t)throw new Error("setEmailPost requires valid postId and postType parameters");return{type:"SET_EMAIL_POST",state:{postId:e,postType:t}}}const po=e=>async({registry:t})=>{const o=t.select(H.H).getEmailPostId(),n=t.select(H.H).getEmailPostType();t.dispatch(re.store).editEntityRecord("postType",n,o,{template:e})};function*uo(e){if(!(0,a.select)(H.H).getPreviewState().isSendingPreviewEmail){yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:null,isSendingPreviewEmail:!0}};try{const t=(0,a.select)(H.H).getEmailPostId();yield(0,ao.apiFetch)({path:"/woocommerce-email-editor/v1/send_preview_email",method:"POST",data:{email:e,postId:t}}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:It.SUCCESS,isSendingPreviewEmail:!1}},B("sent_preview_email",{postId:t,email:e})}catch(t){B("sent_preview_email_error",{email:e}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:It.ERROR,isSendingPreviewEmail:!1,errorMessage:JSON.stringify(t?.error)}}}}}function _o(e){return{type:"SET_IS_FETCHING_PERSONALIZATION_TAGS",state:{isFetching:e}}}function go(e){return{type:"SET_PERSONALIZATION_TAGS_LIST",state:{list:e}}}function ho(e){return{type:"SET_CONTENT_VALIDATION",validation:e}}function yo(){if(!window.WooCommerceEmailEditor)throw new Error("WooCommerceEmailEditor global object is not available. This is required for the email editor to work.");return{editorSettings:window.WooCommerceEmailEditor.editor_settings,theme:window.WooCommerceEmailEditor.editor_theme,styles:{globalStylesPostId:window.WooCommerceEmailEditor.user_theme_post_id},urls:window.WooCommerceEmailEditor.urls,preview:{toEmail:window.WooCommerceEmailEditor.current_wp_user_email,isModalOpened:!1,isSendingPreviewEmail:!1,sendingPreviewStatus:null},personalizationTags:{list:[],isFetching:!1},contentValidation:void 0}}function wo(e,t){switch(t.type){case"CHANGE_PREVIEW_STATE":return{...e,preview:{...e.preview,...t.state}};case"SET_EMAIL_POST":return{...e,...t.state};case"CHANGE_PERSONALIZATION_TAGS_STATE":case"SET_IS_FETCHING_PERSONALIZATION_TAGS":case"SET_PERSONALIZATION_TAGS_LIST":return{...e,personalizationTags:{...e.personalizationTags,...t.state}};case"SET_PERSONALIZATION_TAGS":return{...e,personalizationTags:{...e.personalizationTags,list:t.personalizationTags}};case"SET_CONTENT_VALIDATION":return{...e,contentValidation:t.validation};default:return e}}function xo(e){return e?.content&&"function"==typeof e.content?e.content(e):e?.blocks?(0,d.serialize)(e.blocks):e?.content?e.content:""}const fo=new WeakMap;function bo(e){let t=fo.get(e);return t||(t={...e,get blocks(){return(0,d.parse)(e.content)}},fo.set(e,t)),t}function vo(e){return e?{...e,title:e?.title?.raw||e?.title||"",content:e?.content?.raw||e?.content||""}:null}const jo=(0,a.createRegistrySelector)((e=>(t,o)=>!!e(zt.store).get(H.H,o))),ko=(0,a.createRegistrySelector)((e=>()=>{const t=e(H.H).getEmailPostId(),o=e(H.H).getEmailPostType();return!!e(re.store).hasEditsForEntityRecord("postType",o,t)})),So=(0,a.createRegistrySelector)((e=>()=>{const t=e(H.H).getEmailPostId(),o=e(H.H).getEmailPostType(),n=e(re.store).getEntityRecord("postType",o,t);if(!n)return!0;const{content:r}=n;return!r.raw})),Co=(0,a.createRegistrySelector)((e=>()=>{const t=e(H.H).getEmailPostId(),o=e(H.H).getEmailPostType(),n=e(re.store).getEntityRecord("postType",o,t);return!!n&&"sent"===n.status})),Eo=(0,a.createRegistrySelector)((e=>()=>{const t=e(H.H).getEmailPostId(),o=e(H.H).getEmailPostType(),n=e(re.store).getEditedEntityRecord("postType",o,t);return n?xo(n):""})),To=(0,a.createRegistrySelector)((e=>()=>{const t=e(H.H).getEmailPostType();return e(re.store).getEntityRecords("postType",t,{per_page:30,status:"publish,sent"})?.filter((e=>""!==e?.content?.raw))||[]})),Po=(0,a.createRegistrySelector)((e=>{const t=e(H.H).getEmailPostType();return(0,a.createSelector)((()=>t?e(re.store).getBlockPatterns().filter((({templateTypes:e,postTypes:o})=>Array.isArray(e)&&e.includes("email-template")&&(void 0===o||0===o.length||o.includes(t)))).map(bo):[]),(()=>[e(re.store).getBlockPatterns(),t]))})),No=(0,a.createRegistrySelector)((e=>()=>e(re.store).canUser("create",{kind:"postType",name:"wp_template"})));function Bo(e,t){return No()?e(re.store).getEditedEntityRecord("postType","wp_template",t):vo(e(re.store).getEntityRecord("postType","wp_template",t,{context:"view"}))}const Mo=(0,a.createRegistrySelector)((e=>(t,o)=>{const n=o||e(A.store).getEditedPostAttribute("template");if(n){const t={context:"view",per_page:-1,_woocommerce_email_editor:"fetch-all-templates"},o=e(re.store).getEntityRecords("postType","wp_template",t)?.find((e=>e.slug===n));return o?Bo(e,o.id):null}const r=e(re.store).getDefaultTemplateId({slug:"email-general"});return Bo(e,r)})),Io=(0,a.createRegistrySelector)((e=>()=>{if("wp_template"===e(A.store).getCurrentPostType()){const t=e(A.store).getCurrentPostId();return e(re.store).getEditedEntityRecord("postType","wp_template",t)}return e(H.H).getEditedPostTemplate()})),Ho=()=>{const e=Io();return e?xo(e):""},Fo=(0,a.createRegistrySelector)((e=>()=>{const t=e(H.H).getGlobalStylesPostId();return{postId:t,canEdit:e(re.store).canUser("update",{kind:"root",name:"globalStyles",id:t})}})),zo=(0,a.createRegistrySelector)((e=>()=>{const{postId:t,canEdit:o}=Fo();return t&&void 0!==o&&t?o?e(re.store).getEditedEntityRecord("postType","wp_global_styles",t):vo(e(re.store).getEntityRecord("postType","wp_global_styles",t,{context:"view"})):null})),Ro=(0,a.createRegistrySelector)((e=>()=>{const t=e(H.H).getEmailPostType();return e(re.store).getEntityRecords("postType","wp_template",{per_page:-1,post_type:t,context:"view"})?.filter((e=>e.post_types.includes(t)))}));function Ao(e){return e.postId}function Lo(e){return e.postType}function Oo(e){return e.editorSettings}function Vo(e){return e.editorSettings.__experimentalFeatures.color.palette}function Do(e){return e.preview}function Go(e){return e.personalizationTags}const $o=(0,a.createRegistrySelector)((e=>t=>{const o=t.personalizationTags.list,n=e(H.H).getEmailPostType();if(!n)return o;if("wp_template"===n){const t=e(H.H).getCurrentTemplate();return o.filter((e=>void 0===e.postTypes||0===e.postTypes.length||Array.isArray(t.post_types)&&t.post_types.some((t=>e.postTypes.includes(t)))))}return o.filter((e=>void 0===e.postTypes||0===e.postTypes.length||e.postTypes.includes(n)))}));function Wo(e){return e.theme.styles}function Uo(e){return e.theme}function qo(e){return e.styles.globalStylesPostId}function Zo(e){return e.urls}function Jo(e){return e.contentValidation}function*Yo(){const e=yield(0,a.select)(H.H),t=e.personalizationTags?.isFetching;if(!t){yield _o(!0);try{const e=yield(0,ao.apiFetch)({path:"/woocommerce-email-editor/v1/get_personalization_tags",method:"GET"});yield go(e.result)}finally{yield _o(!1)}}}const Ko=()=>{const e=(0,a.createReduxStore)(H.H,{actions:n,controls:ao.controls,selectors:r,resolvers:s,reducer:wo,initialState:yo()});return(0,a.register)(e),e},Xo=window.wp.mediaUtils,Qo=()=>{(0,c.addFilter)("editor.MediaUpload","woocommerce/email-editor/replace-media-upload",(()=>Xo.MediaUpload))},en=()=>{const e={"You’ve tried to select a block that is part of a template that may be used elsewhere on your site. Would you like to edit the template?":{domain:"default",replacementText:(0,y.__)("You’ve tried to select a block that is part of a template that may be used in other emails. Would you like to edit the template?","woocommerce")}};(0,c.addFilter)("i18n.gettext","woocommerce/email-editor/override-text",((t,o,n)=>e[o]&&e[o].domain===(n||"default")?e[o].replacementText:t))},tn=e=>{(0,c.doAction)("woocommerce_email_editor_events",e.detail)},on=()=>{T()&&N.addEventListener(P,tn)};window.addEventListener("unload",(function(){T()&&N.removeEventListener(P,tn)}));const nn=(...e)=>{const t=(0,a.select)(A.store).isInserterOpened(),o=!!document.getElementsByClassName("block-editor-inserter__quick-inserter").length;let n="other_inserter";t?n="inserter_sidebar":o&&(n="quick_inserter");const r=e[0],s=e[5];!1===Array.isArray(r)&&"object"==typeof r&&B(`${n}_library_block_selected`,{blockName:r.name}),Array.isArray(r)&&s&&s.patternName&&B(`${n}_library_pattern_selected`,{patternName:s.patternName})},rn={"core/editor":{autosave:"editor_content_auto_saved",setDeviceType:e=>{B(`header_preview_dropdown_${e.toLowerCase()}_selected`)},setRenderingMode:e=>{(0,a.select)(A.store).getRenderingMode()!==e&&document.querySelector(`[aria-label="${(0,y.__)("View options")}"]`)&&B("preview_dropdown_rendering_mode_changed",{renderingMode:e})}},"core/block-editor":{insertBlock:nn,insertBlocks:nn},"core/preferences":{set:(e,t,o)=>{if((0,a.select)(zt.store).get(e,t)===o)return;const n={focusMode:"focus_mode_toggle",fullscreenMode:"full_screen_mode_toggle",distractionFree:"distraction_free_toggle",fixedToolbar:"fixed_toolbar_toggle"};n[t]&&B(n[t],{isEnabled:o})}},"core/commands":{open:"command_menu_opened",close:"command_menu_closed"}},sn={},an={},ln=()=>{T()&&(0,a.use)((e=>({dispatch:t=>{const o="object"==typeof t?t.name:t,n=e.dispatch(o),r=rn[o];if(!r)return n;sn[o]||(sn[o]={}),an[o]||(an[o]={});for(const[e,t]of Object.entries(r))an[o][e]||(an[o][e]=n[e],sn[o][e]=(...n)=>{try{"function"==typeof t?t(...n):"string"==typeof t&&B(t)}catch(e){console.error("Error tracking event",e)}return an[o][e](...n)}),n[e]=sn[o][e];return n}})))};let cn=[];function dn(e){cn.forEach((t=>{const o=e.target?.matches?.(t.selector)?e.target:e.target?.closest?.(t.selector);o&&("function"==typeof t.track?t.track(o,e):B(t.track))}))}const mn=new WeakMap,pn={core:["saveEditedEntityRecord","saveEntityRecord"]},un=()=>{Object.keys(mn).length>0||(0,a.use)((e=>({dispatch:t=>{const o="object"==typeof t?t.name:t;if(!pn[o])return e.dispatch(o);const n=e.dispatch(o);mn[o]||(mn[o]={});const r=pn[o].filter((e=>!mn[o][e]));if(r.length>0)for(const t of r)mn[o][t]=n[t],n[t]=async(...n)=>{const r=e.select(H.H).getContentValidation(),s=r?.validateContent;if(s){let e;try{e=s()}catch(t){e=!1}if(!e)return Promise.reject(new Error((0,y.__)("Content validation failed.","woocommerce")))}return await mn[o][t](...n)};return n}})))},_n=window.wp.isShallowEqual;var gn=o.n(_n);function hn(e){const t=(0,l.useRef)(e);return gn()(e,t.current)||(t.current=e),t.current}const yn=[],wn=()=>{const{addValidationNotice:e,hasValidationNotice:t,removeValidationNotice:o}=Zt(),{editedContent:n,editedTemplateContent:r}=(0,a.useSelect)((e=>({editedContent:e(H.H).getEditedEmailContent(),editedTemplateContent:e(H.H).getCurrentTemplateContent()}))),s=hn(n),i=hn(r),d=(0,l.useCallback)((()=>((e,t,{addValidationNotice:o,hasValidationNotice:n,removeValidationNotice:r})=>{const s=(0,c.applyFilters)("woocommerce_email_editor_content_validation_rules",yn);let i=!0;return s.forEach((({id:s,testContent:a,message:l,actions:c})=>{a(e+t)?(o(s,l,c),i=!1):n(s)&&r(s)})),i})(s,i,{addValidationNotice:e,hasValidationNotice:t,removeValidationNotice:o})),[s,i,e,o,t]);return(0,l.useEffect)((()=>((0,a.dispatch)(H.H).setContentValidation({validateContent:d}),()=>{(0,a.dispatch)(H.H).setContentValidation(void 0)})),[d]),(0,l.useEffect)((()=>{const e=(0,a.subscribe)((()=>{t()&&d()}),re.store);return()=>e()}),[t,d]),{validateContent:d}},xn=()=>{const e=(0,l.useMemo)((()=>{const e=(0,y.__)("Saving failed.");return new RegExp("^"+e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))}),[]);(0,l.useEffect)((()=>{const t=(0,a.subscribe)((()=>{(0,a.select)(se.store).getNotices().forEach((t=>{"string"==typeof t.content&&e.test(t.content)&&(0,a.dispatch)(se.store).removeNotice(t.id)}))}));return()=>{t()}}),[e])},fn=()=>{const e=(0,l.useRef)(null),[t,o]=(0,l.useState)(0),n=(0,l.useCallback)((t=>{e.current=t,o((e=>++e))}),[e,o]),r=(0,a.useSelect)((e=>{var t;const{getEditorSettings:o}=e(A.store);return null!==(t=o()?.allowedIframeStyleHandles)&&void 0!==t?t:[]}));return(0,l.useEffect)((()=>{if(!e.current)return;const{ownerDocument:t}=e.current;Array.from(document.styleSheets).filter((e=>{if(!(e?.ownerNode instanceof Element))return!1;const t=e.ownerNode.getAttribute("id"),o=t&&!r.includes(t);return(0,c.applyFilters)("woocommerce_email_editor_iframe_stylesheet_should_remove",o,e)})).map((e=>e.ownerNode.getAttribute("id"))).forEach((e=>{const o=t.getElementById(e);o&&o.remove();const n=t.createElement("style");n.id=e,t.head.appendChild(n)}))}),[r,t]),n};function bn({postId:e,postType:t,isPreview:o=!1}){const[n,r]=(0,l.useState)(!1),{settings:s}=(0,a.useSelect)((e=>({settings:e(H.H).getInitialEditorSettings()})),[]);wn(),xn();const{setEmailPost:c}=(0,a.useDispatch)(H.H);(0,l.useEffect)((()=>{c(e,t),r(!0)}),[e,t,c]);const d=fn();if(!n)return null;const p={...s,allowedBlockTypes:m(),isPreviewMode:o};return(0,i.jsx)(l.StrictMode,{children:(0,i.jsx)(io,{postId:e,postType:t,settings:p,contentRef:d})})}function vn(e){const t=document.getElementById(e);if(!t)return;const{current_post_id:o,current_post_type:n}=window.WooCommerceEmailEditor;if(null==o)throw new Error("current_post_id is required but not provided.");if(!n)throw new Error("current_post_type is required but not provided.");const r=(0,c.applyFilters)("woocommerce_email_editor_wrap_editor_component",bn);on(),ln(),T()&&(cn=[{track:"header_preview_dropdown_preview_in_new_tab_selected",selector:".editor-preview-dropdown__button-external"},{track:()=>{const e=document.getElementsByClassName("is-collapsed editor-collapsible-block-toolbar").length;B("header_blocks_tool_button_clicked",{isBlockToolsCollapsed:e})},selector:".editor-collapsible-block-toolbar__toggle"},{track:e=>{const t=e.classList.contains("is-opened");B("header_more_menu_dropdown_toggle",{isOpened:t})},selector:`.components-dropdown-menu__toggle[aria-label="${(0,y.__)("Options")}"]`},{track:e=>{(e.textContent===(0,y.__)("Save")&&"false"===e.getAttribute("aria-disabled")||e.textContent===(0,y.__)("Saving…"))&&B("header_save_button_clicked")},selector:".editor-post-publish-button"},{track:"header_save_email_button_clicked",selector:".editor-post-saved-state.is-saving"},{track:"inserter_sidebar_library_close_icon_clicked",selector:".block-editor-inserter__menu .block-editor-tabbed-sidebar__close-button"},{track:e=>{const t=e.classList.contains("is-opened");B("header_preview_dropdown_clicked",{isOpened:t})},selector:".editor-preview-dropdown__toggle"},{track:()=>{B("sidebar_tab_selected",{tab:"document"})},selector:'[data-tab-id="edit-post/document"]'},{track:()=>{B("sidebar_tab_selected",{tab:"block"})},selector:'[data-tab-id="edit-post/block"]'},{track:e=>{const t=e.classList.contains("is-pressed");B("header_inserter_sidebar_clicked",{isOpened:t})},selector:".editor-document-tools__inserter-toggle"},{track:e=>{const t=e.classList.contains("is-pressed");B("header_listview_sidebar_clicked",{isOpened:t})},selector:".editor-document-tools__document-overview-toggle"},{track:e=>{B("command_bar_command_clicked",{command:e.dataset?.value})},selector:'.commands-command-menu__container [role="option"]'}],document.addEventListener("click",dn)),Ko(),un(),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/layout/addAttribute",be),(0,c.addFilter)("editor.BlockListBlock","woocommerce-email-editor/with-layout-styles",ke),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-inspector-controls",ve),pe(),Qo(),en(),(0,l.createRoot)(t).render((0,i.jsx)(r,{postId:o,postType:n}))}function jn(e){"loading"===document.readyState?window.addEventListener("DOMContentLoaded",(()=>{vn(e)}),{once:!0}):vn(e)}},58039:(e,t,o)=>{o.d(t,{H:()=>n});const n="email-editor/editor"},76597:e=>{var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===o}(e)}(e)},o="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?a((o=e,Array.isArray(o)?[]:{}),e,t):e;var o}function r(e,t,o){return e.concat(t).map((function(e){return n(e,o)}))}function s(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function i(e,t){try{return t in e}catch(e){return!1}}function a(e,o,l){(l=l||{}).arrayMerge=l.arrayMerge||r,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=n;var c=Array.isArray(o);return c===Array.isArray(e)?c?l.arrayMerge(e,o,l):function(e,t,o){var r={};return o.isMergeableObject(e)&&s(e).forEach((function(t){r[t]=n(e[t],o)})),s(t).forEach((function(s){(function(e,t){return i(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,s)||(i(e,s)&&o.isMergeableObject(t[s])?r[s]=function(e,t){if(!t.customMerge)return a;var o=t.customMerge(e);return"function"==typeof o?o:a}(s,o)(e[s],t[s],o):r[s]=n(t[s],o))})),r}(e,o,l):n(o,l)}a.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,o){return a(e,o,t)}),{})};var l=a;e.exports=l},94931:(e,t,o)=>{var n=o(51609),r=Symbol.for("react.element"),s=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,o){var n,s={},c=null,d=null;for(n in void 0!==o&&(c=""+o),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(d=t.ref),t)i.call(t,n)&&!l.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===s[n]&&(s[n]=t[n]);return{$$typeof:r,type:e,key:c,ref:d,props:s,_owner:a.current}}t.Fragment=s,t.jsx=c,t.jsxs=c},39793:(e,t,o)=>{e.exports=o(94931)},51609:e=>{e.exports=window.React},56427:e=>{e.exports=window.wp.components},3582:e=>{e.exports=window.wp.coreData},47143:e=>{e.exports=window.wp.data},86087:e=>{e.exports=window.wp.element},52619:e=>{e.exports=window.wp.hooks},27723:e=>{e.exports=window.wp.i18n},5573:e=>{e.exports=window.wp.primitives},4921:(e,t,o)=>{function n(e){var t,o,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(o=n(e[t]))&&(r&&(r+=" "),r+=o)}else for(o in e)e[o]&&(r&&(r+=" "),r+=o);return r}o.d(t,{A:()=>r});const r=function(){for(var e,t,o=0,r="",s=arguments.length;o<s;o++)(e=arguments[o])&&(t=n(e))&&(r&&(r+=" "),r+=t);return r}}},t={};function o(n){var r=t[n];if(void 0!==r)return r.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,o),s.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n=o(52619),r=o(27723),s=o(32915);const i="woocommerce/email-editor-integration";var a=o(56427),l=o(3582),c=o(86087),d=o(39793);function m({debouncedRecordEvent:e}){const[t,o]=(0,l.useEntityProp)("postType","wp_template","woocommerce_data"),n=(0,c.useRef)(null),s=(0,c.useCallback)((n=>{o({...t,sender_settings:{...t?.sender_settings,from_name:n}}),e("email_from_name_input_updated",{value:n})}),[t,o]),i=(0,c.useCallback)((r=>{o({...t,sender_settings:{...t?.sender_settings,from_address:r}}),n.current&&(n.current.checkValidity(),n.current.reportValidity()),e("email_from_address_input_updated",{value:r})}),[t,o]);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h2",{children:(0,r.__)("Sender Options","woocommerce")}),(0,d.jsx)(a.PanelRow,{children:(0,d.jsx)("p",{children:(0,r.__)("This is how your sender name and email address would appear in outgoing WooCommerce emails.","woocommerce")})}),(0,d.jsx)(a.PanelRow,{children:(0,d.jsx)(a.TextControl,{className:"woocommerce-email-sidebar-template-settings-sender-options-input",label:(0,r.__)("“from” name","woocommerce"),name:"from_name",type:"text",value:t?.sender_settings?.from_name||"",onChange:s})}),(0,d.jsx)(a.PanelRow,{children:(0,d.jsx)(a.TextControl,{ref:n,className:"woocommerce-email-sidebar-template-settings-sender-options-input",label:(0,r.__)("“from” email","woocommerce"),name:"from_email",type:"email",value:t?.sender_settings?.from_address||"",onChange:i,required:!0})})]})}var p=o(47143),u=o(4921),_=o(5573);const g=(0,d.jsx)(_.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,d.jsx)(_.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})}),h=(0,d.jsx)(_.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,d.jsx)(_.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm11.53-1.47-1.06-1.06L11 12.94l-1.47-1.47-1.06 1.06L11 15.06l4.53-4.53Z"})}),y=(0,d.jsx)(_.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,d.jsx)(_.Path,{d:"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM15.5303 8.46967C15.8232 8.76256 15.8232 9.23744 15.5303 9.53033L13.0607 12L15.5303 14.4697C15.8232 14.7626 15.8232 15.2374 15.5303 15.5303C15.2374 15.8232 14.7626 15.8232 14.4697 15.5303L12 13.0607L9.53033 15.5303C9.23744 15.8232 8.76256 15.8232 8.46967 15.5303C8.17678 15.2374 8.17678 14.7626 8.46967 14.4697L10.9393 12L8.46967 9.53033C8.17678 9.23744 8.17678 8.76256 8.46967 8.46967C8.76256 8.17678 9.23744 8.17678 9.53033 8.46967L12 10.9393L14.4697 8.46967C14.7626 8.17678 15.2374 8.17678 15.5303 8.46967Z"})}),w=(0,d.jsx)(_.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,d.jsx)(_.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm9 1V8h-1.5v3.5h-2V13H13Z"})}),x=[{value:"enabled",label:(0,r.__)("Enabled","woocommerce"),icon:h,description:(0,r.__)("Email would be sent if trigger is met","woocommerce")},{value:"disabled",label:(0,r.__)("Inactive","woocommerce"),icon:y,description:(0,r.__)("Email would not be sent","woocommerce")},{value:"manual",label:(0,r.__)("Manually sent","woocommerce"),icon:w,description:(0,r.__)("Email can only be sent manually from the order screen","woocommerce")}];function f({className:e,recordEvent:t}){var o;const[n]=(0,l.useEntityProp)("postType","woo_email","woocommerce_data"),s=n?.is_manual;let i="enabled";s?i="manual":n?.enabled||(i="disabled");const c=null!==(o=x.find((e=>e.value===i)))&&void 0!==o?o:x[1];return(0,d.jsx)(a.PanelRow,{className:e,children:(0,d.jsxs)(a.Flex,{justify:"start",children:[(0,d.jsx)(a.FlexItem,{className:"editor-post-panel__row-label",children:(0,r.__)("Email Status","woocommerce")}),(0,d.jsx)(a.FlexItem,{children:(0,d.jsx)(a.Dropdown,{popoverProps:{placement:"bottom-start",offset:0,shift:!0},renderToggle:({isOpen:e,onToggle:t})=>(0,d.jsx)(a.Button,{variant:"tertiary",className:"editor-post-status__toggle",icon:c.icon,size:"compact",onClick:t,"aria-label":(0,r.sprintf)((0,r.__)("Change status: %s","woocommerce"),c.label),"aria-expanded":e,disabled:s,children:c.label}),renderContent:({onClose:e})=>(0,d.jsxs)("div",{style:{minWidth:230},children:[(0,d.jsxs)(a.Flex,{justify:"space-between",align:"center",style:{padding:"8px 0"},children:[(0,d.jsx)("h2",{className:"block-editor-inspector-popover-header__heading",style:{margin:0},children:(0,r.__)("Status","woocommerce")}),(0,d.jsx)(a.Button,{size:"small",className:"block-editor-inspector-popover-header__action",label:(0,r.__)("Close","woocommerce"),icon:g,onClick:e})]}),(0,d.jsx)(a.RadioControl,{selected:i,options:x.filter((e=>"manual"!==e.value)).map((e=>({label:e.label,value:e.value,description:e.description}))),onChange:o=>{(e=>{const o=(0,p.select)(l.store).getEditedEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id),n=o?.woocommerce_data||{};(0,p.dispatch)(l.store).editEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id,{woocommerce_data:{...n,enabled:e}}),t("email_status_changed",{status:e?"active":"inactive"})})("enabled"===o),e()},disabled:s})]})})})]})})}const b=({RichTextWithButton:e,recordEvent:t,debouncedRecordEvent:o})=>{var n;const[s]=(0,l.useEntityProp)("postType","woo_email","woocommerce_data"),[i,m]=(0,c.useState)(!!s?.bcc),[_,g]=(0,c.useState)(!!s?.cc);if(!s)return null;const h=(e,t)=>{const o=(0,p.select)(l.store).getEditedEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id),n=o?.woocommerce_data||{};(0,p.dispatch)(l.store).editEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id,{woocommerce_data:{...n,[e]:t}})},y=null!==(n=s?.preheader?.length)&&void 0!==n?n:0;return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("br",{}),"customer_refunded_order"===s.email_type?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(e,{attributeName:"subject_full",attributeValue:s.subject_full,updateProperty:h,label:(0,r.__)("Full Refund Subject","woocommerce"),placeholder:s.default_subject}),(0,d.jsx)("br",{}),(0,d.jsx)(e,{attributeName:"subject_partial",attributeValue:s.subject_partial,updateProperty:h,label:(0,r.__)("Partial Refund Subject","woocommerce"),placeholder:s.default_subject})]}):(0,d.jsx)(e,{attributeName:"subject",attributeValue:s.subject,updateProperty:h,label:(0,r.__)("Subject","woocommerce"),placeholder:s.default_subject}),(0,d.jsx)("br",{}),(0,d.jsx)(e,{attributeName:"preheader",attributeValue:s.preheader,updateProperty:h,label:(0,r.__)("Preview text","woocommerce"),help:(0,d.jsxs)("span",{className:(0,u.A)("woocommerce-settings-panel__preview-text-length",{"woocommerce-settings-panel__preview-text-length-warning":y>80,"woocommerce-settings-panel__preview-text-length-error":y>150}),children:[y,"/",150]}),placeholder:(0,r.__)("Shown as a preview in the inbox, next to the subject line.","woocommerce")}),(0,d.jsx)(a.PanelRow,{children:(0,d.jsx)(a.BaseControl,{__nextHasNoMarginBottom:!0,label:(0,r.__)("Recipients","woocommerce"),id:"woocommerce-email-editor-recipients",children:null===s.recipient?(0,d.jsx)("p",{className:"woocommerce-email-editor-recipients-help",children:(0,r.__)("This email is sent to Customer.","woocommerce")}):(0,d.jsx)(a.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,name:"recipient","data-testid":"email_recipient",value:s.recipient,onChange:e=>{h("recipient",e)},help:(0,r.__)("Separate with commas to add multiple email addresses.","woocommerce")})})}),(0,d.jsx)(a.PanelRow,{children:(0,d.jsx)(a.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,d.jsx)(a.ToggleControl,{__nextHasNoMarginBottom:!0,name:"add_cc",checked:_,label:(0,r.__)("Add CC","woocommerce"),onChange:e=>{g(e),e||h("cc",""),t("email_cc_toggle_clicked",{isEnabled:e})}})})}),_&&(0,d.jsx)(a.PanelRow,{children:(0,d.jsx)(a.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,d.jsx)(a.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,"data-testid":"email_cc",value:s?.cc||"",onChange:e=>{h("cc",e),o("email_cc_input_updated",{value:e})},help:(0,r.__)("Add recipients who will receive a copy of the email. Separate multiple addresses with commas.","woocommerce")})})}),(0,d.jsx)(a.PanelRow,{children:(0,d.jsx)(a.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,d.jsx)(a.ToggleControl,{__nextHasNoMarginBottom:!0,name:"add_bcc",checked:i,label:(0,r.__)("Add BCC","woocommerce"),onChange:e=>{m(e),e||h("bcc",""),t("email_bcc_toggle_clicked",{isEnabled:e})}})})}),i&&(0,d.jsx)(a.PanelRow,{children:(0,d.jsx)(a.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,d.jsx)(a.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,"data-testid":"email_bcc",value:s?.bcc||"",onChange:e=>{h("bcc",e),o("email_bcc_input_updated",{value:e})},help:(0,r.__)("Add recipients who will receive a hidden copy of the email. Separate multiple addresses with commas.","woocommerce")})})})]})};function v(){return(0,p.select)("core").getEditedEntityRecord("postType",window.WooCommerceEmailEditor.current_post_type,window.WooCommerceEmailEditor.current_post_id)?.woocommerce_data}function j(e){const t=document.createElement("input");return t.type="email",t.value=e,t.checkValidity()}function k(e){return e.split(",").filter((e=>!!e.trim()&&!j(e.trim())))}function S(e,t){return{id:`${e}-email-validation`,testContent:()=>{const t=v();return!(!(e in t)||!t[e])&&k(t[e]).length>0},get message(){var o;const n=k(null!==(o=v()[e])&&void 0!==o?o:"");return(0,r.sprintf)(t,n.join(","))},actions:[]}}const C={id:"sender-email-validation",testContent:()=>{var e;const t=v(),o=null!==(e=t?.sender_settings?.from_address)&&void 0!==e?e:"";return!!o.trim()&&!j(o.trim())},message:(0,r.__)('The "from" email address is invalid. Please enter a valid email address that will appear as the sender in outgoing WooCommerce emails.',"woocommerce"),actions:[]},E=S("recipient",(0,r.__)("One or more Recipient email addresses are invalid: “%s”. Please enter valid email addresses separated by commas.","woocommerce")),T=S("cc",(0,r.__)("One or more CC email addresses are invalid: “%s”. Please enter valid email addresses separated by commas.","woocommerce")),P=S("bcc",(0,r.__)("One or more BCC email addresses are invalid: “%s”. Please enter valid email addresses separated by commas.","woocommerce"));(0,n.addFilter)("woocommerce_email_editor_send_button_label",i,(()=>(0,r.__)("Save email","woocommerce"))),(0,n.addFilter)("woocommerce_email_editor_check_sending_method_configuration_link",i,(()=>"https://woocommerce.com/document/email-faq/")),(0,n.addFilter)("woocommerce_email_editor_trash_modal_should_permanently_delete",i,(()=>!0)),(0,n.addFilter)("woocommerce_email_editor_setting_sidebar_email_status_component",i,((e,t)=>()=>(0,d.jsx)(f,{recordEvent:t.recordEvent}))),(0,n.addFilter)("woocommerce_email_editor_setting_sidebar_extension_component",i,((e,t)=>()=>(0,d.jsx)(b,{RichTextWithButton:e,recordEvent:t.recordEvent,debouncedRecordEvent:t.debouncedRecordEvent}))),(0,n.addFilter)("woocommerce_email_editor_template_sections","my-plugin/template-settings",((e,t)=>[...e,{id:"my-custom-section",render:()=>(0,d.jsx)(m,{debouncedRecordEvent:t.debouncedRecordEvent})}])),(0,n.addFilter)("woocommerce_email_editor_content_validation_rules",i,(e=>[...e||[],C,E,T,P])),(0,s.initializeEditor)("woocommerce-email-editor"),(window.wc=window.wc||{}).emailEditorIntegration={}})();