import*as t from"@woocommerce/stores/woocommerce/cart";import*as e from"@wordpress/interactivity";import*as r from"@woocommerce/stores/woocommerce/product-data";var o={7908:t=>{t.exports=import("@woocommerce/stores/store-notices")},1401:e=>{e.exports=t},2833:(t,r,o)=>{var n,a;t.exports=(n={getContext:()=>e.getContext,store:()=>e.store},a={},o.d(a,n),a)}},n={};function a(t){var e=n[t];if(void 0!==e)return e.exports;var r=n[t]={exports:{}};return o[t](r,r.exports,a),r.exports}a.d=(t,e)=>{for(var r in e)a.o(e,r)&&!a.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var i={};a.d(i,{O:()=>p});var s=a(2833);a.d({},{});const u="I acknowledge that using a private store means my plugin will inevitably break on the next store release.",{state:c}=(0,s.store)("woocommerce",{},{lock:u}),l=(t,e,r,o)=>{let n,a=t;if("variable"===e){const e=((t,e)=>Array.isArray(t)&&Array.isArray(e)&&0!==t.length&&0!==e.length&&t.find((t=>Object.entries(t.attributes).every((([t,r])=>e.some((e=>!(e.attribute!==t)&&(e.value===r||e.value&&""===r)))))))||null)(r,o);e?.variation_id&&(a=e.variation_id,n=c?.products?.[t]?.variations?.[e?.variation_id])}else n=c?.products?.[a];if("object"!=typeof n||null===n)return null;const i="number"==typeof n.min?n.min:1,s="number"==typeof n.max&&n.max>=1?n.max:1/0,u=n.step||1;return{id:a,...n,min:i,max:s,step:u}},d=t=>{const e=(t=>{let e=null;return t.target instanceof HTMLButtonElement&&(e=t.target.parentElement?.querySelector(".qty")),t.target instanceof HTMLInputElement&&(e=t.target),e})(t);if(!e)return;const r=parseInt(e.value,10);return{currentValue:isNaN(r)?0:r,inputElement:e}},p=(t,e,r)=>{const o=c.cart?.items.find((e=>{return"variation"===e.type?!(e.id!==t||!e.variation||!r||e.variation.length!==r.length)&&(o=e,n=r,!(!Array.isArray(o.variation)||!Array.isArray(n))&&o.variation.length===n.length&&o.variation.every((({raw_attribute:t,value:e})=>n.some((r=>r.attribute===t&&(r.value.toLowerCase()===e.toLowerCase()||r.value&&""===e)))))):e.id===t;var o,n}));return(o?.quantity||0)+e},m=t=>{const e=new Event("change",{bubbles:!0});t.dispatchEvent(e)},{actions:y,state:v}=(0,s.store)("woocommerce/add-to-cart-with-options",{state:{noticeIds:[],get validationErrors(){const t=(0,s.getContext)();return t&&t.validationErrors?t.validationErrors:[]},get isFormValid(){const t=(0,s.getContext)();if(!t)return!0;const{productType:e,quantity:r}=t;return"grouped"===e?Object.values(r).some((t=>t>0)):0===v.validationErrors.length},get allowsDecrease(){const{quantity:t,childProductId:e,productType:r,productId:o,availableVariations:n,selectedAttributes:a}=(0,s.getContext)();if("grouped"===r&&t[e]>0)return!0;const i=l(e||o,r,n,a);if(!i)return!0;const{id:u,min:c,step:d}=i;return"number"!=typeof d||"number"!=typeof c||(t[u]||0)-d>=c},get allowsIncrease(){const{quantity:t,childProductId:e,productType:r,productId:o,availableVariations:n,selectedAttributes:a}=(0,s.getContext)(),i=l(e||o,r,n,a);if(!i)return!0;const{id:u,max:c,step:d}=i;return"number"!=typeof d||"number"!=typeof c||(t[u]||0)+d<=c}},actions:{setQuantity(t){const e=(0,s.getContext)();if("variable"===e.productType)e.availableVariations.map((t=>t.variation_id)).forEach((r=>{e.quantity[r]=t}));else{const r=e.childProductId||e.productId;e.quantity={...e.quantity,[r]:t}}},addError:t=>{const{validationErrors:e}=v;return e.push(t),t.code},clearErrors:t=>{const{validationErrors:e}=v;if(t){const r=e.filter((e=>e.group!==t));e.splice(0,e.length,...r)}else e.length=0},increaseQuantity:t=>{const e=d(t);if(!e)return;const{currentValue:r,inputElement:o}=e,{childProductId:n,productType:a,productId:i,availableVariations:u,selectedAttributes:c}=(0,s.getContext)(),p=l(n||i,a,u,c);if(!p)return;const{max:v,min:f,step:g}=p;if("number"!=typeof g||"number"!=typeof f||"number"!=typeof v)return;const b=r+g;if(b<=v){const t=Math.max(f,b);y.setQuantity(t),o.value=t.toString(),m(o)}},decreaseQuantity:t=>{const e=d(t);if(!e)return;const{currentValue:r,inputElement:o}=e,{childProductId:n,productType:a,productId:i,availableVariations:u,selectedAttributes:c}=(0,s.getContext)(),p=l(n||i,a,u,c);if(!p)return;const{min:v,max:f,step:g}=p;if("number"!=typeof g||"number"!=typeof v||"number"!=typeof f)return;let b=r-g;"grouped"===a&&b<v&&(b=r>v?v:0),b!==r&&(y.setQuantity(b),o.value=b.toString(),m(o))},handleQuantityInput:t=>{const e=d(t);if(!e)return;const{currentValue:r}=e;y.setQuantity(r)},handleQuantityChange:t=>{const e=d(t);if(!e)return;const{childProductId:r}=(0,s.getContext)(),{currentValue:o}=e,{productType:n,productId:a,availableVariations:i,selectedAttributes:u}=(0,s.getContext)(),c=l(r||a,n,i,u);if(!c)return;const{min:p,max:v,step:f}=c;if("number"!=typeof f||"number"!=typeof p||"number"!=typeof v)return;let g=Math.min(null!=v?v:1/0,Math.max(p,o));"grouped"===n&&o<p&&(g=0),t.target.value!==g.toString()&&(y.setQuantity(g),t.target.value=g.toString(),m(t.target))},handleQuantityCheckboxChange:t=>{const e=d(t);if(!e)return;const{inputElement:r}=e;y.setQuantity(r.checked?1:0)},*addToCart(){yield Promise.resolve().then(a.bind(a,1401));const{productId:t,quantity:e,selectedAttributes:r,productType:o}=(0,s.getContext)(),{variationId:n}=v,i=n||t,c=p(i,e[i],r),{actions:l}=(0,s.store)("woocommerce",{},{lock:u});yield l.addCartItem({id:i,quantity:c,variation:r,type:o})},*handleSubmit(t){t.preventDefault();const{isFormValid:e}=v;if(e)yield y.addToCart();else{yield Promise.resolve().then(a.bind(a,7908));const{actions:t}=(0,s.store)("woocommerce/store-notices",{},{lock:u}),{noticeIds:e,validationErrors:r}=v;e.forEach((e=>{t.removeNotice(e)})),e.splice(0,e.length);const o=r.map((e=>t.addNotice({notice:e.message,type:"error",dismissible:!0})));e.push(...o)}}}},{lock:u});var f=i.O;export{f as getNewQuantity};