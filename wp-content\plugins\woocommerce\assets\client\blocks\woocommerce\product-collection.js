import*as e from"@wordpress/interactivity";var t={1301:()=>{},438:e=>{e.exports=import("@wordpress/interactivity-router")}},o={};function r(e){var i=o[e];if(void 0!==i)return i.exports;var n=o[e]={exports:{}};return t[e](n,n.exports,r),n.exports}r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const i=(l={getContext:()=>e.getContext,getElement:()=>e.getElement,store:()=>e.store},s={},r.d(s,l),s),n=(e,{bubbles:t=!1,cancelable:o=!1,element:r,detail:i={}})=>{if(!CustomEvent)return;r||(r=document.body);const n=new CustomEvent(e,{bubbles:t,cancelable:o,detail:i});r.dispatchEvent(n)},c=e=>{n("wc-blocks_product_list_rendered",{bubbles:!0,cancelable:!0,detail:e})};var l,s;function d(){return"rtl"===document.documentElement?.dir}function a(e){return null!==e&&e instanceof window.HTMLAnchorElement&&!!e.href&&(!e.target||"_self"===e.target)&&e.origin===window.location.origin}r(1301);const u=(e,t)=>{if(!e)return{isDisabledPrevious:!0,isDisabledNext:!0};const{scrollWidth:o,clientWidth:r}=e;return d()?{isDisabledPrevious:t>-5,isDisabledNext:t<=r-o+5}:{isDisabledPrevious:t<5,isDisabledNext:t>=o-r-5}},b=e=>{const{ref:t}=(0,i.getElement)(),o=t?.closest(".wp-block-woocommerce-product-collection"),r=o?.querySelector(".wc-block-product-template");if(!r)return;const n=o?.clientWidth,c=n?.9*n:400,l=d()?-1:1;r?.scrollBy({left:l*("left"===e?-c:c),behavior:"smooth"});const s=(0,i.getContext)(),{scrollLeft:a}=r,b="left"===e?a-l*c:a+l*c,{isDisabledPrevious:f,isDisabledNext:v}=u(r,b);s.isDisabledPrevious=f,s.isDisabledNext=v},f=e=>{"ArrowRight"===e.code&&(e.preventDefault(),b("right")),"ArrowLeft"===e.code&&(e.preventDefault(),b("left"))};(0,i.store)("woocommerce/product-collection",{actions:{*navigate(e){const{ref:t}=(0,i.getElement)();if(a(t)&&function(e){return!(0!==e.button||e.metaKey||e.ctrlKey||e.altKey||e.shiftKey||e.defaultPrevented)}(e)){e.preventDefault();const o=(0,i.getContext)(),n=t.closest("[data-wp-router-region]")?.getAttribute("data-wp-router-region"),{actions:l}=yield Promise.resolve().then(r.bind(r,438));yield l.navigate(t.href),o.isPrefetchNextOrPreviousLink=t.href;const s=document.querySelector(`[data-wp-router-region=${n}] .wc-block-product-template .wc-block-product a`);s?.focus(),c({collection:o.collection})}},*prefetchOnHover(){const{ref:e}=(0,i.getElement)();if(a(e)){const{actions:t}=yield Promise.resolve().then(r.bind(r,438));yield t.prefetch(e.href)}},*viewProduct(){const{collection:e,productId:t}=(0,i.getContext)();t&&n("wc-blocks_viewed_product",{bubbles:!0,cancelable:!0,detail:{collection:e,productId:t}})},onClickPrevious:()=>{b("left")},onClickNext:()=>{b("right")},onKeyDownPrevious:e=>{f(e)},onKeyDownNext:e=>{f(e)},watchScroll:()=>{const e=(0,i.getContext)(),{ref:t}=(0,i.getElement)();if(t){const{isDisabledPrevious:o,isDisabledNext:r}=u(t,t.scrollLeft);e.isDisabledPrevious=o,e.isDisabledNext=r}}},callbacks:{*prefetch(){const{ref:e}=(0,i.getElement)(),t=(0,i.getContext)();if(a(e)&&t.isPrefetchNextOrPreviousLink){const{actions:t}=yield Promise.resolve().then(r.bind(r,438));yield t.prefetch(e.href)}},*onRender(){const{collection:e}=(0,i.getContext)();c({collection:e})},initResizeObserver:()=>{const e=(0,i.getElement)()?.ref;if(!e)return;const t=(0,i.getContext)();new ResizeObserver((()=>{const o=e.scrollWidth>e.clientWidth;t.hideNextPreviousButtons=!o})).observe(e)}}},{lock:!0});