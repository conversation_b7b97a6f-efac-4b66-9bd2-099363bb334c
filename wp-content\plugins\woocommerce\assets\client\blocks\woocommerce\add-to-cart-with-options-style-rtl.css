@keyframes wc-skeleton-shimmer{to{transform:translateX(-100%)}}:where(.wc-block-add-to-cart-with-options) :where(.quantity){align-items:stretch;display:inline-flex}:where(.wc-block-add-to-cart-with-options) :where(.quantity .input-text){font-size:var(--wp--preset--font-size--small);padding:.9rem 1.1rem}:where(.wc-block-add-to-cart-with-options-grouped-product-item-selector) .wc-block-components-quantity-selector.wc-block-components-quantity-selector,:where(.wc-block-add-to-cart-with-options__quantity-selector) .wc-block-components-quantity-selector.wc-block-components-quantity-selector{display:inline-flex;margin-bottom:0;margin-left:0;width:unset}:where(.wc-block-add-to-cart-with-options-grouped-product-item-selector) :where(.wc-block-components-quantity-selector):after,:where(.wc-block-add-to-cart-with-options__quantity-selector) :where(.wc-block-components-quantity-selector):after{border:1px solid;opacity:.3}:where(.wc-block-add-to-cart-with-options-grouped-product-item-selector) :where(.wc-block-components-quantity-selector) :where(.input-text),:where(.wc-block-add-to-cart-with-options__quantity-selector) :where(.wc-block-components-quantity-selector) :where(.input-text){font-size:inherit}:where(.wc-block-add-to-cart-with-options-grouped-product-item-selector) :where(.wc-block-components-quantity-selector) :where(input[type=number].qty),:where(.wc-block-add-to-cart-with-options__quantity-selector) :where(.wc-block-components-quantity-selector) :where(input[type=number].qty){-moz-appearance:textfield;background-color:transparent;border:unset;box-sizing:content-box;color:currentColor;font-size:.8em;font-weight:600;margin:0;margin-left:unset;max-width:3.631em;order:2;padding:.7rem 0;text-align:center}:where(.wc-block-add-to-cart-with-options-grouped-product-item-selector) :where(.wc-block-components-quantity-selector) :where(input[type=number].qty):focus,:where(.wc-block-add-to-cart-with-options__quantity-selector) :where(.wc-block-components-quantity-selector) :where(input[type=number].qty):focus{border-radius:unset}:where(.wc-block-add-to-cart-with-options-grouped-product-item-selector) :where(.wc-block-components-quantity-selector) :where(input[type=number]::-webkit-inner-spin-button),:where(.wc-block-add-to-cart-with-options-grouped-product-item-selector) :where(.wc-block-components-quantity-selector) :where(input[type=number]::-webkit-outer-spin-button),:where(.wc-block-add-to-cart-with-options__quantity-selector) :where(.wc-block-components-quantity-selector) :where(input[type=number]::-webkit-inner-spin-button),:where(.wc-block-add-to-cart-with-options__quantity-selector) :where(.wc-block-components-quantity-selector) :where(input[type=number]::-webkit-outer-spin-button){-webkit-appearance:none;margin:0}:where(.wc-block-add-to-cart-with-options.is-invalid) .wp-block-woocommerce-product-button .wc-block-components-product-button__button{cursor:not-allowed;opacity:.5}
